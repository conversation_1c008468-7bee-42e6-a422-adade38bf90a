{"name": "tucsenberg-web-frontier", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"react": "19.1.0", "react-dom": "19.1.0", "next": "15.4.4"}, "devDependencies": {"typescript": "5.8.3", "@types/node": "20.19.9", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@tailwindcss/postcss": "4.1.11", "tailwindcss": "4.1.11", "eslint": "9.32.0", "eslint-config-next": "15.4.4", "@eslint/eslintrc": "3.3.1"}}