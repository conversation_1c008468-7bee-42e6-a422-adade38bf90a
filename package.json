{"name": "tucsenberg-web-frontier", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@sentry/nextjs": "8.46.0", "next": "15.4.4", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "3.3.1", "@sentry/cli": "2.42.0", "@tailwindcss/postcss": "4.1.11", "@types/node": "20.19.9", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "eslint": "9.32.0", "eslint-config-next": "15.4.4", "tailwindcss": "4.1.11", "typescript": "5.8.3"}}