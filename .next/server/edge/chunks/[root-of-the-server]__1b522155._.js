(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__1b522155._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[project]/sentry.edge.config.ts [instrumentation-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// This file configures the initialization of Sentry for edge runtime
// The config you add here will be used whenever a page or API route is run in edge runtime
// https://docs.sentry.io/platforms/javascript/guides/nextjs/
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$sentry$2b$nextjs$40$8$2e$46$2e$0_$40$opentelemetry$2b$core$40$1$2e$30$2e$1_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_$5f40$opentelemet_783e65edf93b841e38bfe79c9654f4a8$2f$node_modules$2f40$sentry$2f$nextjs$2f$build$2f$esm$2f$edge$2f$index$2e$js__$5b$instrumentation$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@sentry+nextjs@8.46.0_@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0__@opentelemet_783e65edf93b841e38bfe79c9654f4a8/node_modules/@sentry/nextjs/build/esm/edge/index.js [instrumentation-edge] (ecmascript) <locals>");
;
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$sentry$2b$nextjs$40$8$2e$46$2e$0_$40$opentelemetry$2b$core$40$1$2e$30$2e$1_$40$opentelemetry$2b$api$40$1$2e$9$2e$0_$5f40$opentelemet_783e65edf93b841e38bfe79c9654f4a8$2f$node_modules$2f40$sentry$2f$nextjs$2f$build$2f$esm$2f$edge$2f$index$2e$js__$5b$instrumentation$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__["init"]({
    dsn: process.env.SENTRY_DSN,
    // Adjust this value in production, or use tracesSampler for greater control
    tracesSampleRate: 1,
    // Setting this option to true will print useful information to the console while you're setting up Sentry.
    debug: false,
    // Set user context for edge runtime
    initialScope: {
        tags: {
            component: "edge"
        }
    },
    // Environment configuration
    environment: ("TURBOPACK compile-time value", "development") || 'development',
    // Release tracking
    release: process.env.VERCEL_GIT_COMMIT_SHA || 'development',
    // Edge runtime error filtering
    beforeSend (event) {
        // Filter out certain edge runtime errors
        if ("TURBOPACK compile-time truthy", 1) {
            // Don't send certain errors in development
            if (event.exception) {
                const error = event.exception.values?.[0];
                if (error?.type === 'TypeError' && error?.value?.includes('fetch')) {
                    return null;
                }
            }
        }
        return event;
    },
    // Custom edge tags
    beforeSendTransaction (event) {
        // Add custom tags to edge transactions
        event.tags = {
            ...event.tags,
            section: 'edge',
            runtime: 'edge'
        };
        return event;
    },
    // Configure sampling for edge runtime
    tracesSampler: (samplingContext)=>{
        // Sample middleware at a lower rate
        if (samplingContext.name === 'middleware') {
            return 0.1;
        }
        // Sample API routes at a higher rate
        if (samplingContext.request?.url?.includes('/api/')) {
            return 1.0;
        }
        // Default sampling
        return ("TURBOPACK compile-time falsy", 0) ? "TURBOPACK unreachable" : 1.0;
    }
});
}),
"[project]/instrumentation.ts [instrumentation-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "register": ()=>register
});
async function register() {
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    if ("TURBOPACK compile-time truthy", 1) {
        await Promise.resolve().then(()=>__turbopack_context__.i("[project]/sentry.edge.config.ts [instrumentation-edge] (ecmascript)"));
    }
}
}),
"[project]/edge-wrapper.js { MODULE => \"[project]/instrumentation.ts [instrumentation-edge] (ecmascript)\" } [instrumentation-edge] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
self._ENTRIES ||= {};
const modProm = Promise.resolve().then(()=>__turbopack_context__.i("[project]/instrumentation.ts [instrumentation-edge] (ecmascript)"));
modProm.catch(()=>{});
self._ENTRIES["middleware_instrumentation"] = new Proxy(modProm, {
    get (modProm, name) {
        if (name === "then") {
            return (res, rej)=>modProm.then(res, rej);
        }
        let result = (...args)=>modProm.then((mod)=>(0, mod[name])(...args));
        result.then = (res, rej)=>modProm.then((mod)=>mod[name]).then(res, rej);
        return result;
    }
});
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__1b522155._.js.map