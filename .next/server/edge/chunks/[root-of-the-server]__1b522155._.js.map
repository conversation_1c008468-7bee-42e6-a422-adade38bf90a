{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/sentry.edge.config.ts"], "sourcesContent": ["// This file configures the initialization of Sentry for edge runtime\n// The config you add here will be used whenever a page or API route is run in edge runtime\n// https://docs.sentry.io/platforms/javascript/guides/nextjs/\n\nimport * as Sentry from \"@sentry/nextjs\";\n\nSentry.init({\n  dsn: process.env.SENTRY_DSN,\n\n  // Adjust this value in production, or use tracesSampler for greater control\n  tracesSampleRate: 1,\n\n  // Setting this option to true will print useful information to the console while you're setting up Sentry.\n  debug: false,\n\n  // Set user context for edge runtime\n  initialScope: {\n    tags: {\n      component: \"edge\"\n    },\n  },\n\n  // Environment configuration\n  environment: process.env.NODE_ENV || 'development',\n\n  // Release tracking\n  release: process.env.VERCEL_GIT_COMMIT_SHA || 'development',\n\n  // Edge runtime error filtering\n  beforeSend(event) {\n    // Filter out certain edge runtime errors\n    if (process.env.NODE_ENV === 'development') {\n      // Don't send certain errors in development\n      if (event.exception) {\n        const error = event.exception.values?.[0];\n        if (error?.type === 'TypeError' && \n            error?.value?.includes('fetch')) {\n          return null;\n        }\n      }\n    }\n    return event;\n  },\n\n  // Custom edge tags\n  beforeSendTransaction(event) {\n    // Add custom tags to edge transactions\n    event.tags = {\n      ...event.tags,\n      section: 'edge',\n      runtime: 'edge',\n    };\n    return event;\n  },\n\n  // Configure sampling for edge runtime\n  tracesSampler: (samplingContext) => {\n    // Sample middleware at a lower rate\n    if (samplingContext.name === 'middleware') {\n      return 0.1;\n    }\n    \n    // Sample API routes at a higher rate\n    if (samplingContext.request?.url?.includes('/api/')) {\n      return 1.0;\n    }\n    \n    // Default sampling\n    return process.env.NODE_ENV === 'production' ? 0.1 : 1.0;\n  },\n});\n"], "names": [], "mappings": "AAAA,qEAAqE;AACrE,2FAA2F;AAC3F,6DAA6D;;AAE7D;;AAEA,qYAAA,CAAA,OAAW,CAAC;IACV,KAAK,QAAQ,GAAG,CAAC,UAAU;IAE3B,4EAA4E;IAC5E,kBAAkB;IAElB,2GAA2G;IAC3G,OAAO;IAEP,oCAAoC;IACpC,cAAc;QACZ,MAAM;YACJ,WAAW;QACb;IACF;IAEA,4BAA4B;IAC5B,aAAa,mDAAwB;IAErC,mBAAmB;IACnB,SAAS,QAAQ,GAAG,CAAC,qBAAqB,IAAI;IAE9C,+BAA+B;IAC/B,YAAW,KAAK;QACd,yCAAyC;QACzC,wCAA4C;YAC1C,2CAA2C;YAC3C,IAAI,MAAM,SAAS,EAAE;gBACnB,MAAM,QAAQ,MAAM,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;gBACzC,IAAI,OAAO,SAAS,eAChB,OAAO,OAAO,SAAS,UAAU;oBACnC,OAAO;gBACT;YACF;QACF;QACA,OAAO;IACT;IAEA,mBAAmB;IACnB,uBAAsB,KAAK;QACzB,uCAAuC;QACvC,MAAM,IAAI,GAAG;YACX,GAAG,MAAM,IAAI;YACb,SAAS;YACT,SAAS;QACX;QACA,OAAO;IACT;IAEA,sCAAsC;IACtC,eAAe,CAAC;QACd,oCAAoC;QACpC,IAAI,gBAAgB,IAAI,KAAK,cAAc;YACzC,OAAO;QACT;QAEA,qCAAqC;QACrC,IAAI,gBAAgB,OAAO,EAAE,KAAK,SAAS,UAAU;YACnD,OAAO;QACT;QAEA,mBAAmB;QACnB,OAAO,sCAAwC,0BAAM;IACvD;AACF"}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/instrumentation.ts"], "sourcesContent": ["export async function register() {\n  if (process.env.NEXT_RUNTIME === 'nodejs') {\n    await import('./sentry.server.config');\n  }\n\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    await import('./sentry.edge.config');\n  }\n}\n"], "names": [], "mappings": ";;;AAAO,eAAe;IACpB;;IAIA,wCAAyC;QACvC;IACF;AACF"}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/edge-wrapper.js"], "sourcesContent": ["self._ENTRIES ||= {};\nconst modProm = import('MODULE');\nmodProm.catch(() => {});\nself._ENTRIES[\"middleware_instrumentation\"] = new Proxy(modProm, {\n    get(modProm, name) {\n        if (name === \"then\") {\n            return (res, rej) => modProm.then(res, rej);\n        }\n        let result = (...args) => modProm.then((mod) => (0, mod[name])(...args));\n        result.then = (res, rej) => modProm.then((mod) => mod[name]).then(res, rej);\n        return result;\n    },\n});\n"], "names": [], "mappings": "AAAA,KAAK,QAAQ,KAAK,CAAC;AACnB,MAAM;AACN,QAAQ,KAAK,CAAC,KAAO;AACrB,KAAK,QAAQ,CAAC,6BAA6B,GAAG,IAAI,MAAM,SAAS;IAC7D,KAAI,OAAO,EAAE,IAAI;QACb,IAAI,SAAS,QAAQ;YACjB,OAAO,CAAC,KAAK,MAAQ,QAAQ,IAAI,CAAC,KAAK;QAC3C;QACA,IAAI,SAAS,CAAC,GAAG,OAAS,QAAQ,IAAI,CAAC,CAAC,MAAQ,CAAC,GAAG,GAAG,CAAC,KAAK,KAAK;QAClE,OAAO,IAAI,GAAG,CAAC,KAAK,MAAQ,QAAQ,IAAI,CAAC,CAAC,MAAQ,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK;QACvE,OAAO;IACX;AACJ"}}]}