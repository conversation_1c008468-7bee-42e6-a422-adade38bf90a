module.exports = {

"[project]/sentry.server.config.ts [instrumentation] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/[externals]_node:inspector_1230e574._.js",
  "server/chunks/58bca_@sentry_core_build_cjs_ea9cbc40._.js",
  "server/chunks/8e59f_@opentelemetry_api_build_esm_abee2efd._.js",
  "server/chunks/node_modules__pnpm_00b55a9c._.js",
  "server/chunks/ffc21_@opentelemetry_semantic-conventions_build_esm_326d840b._.js",
  "server/chunks/f6faf_@opentelemetry_semantic-conventions_build_esm_01ae72d2._.js",
  "server/chunks/a7dda_@opentelemetry_semantic-conventions_build_esm_trace_e2b927a8._.js",
  "server/chunks/a7dda_@opentelemetry_semantic-conventions_build_esm_resource_8005372b._.js",
  "server/chunks/a7dda_@opentelemetry_semantic-conventions_build_esm_index_7da10e9e.js",
  "server/chunks/a7dda_@opentelemetry_semantic-conventions_build_esm_index_2fce3b8f.js",
  "server/chunks/a7dda_@opentelemetry_semantic-conventions_build_esm_index-incubating_9324654e.js",
  "server/chunks/a7dda_@opentelemetry_semantic-conventions_build_esm_index-incubating_975d26b0.js",
  "server/chunks/a7dda_@opentelemetry_semantic-conventions_build_esm_0b9b473b._.js",
  "server/chunks/node_modules__pnpm_575462f7._.js",
  "server/chunks/2d6f5_@opentelemetry_sdk-trace-base_build_esm_18781278._.js",
  "server/chunks/46f7a_@opentelemetry_resources_build_esm_a7ee80be._.js",
  "server/chunks/5156f_@sentry_node_build_cjs_52a9a52a._.js",
  "server/chunks/571fd_@sentry_nextjs_build_cjs_2b1e2763._.js",
  "server/chunks/node_modules__pnpm_971a5ab9._.js",
  "server/chunks/[root-of-the-server]__582880a6._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/sentry.server.config.ts [instrumentation] (ecmascript)");
    });
});
}),

};