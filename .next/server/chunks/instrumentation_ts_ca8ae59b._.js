module.exports = {

"[project]/instrumentation.ts [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "register": ()=>register
});
async function register() {
    if ("TURBOPACK compile-time truthy", 1) {
        await __turbopack_context__.r("[project]/sentry.server.config.ts [instrumentation] (ecmascript, async loader)")(__turbopack_context__.i);
    }
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
}
}),

};

//# sourceMappingURL=instrumentation_ts_ca8ae59b._.js.map