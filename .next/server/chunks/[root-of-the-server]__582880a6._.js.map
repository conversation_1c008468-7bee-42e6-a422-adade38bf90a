{"version": 3, "sources": [], "sections": [{"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/sentry.server.config.ts"], "sourcesContent": ["// This file configures the initialization of Sentry on the server side\n// The config you add here will be used whenever the server handles a request\n// https://docs.sentry.io/platforms/javascript/guides/nextjs/\n\nimport * as Sentry from \"@sentry/nextjs\";\n\nSentry.init({\n  dsn: process.env.SENTRY_DSN,\n\n  // Adjust this value in production, or use tracesSampler for greater control\n  tracesSampleRate: 1,\n\n  // Setting this option to true will print useful information to the console while you're setting up Sentry.\n  debug: false,\n\n  // Uncomment the line below to enable Spotlight (https://spotlightjs.com)\n  // spotlight: process.env.NODE_ENV === 'development',\n\n  // Performance Monitoring\n  integrations: [\n    // Add integrations for performance monitoring\n    // Note: nodeProfilingIntegration may not be available in all versions\n  ],\n\n  // Set user context for server-side\n  initialScope: {\n    tags: {\n      component: \"server\"\n    },\n  },\n\n  // Environment configuration\n  environment: process.env.NODE_ENV || 'development',\n\n  // Release tracking\n  release: process.env.VERCEL_GIT_COMMIT_SHA || 'development',\n\n  // Server-side error filtering\n  beforeSend(event) {\n    // Filter out certain server errors\n    if (process.env.NODE_ENV === 'development') {\n      // Don't send certain errors in development\n      if (event.exception) {\n        const error = event.exception.values?.[0];\n        if (error?.type === 'ENOENT' || \n            error?.value?.includes('ECONNREFUSED')) {\n          return null;\n        }\n      }\n    }\n    return event;\n  },\n\n  // Custom server tags\n  beforeSendTransaction(event) {\n    // Add custom tags to server transactions\n    event.tags = {\n      ...event.tags,\n      section: 'server',\n    };\n    return event;\n  },\n\n  // Configure sampling for different transaction types\n  tracesSampler: (samplingContext) => {\n    // Sample API routes at a higher rate\n    if (samplingContext.request?.url?.includes('/api/')) {\n      return 1.0;\n    }\n    \n    // Sample page requests at a lower rate in production\n    if (process.env.NODE_ENV === 'production') {\n      return 0.1;\n    }\n    \n    // Sample everything in development\n    return 1.0;\n  },\n});\n"], "names": [], "mappings": "AAAA,uEAAuE;AACvE,6EAA6E;AAC7E,6DAA6D;;AAE7D;;AAEA,+WAAA,CAAA,OAAW,CAAC;IACV,KAAK,QAAQ,GAAG,CAAC,UAAU;IAE3B,4EAA4E;IAC5E,kBAAkB;IAElB,2GAA2G;IAC3G,OAAO;IAEP,yEAAyE;IACzE,qDAAqD;IAErD,yBAAyB;IACzB,cAAc,EAGb;IAED,mCAAmC;IACnC,cAAc;QACZ,MAAM;YACJ,WAAW;QACb;IACF;IAEA,4BAA4B;IAC5B,aAAa,mDAAwB;IAErC,mBAAmB;IACnB,SAAS,QAAQ,GAAG,CAAC,qBAAqB,IAAI;IAE9C,8BAA8B;IAC9B,YAAW,KAAK;QACd,mCAAmC;QACnC,wCAA4C;YAC1C,2CAA2C;YAC3C,IAAI,MAAM,SAAS,EAAE;gBACnB,MAAM,QAAQ,MAAM,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;gBACzC,IAAI,OAAO,SAAS,YAChB,OAAO,OAAO,SAAS,iBAAiB;oBAC1C,OAAO;gBACT;YACF;QACF;QACA,OAAO;IACT;IAEA,qBAAqB;IACrB,uBAAsB,KAAK;QACzB,yCAAyC;QACzC,MAAM,IAAI,GAAG;YACX,GAAG,MAAM,IAAI;YACb,SAAS;QACX;QACA,OAAO;IACT;IAEA,qDAAqD;IACrD,eAAe,CAAC;QACd,qCAAqC;QACrC,IAAI,gBAAgB,OAAO,EAAE,KAAK,SAAS,UAAU;YACnD,OAAO;QACT;QAEA,qDAAqD;QACrD;;QAIA,mCAAmC;QACnC,OAAO;IACT;AACF", "debugId": null}}]}