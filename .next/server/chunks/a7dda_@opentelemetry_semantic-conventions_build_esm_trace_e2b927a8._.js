module.exports = {

"[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/index.js [instrumentation] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /* eslint-disable no-restricted-syntax --
 * These re-exports are only of constants, only one-level deep at this point,
 * and should not cause problems for tree-shakers.
 */ __turbopack_context__.s({});
;
 //# sourceMappingURL=index.js.map
}),
"[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/index.js [instrumentation] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/index.js [instrumentation] (ecmascript) <locals>");
}),
"[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "DBCASSANDRACONSISTENCYLEVELVALUES_ALL": ()=>DBCASSANDRACONSISTENCYLEVELVALUES_ALL,
    "DBCASSANDRACONSISTENCYLEVELVALUES_ANY": ()=>DBCASSANDRACONSISTENCYLEVELVALUES_ANY,
    "DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM": ()=>DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM,
    "DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE": ()=>DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE,
    "DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM": ()=>DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM,
    "DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL": ()=>DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL,
    "DBCASSANDRACONSISTENCYLEVELVALUES_ONE": ()=>DBCASSANDRACONSISTENCYLEVELVALUES_ONE,
    "DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM": ()=>DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM,
    "DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL": ()=>DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL,
    "DBCASSANDRACONSISTENCYLEVELVALUES_THREE": ()=>DBCASSANDRACONSISTENCYLEVELVALUES_THREE,
    "DBCASSANDRACONSISTENCYLEVELVALUES_TWO": ()=>DBCASSANDRACONSISTENCYLEVELVALUES_TWO,
    "DBSYSTEMVALUES_ADABAS": ()=>DBSYSTEMVALUES_ADABAS,
    "DBSYSTEMVALUES_CACHE": ()=>DBSYSTEMVALUES_CACHE,
    "DBSYSTEMVALUES_CASSANDRA": ()=>DBSYSTEMVALUES_CASSANDRA,
    "DBSYSTEMVALUES_CLOUDSCAPE": ()=>DBSYSTEMVALUES_CLOUDSCAPE,
    "DBSYSTEMVALUES_COCKROACHDB": ()=>DBSYSTEMVALUES_COCKROACHDB,
    "DBSYSTEMVALUES_COLDFUSION": ()=>DBSYSTEMVALUES_COLDFUSION,
    "DBSYSTEMVALUES_COSMOSDB": ()=>DBSYSTEMVALUES_COSMOSDB,
    "DBSYSTEMVALUES_COUCHBASE": ()=>DBSYSTEMVALUES_COUCHBASE,
    "DBSYSTEMVALUES_COUCHDB": ()=>DBSYSTEMVALUES_COUCHDB,
    "DBSYSTEMVALUES_DB2": ()=>DBSYSTEMVALUES_DB2,
    "DBSYSTEMVALUES_DERBY": ()=>DBSYSTEMVALUES_DERBY,
    "DBSYSTEMVALUES_DYNAMODB": ()=>DBSYSTEMVALUES_DYNAMODB,
    "DBSYSTEMVALUES_EDB": ()=>DBSYSTEMVALUES_EDB,
    "DBSYSTEMVALUES_ELASTICSEARCH": ()=>DBSYSTEMVALUES_ELASTICSEARCH,
    "DBSYSTEMVALUES_FILEMAKER": ()=>DBSYSTEMVALUES_FILEMAKER,
    "DBSYSTEMVALUES_FIREBIRD": ()=>DBSYSTEMVALUES_FIREBIRD,
    "DBSYSTEMVALUES_FIRSTSQL": ()=>DBSYSTEMVALUES_FIRSTSQL,
    "DBSYSTEMVALUES_GEODE": ()=>DBSYSTEMVALUES_GEODE,
    "DBSYSTEMVALUES_H2": ()=>DBSYSTEMVALUES_H2,
    "DBSYSTEMVALUES_HANADB": ()=>DBSYSTEMVALUES_HANADB,
    "DBSYSTEMVALUES_HBASE": ()=>DBSYSTEMVALUES_HBASE,
    "DBSYSTEMVALUES_HIVE": ()=>DBSYSTEMVALUES_HIVE,
    "DBSYSTEMVALUES_HSQLDB": ()=>DBSYSTEMVALUES_HSQLDB,
    "DBSYSTEMVALUES_INFORMIX": ()=>DBSYSTEMVALUES_INFORMIX,
    "DBSYSTEMVALUES_INGRES": ()=>DBSYSTEMVALUES_INGRES,
    "DBSYSTEMVALUES_INSTANTDB": ()=>DBSYSTEMVALUES_INSTANTDB,
    "DBSYSTEMVALUES_INTERBASE": ()=>DBSYSTEMVALUES_INTERBASE,
    "DBSYSTEMVALUES_MARIADB": ()=>DBSYSTEMVALUES_MARIADB,
    "DBSYSTEMVALUES_MAXDB": ()=>DBSYSTEMVALUES_MAXDB,
    "DBSYSTEMVALUES_MEMCACHED": ()=>DBSYSTEMVALUES_MEMCACHED,
    "DBSYSTEMVALUES_MONGODB": ()=>DBSYSTEMVALUES_MONGODB,
    "DBSYSTEMVALUES_MSSQL": ()=>DBSYSTEMVALUES_MSSQL,
    "DBSYSTEMVALUES_MYSQL": ()=>DBSYSTEMVALUES_MYSQL,
    "DBSYSTEMVALUES_NEO4J": ()=>DBSYSTEMVALUES_NEO4J,
    "DBSYSTEMVALUES_NETEZZA": ()=>DBSYSTEMVALUES_NETEZZA,
    "DBSYSTEMVALUES_ORACLE": ()=>DBSYSTEMVALUES_ORACLE,
    "DBSYSTEMVALUES_OTHER_SQL": ()=>DBSYSTEMVALUES_OTHER_SQL,
    "DBSYSTEMVALUES_PERVASIVE": ()=>DBSYSTEMVALUES_PERVASIVE,
    "DBSYSTEMVALUES_POINTBASE": ()=>DBSYSTEMVALUES_POINTBASE,
    "DBSYSTEMVALUES_POSTGRESQL": ()=>DBSYSTEMVALUES_POSTGRESQL,
    "DBSYSTEMVALUES_PROGRESS": ()=>DBSYSTEMVALUES_PROGRESS,
    "DBSYSTEMVALUES_REDIS": ()=>DBSYSTEMVALUES_REDIS,
    "DBSYSTEMVALUES_REDSHIFT": ()=>DBSYSTEMVALUES_REDSHIFT,
    "DBSYSTEMVALUES_SQLITE": ()=>DBSYSTEMVALUES_SQLITE,
    "DBSYSTEMVALUES_SYBASE": ()=>DBSYSTEMVALUES_SYBASE,
    "DBSYSTEMVALUES_TERADATA": ()=>DBSYSTEMVALUES_TERADATA,
    "DBSYSTEMVALUES_VERTICA": ()=>DBSYSTEMVALUES_VERTICA,
    "DbCassandraConsistencyLevelValues": ()=>DbCassandraConsistencyLevelValues,
    "DbSystemValues": ()=>DbSystemValues,
    "FAASDOCUMENTOPERATIONVALUES_DELETE": ()=>FAASDOCUMENTOPERATIONVALUES_DELETE,
    "FAASDOCUMENTOPERATIONVALUES_EDIT": ()=>FAASDOCUMENTOPERATIONVALUES_EDIT,
    "FAASDOCUMENTOPERATIONVALUES_INSERT": ()=>FAASDOCUMENTOPERATIONVALUES_INSERT,
    "FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD": ()=>FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD,
    "FAASINVOKEDPROVIDERVALUES_AWS": ()=>FAASINVOKEDPROVIDERVALUES_AWS,
    "FAASINVOKEDPROVIDERVALUES_AZURE": ()=>FAASINVOKEDPROVIDERVALUES_AZURE,
    "FAASINVOKEDPROVIDERVALUES_GCP": ()=>FAASINVOKEDPROVIDERVALUES_GCP,
    "FAASTRIGGERVALUES_DATASOURCE": ()=>FAASTRIGGERVALUES_DATASOURCE,
    "FAASTRIGGERVALUES_HTTP": ()=>FAASTRIGGERVALUES_HTTP,
    "FAASTRIGGERVALUES_OTHER": ()=>FAASTRIGGERVALUES_OTHER,
    "FAASTRIGGERVALUES_PUBSUB": ()=>FAASTRIGGERVALUES_PUBSUB,
    "FAASTRIGGERVALUES_TIMER": ()=>FAASTRIGGERVALUES_TIMER,
    "FaasDocumentOperationValues": ()=>FaasDocumentOperationValues,
    "FaasInvokedProviderValues": ()=>FaasInvokedProviderValues,
    "FaasTriggerValues": ()=>FaasTriggerValues,
    "HTTPFLAVORVALUES_HTTP_1_0": ()=>HTTPFLAVORVALUES_HTTP_1_0,
    "HTTPFLAVORVALUES_HTTP_1_1": ()=>HTTPFLAVORVALUES_HTTP_1_1,
    "HTTPFLAVORVALUES_HTTP_2_0": ()=>HTTPFLAVORVALUES_HTTP_2_0,
    "HTTPFLAVORVALUES_QUIC": ()=>HTTPFLAVORVALUES_QUIC,
    "HTTPFLAVORVALUES_SPDY": ()=>HTTPFLAVORVALUES_SPDY,
    "HttpFlavorValues": ()=>HttpFlavorValues,
    "MESSAGETYPEVALUES_RECEIVED": ()=>MESSAGETYPEVALUES_RECEIVED,
    "MESSAGETYPEVALUES_SENT": ()=>MESSAGETYPEVALUES_SENT,
    "MESSAGINGDESTINATIONKINDVALUES_QUEUE": ()=>MESSAGINGDESTINATIONKINDVALUES_QUEUE,
    "MESSAGINGDESTINATIONKINDVALUES_TOPIC": ()=>MESSAGINGDESTINATIONKINDVALUES_TOPIC,
    "MESSAGINGOPERATIONVALUES_PROCESS": ()=>MESSAGINGOPERATIONVALUES_PROCESS,
    "MESSAGINGOPERATIONVALUES_RECEIVE": ()=>MESSAGINGOPERATIONVALUES_RECEIVE,
    "MessageTypeValues": ()=>MessageTypeValues,
    "MessagingDestinationKindValues": ()=>MessagingDestinationKindValues,
    "MessagingOperationValues": ()=>MessagingOperationValues,
    "NETHOSTCONNECTIONSUBTYPEVALUES_CDMA": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_CDMA,
    "NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT,
    "NETHOSTCONNECTIONSUBTYPEVALUES_EDGE": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_EDGE,
    "NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD,
    "NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0,
    "NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A,
    "NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B,
    "NETHOSTCONNECTIONSUBTYPEVALUES_GPRS": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_GPRS,
    "NETHOSTCONNECTIONSUBTYPEVALUES_GSM": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_GSM,
    "NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA,
    "NETHOSTCONNECTIONSUBTYPEVALUES_HSPA": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_HSPA,
    "NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP,
    "NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA,
    "NETHOSTCONNECTIONSUBTYPEVALUES_IDEN": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_IDEN,
    "NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN,
    "NETHOSTCONNECTIONSUBTYPEVALUES_LTE": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_LTE,
    "NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA,
    "NETHOSTCONNECTIONSUBTYPEVALUES_NR": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_NR,
    "NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA,
    "NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA,
    "NETHOSTCONNECTIONSUBTYPEVALUES_UMTS": ()=>NETHOSTCONNECTIONSUBTYPEVALUES_UMTS,
    "NETHOSTCONNECTIONTYPEVALUES_CELL": ()=>NETHOSTCONNECTIONTYPEVALUES_CELL,
    "NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE": ()=>NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE,
    "NETHOSTCONNECTIONTYPEVALUES_UNKNOWN": ()=>NETHOSTCONNECTIONTYPEVALUES_UNKNOWN,
    "NETHOSTCONNECTIONTYPEVALUES_WIFI": ()=>NETHOSTCONNECTIONTYPEVALUES_WIFI,
    "NETHOSTCONNECTIONTYPEVALUES_WIRED": ()=>NETHOSTCONNECTIONTYPEVALUES_WIRED,
    "NETTRANSPORTVALUES_INPROC": ()=>NETTRANSPORTVALUES_INPROC,
    "NETTRANSPORTVALUES_IP": ()=>NETTRANSPORTVALUES_IP,
    "NETTRANSPORTVALUES_IP_TCP": ()=>NETTRANSPORTVALUES_IP_TCP,
    "NETTRANSPORTVALUES_IP_UDP": ()=>NETTRANSPORTVALUES_IP_UDP,
    "NETTRANSPORTVALUES_OTHER": ()=>NETTRANSPORTVALUES_OTHER,
    "NETTRANSPORTVALUES_PIPE": ()=>NETTRANSPORTVALUES_PIPE,
    "NETTRANSPORTVALUES_UNIX": ()=>NETTRANSPORTVALUES_UNIX,
    "NetHostConnectionSubtypeValues": ()=>NetHostConnectionSubtypeValues,
    "NetHostConnectionTypeValues": ()=>NetHostConnectionTypeValues,
    "NetTransportValues": ()=>NetTransportValues,
    "RPCGRPCSTATUSCODEVALUES_ABORTED": ()=>RPCGRPCSTATUSCODEVALUES_ABORTED,
    "RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS": ()=>RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS,
    "RPCGRPCSTATUSCODEVALUES_CANCELLED": ()=>RPCGRPCSTATUSCODEVALUES_CANCELLED,
    "RPCGRPCSTATUSCODEVALUES_DATA_LOSS": ()=>RPCGRPCSTATUSCODEVALUES_DATA_LOSS,
    "RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED": ()=>RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED,
    "RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION": ()=>RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION,
    "RPCGRPCSTATUSCODEVALUES_INTERNAL": ()=>RPCGRPCSTATUSCODEVALUES_INTERNAL,
    "RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT": ()=>RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT,
    "RPCGRPCSTATUSCODEVALUES_NOT_FOUND": ()=>RPCGRPCSTATUSCODEVALUES_NOT_FOUND,
    "RPCGRPCSTATUSCODEVALUES_OK": ()=>RPCGRPCSTATUSCODEVALUES_OK,
    "RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE": ()=>RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE,
    "RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED": ()=>RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED,
    "RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED": ()=>RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED,
    "RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED": ()=>RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED,
    "RPCGRPCSTATUSCODEVALUES_UNAVAILABLE": ()=>RPCGRPCSTATUSCODEVALUES_UNAVAILABLE,
    "RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED": ()=>RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED,
    "RPCGRPCSTATUSCODEVALUES_UNKNOWN": ()=>RPCGRPCSTATUSCODEVALUES_UNKNOWN,
    "RpcGrpcStatusCodeValues": ()=>RpcGrpcStatusCodeValues,
    "SEMATTRS_AWS_DYNAMODB_ATTRIBUTES_TO_GET": ()=>SEMATTRS_AWS_DYNAMODB_ATTRIBUTES_TO_GET,
    "SEMATTRS_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS": ()=>SEMATTRS_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS,
    "SEMATTRS_AWS_DYNAMODB_CONSISTENT_READ": ()=>SEMATTRS_AWS_DYNAMODB_CONSISTENT_READ,
    "SEMATTRS_AWS_DYNAMODB_CONSUMED_CAPACITY": ()=>SEMATTRS_AWS_DYNAMODB_CONSUMED_CAPACITY,
    "SEMATTRS_AWS_DYNAMODB_COUNT": ()=>SEMATTRS_AWS_DYNAMODB_COUNT,
    "SEMATTRS_AWS_DYNAMODB_EXCLUSIVE_START_TABLE": ()=>SEMATTRS_AWS_DYNAMODB_EXCLUSIVE_START_TABLE,
    "SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES": ()=>SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES,
    "SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES": ()=>SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES,
    "SEMATTRS_AWS_DYNAMODB_INDEX_NAME": ()=>SEMATTRS_AWS_DYNAMODB_INDEX_NAME,
    "SEMATTRS_AWS_DYNAMODB_ITEM_COLLECTION_METRICS": ()=>SEMATTRS_AWS_DYNAMODB_ITEM_COLLECTION_METRICS,
    "SEMATTRS_AWS_DYNAMODB_LIMIT": ()=>SEMATTRS_AWS_DYNAMODB_LIMIT,
    "SEMATTRS_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES": ()=>SEMATTRS_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES,
    "SEMATTRS_AWS_DYNAMODB_PROJECTION": ()=>SEMATTRS_AWS_DYNAMODB_PROJECTION,
    "SEMATTRS_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY": ()=>SEMATTRS_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY,
    "SEMATTRS_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY": ()=>SEMATTRS_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY,
    "SEMATTRS_AWS_DYNAMODB_SCANNED_COUNT": ()=>SEMATTRS_AWS_DYNAMODB_SCANNED_COUNT,
    "SEMATTRS_AWS_DYNAMODB_SCAN_FORWARD": ()=>SEMATTRS_AWS_DYNAMODB_SCAN_FORWARD,
    "SEMATTRS_AWS_DYNAMODB_SEGMENT": ()=>SEMATTRS_AWS_DYNAMODB_SEGMENT,
    "SEMATTRS_AWS_DYNAMODB_SELECT": ()=>SEMATTRS_AWS_DYNAMODB_SELECT,
    "SEMATTRS_AWS_DYNAMODB_TABLE_COUNT": ()=>SEMATTRS_AWS_DYNAMODB_TABLE_COUNT,
    "SEMATTRS_AWS_DYNAMODB_TABLE_NAMES": ()=>SEMATTRS_AWS_DYNAMODB_TABLE_NAMES,
    "SEMATTRS_AWS_DYNAMODB_TOTAL_SEGMENTS": ()=>SEMATTRS_AWS_DYNAMODB_TOTAL_SEGMENTS,
    "SEMATTRS_AWS_LAMBDA_INVOKED_ARN": ()=>SEMATTRS_AWS_LAMBDA_INVOKED_ARN,
    "SEMATTRS_CODE_FILEPATH": ()=>SEMATTRS_CODE_FILEPATH,
    "SEMATTRS_CODE_FUNCTION": ()=>SEMATTRS_CODE_FUNCTION,
    "SEMATTRS_CODE_LINENO": ()=>SEMATTRS_CODE_LINENO,
    "SEMATTRS_CODE_NAMESPACE": ()=>SEMATTRS_CODE_NAMESPACE,
    "SEMATTRS_DB_CASSANDRA_CONSISTENCY_LEVEL": ()=>SEMATTRS_DB_CASSANDRA_CONSISTENCY_LEVEL,
    "SEMATTRS_DB_CASSANDRA_COORDINATOR_DC": ()=>SEMATTRS_DB_CASSANDRA_COORDINATOR_DC,
    "SEMATTRS_DB_CASSANDRA_COORDINATOR_ID": ()=>SEMATTRS_DB_CASSANDRA_COORDINATOR_ID,
    "SEMATTRS_DB_CASSANDRA_IDEMPOTENCE": ()=>SEMATTRS_DB_CASSANDRA_IDEMPOTENCE,
    "SEMATTRS_DB_CASSANDRA_KEYSPACE": ()=>SEMATTRS_DB_CASSANDRA_KEYSPACE,
    "SEMATTRS_DB_CASSANDRA_PAGE_SIZE": ()=>SEMATTRS_DB_CASSANDRA_PAGE_SIZE,
    "SEMATTRS_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT": ()=>SEMATTRS_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT,
    "SEMATTRS_DB_CASSANDRA_TABLE": ()=>SEMATTRS_DB_CASSANDRA_TABLE,
    "SEMATTRS_DB_CONNECTION_STRING": ()=>SEMATTRS_DB_CONNECTION_STRING,
    "SEMATTRS_DB_HBASE_NAMESPACE": ()=>SEMATTRS_DB_HBASE_NAMESPACE,
    "SEMATTRS_DB_JDBC_DRIVER_CLASSNAME": ()=>SEMATTRS_DB_JDBC_DRIVER_CLASSNAME,
    "SEMATTRS_DB_MONGODB_COLLECTION": ()=>SEMATTRS_DB_MONGODB_COLLECTION,
    "SEMATTRS_DB_MSSQL_INSTANCE_NAME": ()=>SEMATTRS_DB_MSSQL_INSTANCE_NAME,
    "SEMATTRS_DB_NAME": ()=>SEMATTRS_DB_NAME,
    "SEMATTRS_DB_OPERATION": ()=>SEMATTRS_DB_OPERATION,
    "SEMATTRS_DB_REDIS_DATABASE_INDEX": ()=>SEMATTRS_DB_REDIS_DATABASE_INDEX,
    "SEMATTRS_DB_SQL_TABLE": ()=>SEMATTRS_DB_SQL_TABLE,
    "SEMATTRS_DB_STATEMENT": ()=>SEMATTRS_DB_STATEMENT,
    "SEMATTRS_DB_SYSTEM": ()=>SEMATTRS_DB_SYSTEM,
    "SEMATTRS_DB_USER": ()=>SEMATTRS_DB_USER,
    "SEMATTRS_ENDUSER_ID": ()=>SEMATTRS_ENDUSER_ID,
    "SEMATTRS_ENDUSER_ROLE": ()=>SEMATTRS_ENDUSER_ROLE,
    "SEMATTRS_ENDUSER_SCOPE": ()=>SEMATTRS_ENDUSER_SCOPE,
    "SEMATTRS_EXCEPTION_ESCAPED": ()=>SEMATTRS_EXCEPTION_ESCAPED,
    "SEMATTRS_EXCEPTION_MESSAGE": ()=>SEMATTRS_EXCEPTION_MESSAGE,
    "SEMATTRS_EXCEPTION_STACKTRACE": ()=>SEMATTRS_EXCEPTION_STACKTRACE,
    "SEMATTRS_EXCEPTION_TYPE": ()=>SEMATTRS_EXCEPTION_TYPE,
    "SEMATTRS_FAAS_COLDSTART": ()=>SEMATTRS_FAAS_COLDSTART,
    "SEMATTRS_FAAS_CRON": ()=>SEMATTRS_FAAS_CRON,
    "SEMATTRS_FAAS_DOCUMENT_COLLECTION": ()=>SEMATTRS_FAAS_DOCUMENT_COLLECTION,
    "SEMATTRS_FAAS_DOCUMENT_NAME": ()=>SEMATTRS_FAAS_DOCUMENT_NAME,
    "SEMATTRS_FAAS_DOCUMENT_OPERATION": ()=>SEMATTRS_FAAS_DOCUMENT_OPERATION,
    "SEMATTRS_FAAS_DOCUMENT_TIME": ()=>SEMATTRS_FAAS_DOCUMENT_TIME,
    "SEMATTRS_FAAS_EXECUTION": ()=>SEMATTRS_FAAS_EXECUTION,
    "SEMATTRS_FAAS_INVOKED_NAME": ()=>SEMATTRS_FAAS_INVOKED_NAME,
    "SEMATTRS_FAAS_INVOKED_PROVIDER": ()=>SEMATTRS_FAAS_INVOKED_PROVIDER,
    "SEMATTRS_FAAS_INVOKED_REGION": ()=>SEMATTRS_FAAS_INVOKED_REGION,
    "SEMATTRS_FAAS_TIME": ()=>SEMATTRS_FAAS_TIME,
    "SEMATTRS_FAAS_TRIGGER": ()=>SEMATTRS_FAAS_TRIGGER,
    "SEMATTRS_HTTP_CLIENT_IP": ()=>SEMATTRS_HTTP_CLIENT_IP,
    "SEMATTRS_HTTP_FLAVOR": ()=>SEMATTRS_HTTP_FLAVOR,
    "SEMATTRS_HTTP_HOST": ()=>SEMATTRS_HTTP_HOST,
    "SEMATTRS_HTTP_METHOD": ()=>SEMATTRS_HTTP_METHOD,
    "SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH": ()=>SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH,
    "SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED": ()=>SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED,
    "SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH": ()=>SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH,
    "SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED": ()=>SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED,
    "SEMATTRS_HTTP_ROUTE": ()=>SEMATTRS_HTTP_ROUTE,
    "SEMATTRS_HTTP_SCHEME": ()=>SEMATTRS_HTTP_SCHEME,
    "SEMATTRS_HTTP_SERVER_NAME": ()=>SEMATTRS_HTTP_SERVER_NAME,
    "SEMATTRS_HTTP_STATUS_CODE": ()=>SEMATTRS_HTTP_STATUS_CODE,
    "SEMATTRS_HTTP_TARGET": ()=>SEMATTRS_HTTP_TARGET,
    "SEMATTRS_HTTP_URL": ()=>SEMATTRS_HTTP_URL,
    "SEMATTRS_HTTP_USER_AGENT": ()=>SEMATTRS_HTTP_USER_AGENT,
    "SEMATTRS_MESSAGE_COMPRESSED_SIZE": ()=>SEMATTRS_MESSAGE_COMPRESSED_SIZE,
    "SEMATTRS_MESSAGE_ID": ()=>SEMATTRS_MESSAGE_ID,
    "SEMATTRS_MESSAGE_TYPE": ()=>SEMATTRS_MESSAGE_TYPE,
    "SEMATTRS_MESSAGE_UNCOMPRESSED_SIZE": ()=>SEMATTRS_MESSAGE_UNCOMPRESSED_SIZE,
    "SEMATTRS_MESSAGING_CONSUMER_ID": ()=>SEMATTRS_MESSAGING_CONSUMER_ID,
    "SEMATTRS_MESSAGING_CONVERSATION_ID": ()=>SEMATTRS_MESSAGING_CONVERSATION_ID,
    "SEMATTRS_MESSAGING_DESTINATION": ()=>SEMATTRS_MESSAGING_DESTINATION,
    "SEMATTRS_MESSAGING_DESTINATION_KIND": ()=>SEMATTRS_MESSAGING_DESTINATION_KIND,
    "SEMATTRS_MESSAGING_KAFKA_CLIENT_ID": ()=>SEMATTRS_MESSAGING_KAFKA_CLIENT_ID,
    "SEMATTRS_MESSAGING_KAFKA_CONSUMER_GROUP": ()=>SEMATTRS_MESSAGING_KAFKA_CONSUMER_GROUP,
    "SEMATTRS_MESSAGING_KAFKA_MESSAGE_KEY": ()=>SEMATTRS_MESSAGING_KAFKA_MESSAGE_KEY,
    "SEMATTRS_MESSAGING_KAFKA_PARTITION": ()=>SEMATTRS_MESSAGING_KAFKA_PARTITION,
    "SEMATTRS_MESSAGING_KAFKA_TOMBSTONE": ()=>SEMATTRS_MESSAGING_KAFKA_TOMBSTONE,
    "SEMATTRS_MESSAGING_MESSAGE_ID": ()=>SEMATTRS_MESSAGING_MESSAGE_ID,
    "SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES": ()=>SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES,
    "SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES": ()=>SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES,
    "SEMATTRS_MESSAGING_OPERATION": ()=>SEMATTRS_MESSAGING_OPERATION,
    "SEMATTRS_MESSAGING_PROTOCOL": ()=>SEMATTRS_MESSAGING_PROTOCOL,
    "SEMATTRS_MESSAGING_PROTOCOL_VERSION": ()=>SEMATTRS_MESSAGING_PROTOCOL_VERSION,
    "SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY": ()=>SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY,
    "SEMATTRS_MESSAGING_SYSTEM": ()=>SEMATTRS_MESSAGING_SYSTEM,
    "SEMATTRS_MESSAGING_TEMP_DESTINATION": ()=>SEMATTRS_MESSAGING_TEMP_DESTINATION,
    "SEMATTRS_MESSAGING_URL": ()=>SEMATTRS_MESSAGING_URL,
    "SEMATTRS_NET_HOST_CARRIER_ICC": ()=>SEMATTRS_NET_HOST_CARRIER_ICC,
    "SEMATTRS_NET_HOST_CARRIER_MCC": ()=>SEMATTRS_NET_HOST_CARRIER_MCC,
    "SEMATTRS_NET_HOST_CARRIER_MNC": ()=>SEMATTRS_NET_HOST_CARRIER_MNC,
    "SEMATTRS_NET_HOST_CARRIER_NAME": ()=>SEMATTRS_NET_HOST_CARRIER_NAME,
    "SEMATTRS_NET_HOST_CONNECTION_SUBTYPE": ()=>SEMATTRS_NET_HOST_CONNECTION_SUBTYPE,
    "SEMATTRS_NET_HOST_CONNECTION_TYPE": ()=>SEMATTRS_NET_HOST_CONNECTION_TYPE,
    "SEMATTRS_NET_HOST_IP": ()=>SEMATTRS_NET_HOST_IP,
    "SEMATTRS_NET_HOST_NAME": ()=>SEMATTRS_NET_HOST_NAME,
    "SEMATTRS_NET_HOST_PORT": ()=>SEMATTRS_NET_HOST_PORT,
    "SEMATTRS_NET_PEER_IP": ()=>SEMATTRS_NET_PEER_IP,
    "SEMATTRS_NET_PEER_NAME": ()=>SEMATTRS_NET_PEER_NAME,
    "SEMATTRS_NET_PEER_PORT": ()=>SEMATTRS_NET_PEER_PORT,
    "SEMATTRS_NET_TRANSPORT": ()=>SEMATTRS_NET_TRANSPORT,
    "SEMATTRS_PEER_SERVICE": ()=>SEMATTRS_PEER_SERVICE,
    "SEMATTRS_RPC_GRPC_STATUS_CODE": ()=>SEMATTRS_RPC_GRPC_STATUS_CODE,
    "SEMATTRS_RPC_JSONRPC_ERROR_CODE": ()=>SEMATTRS_RPC_JSONRPC_ERROR_CODE,
    "SEMATTRS_RPC_JSONRPC_ERROR_MESSAGE": ()=>SEMATTRS_RPC_JSONRPC_ERROR_MESSAGE,
    "SEMATTRS_RPC_JSONRPC_REQUEST_ID": ()=>SEMATTRS_RPC_JSONRPC_REQUEST_ID,
    "SEMATTRS_RPC_JSONRPC_VERSION": ()=>SEMATTRS_RPC_JSONRPC_VERSION,
    "SEMATTRS_RPC_METHOD": ()=>SEMATTRS_RPC_METHOD,
    "SEMATTRS_RPC_SERVICE": ()=>SEMATTRS_RPC_SERVICE,
    "SEMATTRS_RPC_SYSTEM": ()=>SEMATTRS_RPC_SYSTEM,
    "SEMATTRS_THREAD_ID": ()=>SEMATTRS_THREAD_ID,
    "SEMATTRS_THREAD_NAME": ()=>SEMATTRS_THREAD_NAME,
    "SemanticAttributes": ()=>SemanticAttributes
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/internal/utils.js [instrumentation] (ecmascript)");
;
//----------------------------------------------------------------------------------------------------------
// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates//templates/SemanticAttributes.ts.j2
//----------------------------------------------------------------------------------------------------------
//----------------------------------------------------------------------------------------------------------
// Constant values for SemanticAttributes
//----------------------------------------------------------------------------------------------------------
// Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_AWS_LAMBDA_INVOKED_ARN = 'aws.lambda.invoked_arn';
var TMP_DB_SYSTEM = 'db.system';
var TMP_DB_CONNECTION_STRING = 'db.connection_string';
var TMP_DB_USER = 'db.user';
var TMP_DB_JDBC_DRIVER_CLASSNAME = 'db.jdbc.driver_classname';
var TMP_DB_NAME = 'db.name';
var TMP_DB_STATEMENT = 'db.statement';
var TMP_DB_OPERATION = 'db.operation';
var TMP_DB_MSSQL_INSTANCE_NAME = 'db.mssql.instance_name';
var TMP_DB_CASSANDRA_KEYSPACE = 'db.cassandra.keyspace';
var TMP_DB_CASSANDRA_PAGE_SIZE = 'db.cassandra.page_size';
var TMP_DB_CASSANDRA_CONSISTENCY_LEVEL = 'db.cassandra.consistency_level';
var TMP_DB_CASSANDRA_TABLE = 'db.cassandra.table';
var TMP_DB_CASSANDRA_IDEMPOTENCE = 'db.cassandra.idempotence';
var TMP_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT = 'db.cassandra.speculative_execution_count';
var TMP_DB_CASSANDRA_COORDINATOR_ID = 'db.cassandra.coordinator.id';
var TMP_DB_CASSANDRA_COORDINATOR_DC = 'db.cassandra.coordinator.dc';
var TMP_DB_HBASE_NAMESPACE = 'db.hbase.namespace';
var TMP_DB_REDIS_DATABASE_INDEX = 'db.redis.database_index';
var TMP_DB_MONGODB_COLLECTION = 'db.mongodb.collection';
var TMP_DB_SQL_TABLE = 'db.sql.table';
var TMP_EXCEPTION_TYPE = 'exception.type';
var TMP_EXCEPTION_MESSAGE = 'exception.message';
var TMP_EXCEPTION_STACKTRACE = 'exception.stacktrace';
var TMP_EXCEPTION_ESCAPED = 'exception.escaped';
var TMP_FAAS_TRIGGER = 'faas.trigger';
var TMP_FAAS_EXECUTION = 'faas.execution';
var TMP_FAAS_DOCUMENT_COLLECTION = 'faas.document.collection';
var TMP_FAAS_DOCUMENT_OPERATION = 'faas.document.operation';
var TMP_FAAS_DOCUMENT_TIME = 'faas.document.time';
var TMP_FAAS_DOCUMENT_NAME = 'faas.document.name';
var TMP_FAAS_TIME = 'faas.time';
var TMP_FAAS_CRON = 'faas.cron';
var TMP_FAAS_COLDSTART = 'faas.coldstart';
var TMP_FAAS_INVOKED_NAME = 'faas.invoked_name';
var TMP_FAAS_INVOKED_PROVIDER = 'faas.invoked_provider';
var TMP_FAAS_INVOKED_REGION = 'faas.invoked_region';
var TMP_NET_TRANSPORT = 'net.transport';
var TMP_NET_PEER_IP = 'net.peer.ip';
var TMP_NET_PEER_PORT = 'net.peer.port';
var TMP_NET_PEER_NAME = 'net.peer.name';
var TMP_NET_HOST_IP = 'net.host.ip';
var TMP_NET_HOST_PORT = 'net.host.port';
var TMP_NET_HOST_NAME = 'net.host.name';
var TMP_NET_HOST_CONNECTION_TYPE = 'net.host.connection.type';
var TMP_NET_HOST_CONNECTION_SUBTYPE = 'net.host.connection.subtype';
var TMP_NET_HOST_CARRIER_NAME = 'net.host.carrier.name';
var TMP_NET_HOST_CARRIER_MCC = 'net.host.carrier.mcc';
var TMP_NET_HOST_CARRIER_MNC = 'net.host.carrier.mnc';
var TMP_NET_HOST_CARRIER_ICC = 'net.host.carrier.icc';
var TMP_PEER_SERVICE = 'peer.service';
var TMP_ENDUSER_ID = 'enduser.id';
var TMP_ENDUSER_ROLE = 'enduser.role';
var TMP_ENDUSER_SCOPE = 'enduser.scope';
var TMP_THREAD_ID = 'thread.id';
var TMP_THREAD_NAME = 'thread.name';
var TMP_CODE_FUNCTION = 'code.function';
var TMP_CODE_NAMESPACE = 'code.namespace';
var TMP_CODE_FILEPATH = 'code.filepath';
var TMP_CODE_LINENO = 'code.lineno';
var TMP_HTTP_METHOD = 'http.method';
var TMP_HTTP_URL = 'http.url';
var TMP_HTTP_TARGET = 'http.target';
var TMP_HTTP_HOST = 'http.host';
var TMP_HTTP_SCHEME = 'http.scheme';
var TMP_HTTP_STATUS_CODE = 'http.status_code';
var TMP_HTTP_FLAVOR = 'http.flavor';
var TMP_HTTP_USER_AGENT = 'http.user_agent';
var TMP_HTTP_REQUEST_CONTENT_LENGTH = 'http.request_content_length';
var TMP_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED = 'http.request_content_length_uncompressed';
var TMP_HTTP_RESPONSE_CONTENT_LENGTH = 'http.response_content_length';
var TMP_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED = 'http.response_content_length_uncompressed';
var TMP_HTTP_SERVER_NAME = 'http.server_name';
var TMP_HTTP_ROUTE = 'http.route';
var TMP_HTTP_CLIENT_IP = 'http.client_ip';
var TMP_AWS_DYNAMODB_TABLE_NAMES = 'aws.dynamodb.table_names';
var TMP_AWS_DYNAMODB_CONSUMED_CAPACITY = 'aws.dynamodb.consumed_capacity';
var TMP_AWS_DYNAMODB_ITEM_COLLECTION_METRICS = 'aws.dynamodb.item_collection_metrics';
var TMP_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY = 'aws.dynamodb.provisioned_read_capacity';
var TMP_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY = 'aws.dynamodb.provisioned_write_capacity';
var TMP_AWS_DYNAMODB_CONSISTENT_READ = 'aws.dynamodb.consistent_read';
var TMP_AWS_DYNAMODB_PROJECTION = 'aws.dynamodb.projection';
var TMP_AWS_DYNAMODB_LIMIT = 'aws.dynamodb.limit';
var TMP_AWS_DYNAMODB_ATTRIBUTES_TO_GET = 'aws.dynamodb.attributes_to_get';
var TMP_AWS_DYNAMODB_INDEX_NAME = 'aws.dynamodb.index_name';
var TMP_AWS_DYNAMODB_SELECT = 'aws.dynamodb.select';
var TMP_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES = 'aws.dynamodb.global_secondary_indexes';
var TMP_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES = 'aws.dynamodb.local_secondary_indexes';
var TMP_AWS_DYNAMODB_EXCLUSIVE_START_TABLE = 'aws.dynamodb.exclusive_start_table';
var TMP_AWS_DYNAMODB_TABLE_COUNT = 'aws.dynamodb.table_count';
var TMP_AWS_DYNAMODB_SCAN_FORWARD = 'aws.dynamodb.scan_forward';
var TMP_AWS_DYNAMODB_SEGMENT = 'aws.dynamodb.segment';
var TMP_AWS_DYNAMODB_TOTAL_SEGMENTS = 'aws.dynamodb.total_segments';
var TMP_AWS_DYNAMODB_COUNT = 'aws.dynamodb.count';
var TMP_AWS_DYNAMODB_SCANNED_COUNT = 'aws.dynamodb.scanned_count';
var TMP_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS = 'aws.dynamodb.attribute_definitions';
var TMP_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES = 'aws.dynamodb.global_secondary_index_updates';
var TMP_MESSAGING_SYSTEM = 'messaging.system';
var TMP_MESSAGING_DESTINATION = 'messaging.destination';
var TMP_MESSAGING_DESTINATION_KIND = 'messaging.destination_kind';
var TMP_MESSAGING_TEMP_DESTINATION = 'messaging.temp_destination';
var TMP_MESSAGING_PROTOCOL = 'messaging.protocol';
var TMP_MESSAGING_PROTOCOL_VERSION = 'messaging.protocol_version';
var TMP_MESSAGING_URL = 'messaging.url';
var TMP_MESSAGING_MESSAGE_ID = 'messaging.message_id';
var TMP_MESSAGING_CONVERSATION_ID = 'messaging.conversation_id';
var TMP_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES = 'messaging.message_payload_size_bytes';
var TMP_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES = 'messaging.message_payload_compressed_size_bytes';
var TMP_MESSAGING_OPERATION = 'messaging.operation';
var TMP_MESSAGING_CONSUMER_ID = 'messaging.consumer_id';
var TMP_MESSAGING_RABBITMQ_ROUTING_KEY = 'messaging.rabbitmq.routing_key';
var TMP_MESSAGING_KAFKA_MESSAGE_KEY = 'messaging.kafka.message_key';
var TMP_MESSAGING_KAFKA_CONSUMER_GROUP = 'messaging.kafka.consumer_group';
var TMP_MESSAGING_KAFKA_CLIENT_ID = 'messaging.kafka.client_id';
var TMP_MESSAGING_KAFKA_PARTITION = 'messaging.kafka.partition';
var TMP_MESSAGING_KAFKA_TOMBSTONE = 'messaging.kafka.tombstone';
var TMP_RPC_SYSTEM = 'rpc.system';
var TMP_RPC_SERVICE = 'rpc.service';
var TMP_RPC_METHOD = 'rpc.method';
var TMP_RPC_GRPC_STATUS_CODE = 'rpc.grpc.status_code';
var TMP_RPC_JSONRPC_VERSION = 'rpc.jsonrpc.version';
var TMP_RPC_JSONRPC_REQUEST_ID = 'rpc.jsonrpc.request_id';
var TMP_RPC_JSONRPC_ERROR_CODE = 'rpc.jsonrpc.error_code';
var TMP_RPC_JSONRPC_ERROR_MESSAGE = 'rpc.jsonrpc.error_message';
var TMP_MESSAGE_TYPE = 'message.type';
var TMP_MESSAGE_ID = 'message.id';
var TMP_MESSAGE_COMPRESSED_SIZE = 'message.compressed_size';
var TMP_MESSAGE_UNCOMPRESSED_SIZE = 'message.uncompressed_size';
var SEMATTRS_AWS_LAMBDA_INVOKED_ARN = TMP_AWS_LAMBDA_INVOKED_ARN;
var SEMATTRS_DB_SYSTEM = TMP_DB_SYSTEM;
var SEMATTRS_DB_CONNECTION_STRING = TMP_DB_CONNECTION_STRING;
var SEMATTRS_DB_USER = TMP_DB_USER;
var SEMATTRS_DB_JDBC_DRIVER_CLASSNAME = TMP_DB_JDBC_DRIVER_CLASSNAME;
var SEMATTRS_DB_NAME = TMP_DB_NAME;
var SEMATTRS_DB_STATEMENT = TMP_DB_STATEMENT;
var SEMATTRS_DB_OPERATION = TMP_DB_OPERATION;
var SEMATTRS_DB_MSSQL_INSTANCE_NAME = TMP_DB_MSSQL_INSTANCE_NAME;
var SEMATTRS_DB_CASSANDRA_KEYSPACE = TMP_DB_CASSANDRA_KEYSPACE;
var SEMATTRS_DB_CASSANDRA_PAGE_SIZE = TMP_DB_CASSANDRA_PAGE_SIZE;
var SEMATTRS_DB_CASSANDRA_CONSISTENCY_LEVEL = TMP_DB_CASSANDRA_CONSISTENCY_LEVEL;
var SEMATTRS_DB_CASSANDRA_TABLE = TMP_DB_CASSANDRA_TABLE;
var SEMATTRS_DB_CASSANDRA_IDEMPOTENCE = TMP_DB_CASSANDRA_IDEMPOTENCE;
var SEMATTRS_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT = TMP_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT;
var SEMATTRS_DB_CASSANDRA_COORDINATOR_ID = TMP_DB_CASSANDRA_COORDINATOR_ID;
var SEMATTRS_DB_CASSANDRA_COORDINATOR_DC = TMP_DB_CASSANDRA_COORDINATOR_DC;
var SEMATTRS_DB_HBASE_NAMESPACE = TMP_DB_HBASE_NAMESPACE;
var SEMATTRS_DB_REDIS_DATABASE_INDEX = TMP_DB_REDIS_DATABASE_INDEX;
var SEMATTRS_DB_MONGODB_COLLECTION = TMP_DB_MONGODB_COLLECTION;
var SEMATTRS_DB_SQL_TABLE = TMP_DB_SQL_TABLE;
var SEMATTRS_EXCEPTION_TYPE = TMP_EXCEPTION_TYPE;
var SEMATTRS_EXCEPTION_MESSAGE = TMP_EXCEPTION_MESSAGE;
var SEMATTRS_EXCEPTION_STACKTRACE = TMP_EXCEPTION_STACKTRACE;
var SEMATTRS_EXCEPTION_ESCAPED = TMP_EXCEPTION_ESCAPED;
var SEMATTRS_FAAS_TRIGGER = TMP_FAAS_TRIGGER;
var SEMATTRS_FAAS_EXECUTION = TMP_FAAS_EXECUTION;
var SEMATTRS_FAAS_DOCUMENT_COLLECTION = TMP_FAAS_DOCUMENT_COLLECTION;
var SEMATTRS_FAAS_DOCUMENT_OPERATION = TMP_FAAS_DOCUMENT_OPERATION;
var SEMATTRS_FAAS_DOCUMENT_TIME = TMP_FAAS_DOCUMENT_TIME;
var SEMATTRS_FAAS_DOCUMENT_NAME = TMP_FAAS_DOCUMENT_NAME;
var SEMATTRS_FAAS_TIME = TMP_FAAS_TIME;
var SEMATTRS_FAAS_CRON = TMP_FAAS_CRON;
var SEMATTRS_FAAS_COLDSTART = TMP_FAAS_COLDSTART;
var SEMATTRS_FAAS_INVOKED_NAME = TMP_FAAS_INVOKED_NAME;
var SEMATTRS_FAAS_INVOKED_PROVIDER = TMP_FAAS_INVOKED_PROVIDER;
var SEMATTRS_FAAS_INVOKED_REGION = TMP_FAAS_INVOKED_REGION;
var SEMATTRS_NET_TRANSPORT = TMP_NET_TRANSPORT;
var SEMATTRS_NET_PEER_IP = TMP_NET_PEER_IP;
var SEMATTRS_NET_PEER_PORT = TMP_NET_PEER_PORT;
var SEMATTRS_NET_PEER_NAME = TMP_NET_PEER_NAME;
var SEMATTRS_NET_HOST_IP = TMP_NET_HOST_IP;
var SEMATTRS_NET_HOST_PORT = TMP_NET_HOST_PORT;
var SEMATTRS_NET_HOST_NAME = TMP_NET_HOST_NAME;
var SEMATTRS_NET_HOST_CONNECTION_TYPE = TMP_NET_HOST_CONNECTION_TYPE;
var SEMATTRS_NET_HOST_CONNECTION_SUBTYPE = TMP_NET_HOST_CONNECTION_SUBTYPE;
var SEMATTRS_NET_HOST_CARRIER_NAME = TMP_NET_HOST_CARRIER_NAME;
var SEMATTRS_NET_HOST_CARRIER_MCC = TMP_NET_HOST_CARRIER_MCC;
var SEMATTRS_NET_HOST_CARRIER_MNC = TMP_NET_HOST_CARRIER_MNC;
var SEMATTRS_NET_HOST_CARRIER_ICC = TMP_NET_HOST_CARRIER_ICC;
var SEMATTRS_PEER_SERVICE = TMP_PEER_SERVICE;
var SEMATTRS_ENDUSER_ID = TMP_ENDUSER_ID;
var SEMATTRS_ENDUSER_ROLE = TMP_ENDUSER_ROLE;
var SEMATTRS_ENDUSER_SCOPE = TMP_ENDUSER_SCOPE;
var SEMATTRS_THREAD_ID = TMP_THREAD_ID;
var SEMATTRS_THREAD_NAME = TMP_THREAD_NAME;
var SEMATTRS_CODE_FUNCTION = TMP_CODE_FUNCTION;
var SEMATTRS_CODE_NAMESPACE = TMP_CODE_NAMESPACE;
var SEMATTRS_CODE_FILEPATH = TMP_CODE_FILEPATH;
var SEMATTRS_CODE_LINENO = TMP_CODE_LINENO;
var SEMATTRS_HTTP_METHOD = TMP_HTTP_METHOD;
var SEMATTRS_HTTP_URL = TMP_HTTP_URL;
var SEMATTRS_HTTP_TARGET = TMP_HTTP_TARGET;
var SEMATTRS_HTTP_HOST = TMP_HTTP_HOST;
var SEMATTRS_HTTP_SCHEME = TMP_HTTP_SCHEME;
var SEMATTRS_HTTP_STATUS_CODE = TMP_HTTP_STATUS_CODE;
var SEMATTRS_HTTP_FLAVOR = TMP_HTTP_FLAVOR;
var SEMATTRS_HTTP_USER_AGENT = TMP_HTTP_USER_AGENT;
var SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH = TMP_HTTP_REQUEST_CONTENT_LENGTH;
var SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED = TMP_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED;
var SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH = TMP_HTTP_RESPONSE_CONTENT_LENGTH;
var SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED = TMP_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED;
var SEMATTRS_HTTP_SERVER_NAME = TMP_HTTP_SERVER_NAME;
var SEMATTRS_HTTP_ROUTE = TMP_HTTP_ROUTE;
var SEMATTRS_HTTP_CLIENT_IP = TMP_HTTP_CLIENT_IP;
var SEMATTRS_AWS_DYNAMODB_TABLE_NAMES = TMP_AWS_DYNAMODB_TABLE_NAMES;
var SEMATTRS_AWS_DYNAMODB_CONSUMED_CAPACITY = TMP_AWS_DYNAMODB_CONSUMED_CAPACITY;
var SEMATTRS_AWS_DYNAMODB_ITEM_COLLECTION_METRICS = TMP_AWS_DYNAMODB_ITEM_COLLECTION_METRICS;
var SEMATTRS_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY = TMP_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY;
var SEMATTRS_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY = TMP_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY;
var SEMATTRS_AWS_DYNAMODB_CONSISTENT_READ = TMP_AWS_DYNAMODB_CONSISTENT_READ;
var SEMATTRS_AWS_DYNAMODB_PROJECTION = TMP_AWS_DYNAMODB_PROJECTION;
var SEMATTRS_AWS_DYNAMODB_LIMIT = TMP_AWS_DYNAMODB_LIMIT;
var SEMATTRS_AWS_DYNAMODB_ATTRIBUTES_TO_GET = TMP_AWS_DYNAMODB_ATTRIBUTES_TO_GET;
var SEMATTRS_AWS_DYNAMODB_INDEX_NAME = TMP_AWS_DYNAMODB_INDEX_NAME;
var SEMATTRS_AWS_DYNAMODB_SELECT = TMP_AWS_DYNAMODB_SELECT;
var SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES = TMP_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES;
var SEMATTRS_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES = TMP_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES;
var SEMATTRS_AWS_DYNAMODB_EXCLUSIVE_START_TABLE = TMP_AWS_DYNAMODB_EXCLUSIVE_START_TABLE;
var SEMATTRS_AWS_DYNAMODB_TABLE_COUNT = TMP_AWS_DYNAMODB_TABLE_COUNT;
var SEMATTRS_AWS_DYNAMODB_SCAN_FORWARD = TMP_AWS_DYNAMODB_SCAN_FORWARD;
var SEMATTRS_AWS_DYNAMODB_SEGMENT = TMP_AWS_DYNAMODB_SEGMENT;
var SEMATTRS_AWS_DYNAMODB_TOTAL_SEGMENTS = TMP_AWS_DYNAMODB_TOTAL_SEGMENTS;
var SEMATTRS_AWS_DYNAMODB_COUNT = TMP_AWS_DYNAMODB_COUNT;
var SEMATTRS_AWS_DYNAMODB_SCANNED_COUNT = TMP_AWS_DYNAMODB_SCANNED_COUNT;
var SEMATTRS_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS = TMP_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS;
var SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES = TMP_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES;
var SEMATTRS_MESSAGING_SYSTEM = TMP_MESSAGING_SYSTEM;
var SEMATTRS_MESSAGING_DESTINATION = TMP_MESSAGING_DESTINATION;
var SEMATTRS_MESSAGING_DESTINATION_KIND = TMP_MESSAGING_DESTINATION_KIND;
var SEMATTRS_MESSAGING_TEMP_DESTINATION = TMP_MESSAGING_TEMP_DESTINATION;
var SEMATTRS_MESSAGING_PROTOCOL = TMP_MESSAGING_PROTOCOL;
var SEMATTRS_MESSAGING_PROTOCOL_VERSION = TMP_MESSAGING_PROTOCOL_VERSION;
var SEMATTRS_MESSAGING_URL = TMP_MESSAGING_URL;
var SEMATTRS_MESSAGING_MESSAGE_ID = TMP_MESSAGING_MESSAGE_ID;
var SEMATTRS_MESSAGING_CONVERSATION_ID = TMP_MESSAGING_CONVERSATION_ID;
var SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES = TMP_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES;
var SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES = TMP_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES;
var SEMATTRS_MESSAGING_OPERATION = TMP_MESSAGING_OPERATION;
var SEMATTRS_MESSAGING_CONSUMER_ID = TMP_MESSAGING_CONSUMER_ID;
var SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY = TMP_MESSAGING_RABBITMQ_ROUTING_KEY;
var SEMATTRS_MESSAGING_KAFKA_MESSAGE_KEY = TMP_MESSAGING_KAFKA_MESSAGE_KEY;
var SEMATTRS_MESSAGING_KAFKA_CONSUMER_GROUP = TMP_MESSAGING_KAFKA_CONSUMER_GROUP;
var SEMATTRS_MESSAGING_KAFKA_CLIENT_ID = TMP_MESSAGING_KAFKA_CLIENT_ID;
var SEMATTRS_MESSAGING_KAFKA_PARTITION = TMP_MESSAGING_KAFKA_PARTITION;
var SEMATTRS_MESSAGING_KAFKA_TOMBSTONE = TMP_MESSAGING_KAFKA_TOMBSTONE;
var SEMATTRS_RPC_SYSTEM = TMP_RPC_SYSTEM;
var SEMATTRS_RPC_SERVICE = TMP_RPC_SERVICE;
var SEMATTRS_RPC_METHOD = TMP_RPC_METHOD;
var SEMATTRS_RPC_GRPC_STATUS_CODE = TMP_RPC_GRPC_STATUS_CODE;
var SEMATTRS_RPC_JSONRPC_VERSION = TMP_RPC_JSONRPC_VERSION;
var SEMATTRS_RPC_JSONRPC_REQUEST_ID = TMP_RPC_JSONRPC_REQUEST_ID;
var SEMATTRS_RPC_JSONRPC_ERROR_CODE = TMP_RPC_JSONRPC_ERROR_CODE;
var SEMATTRS_RPC_JSONRPC_ERROR_MESSAGE = TMP_RPC_JSONRPC_ERROR_MESSAGE;
var SEMATTRS_MESSAGE_TYPE = TMP_MESSAGE_TYPE;
var SEMATTRS_MESSAGE_ID = TMP_MESSAGE_ID;
var SEMATTRS_MESSAGE_COMPRESSED_SIZE = TMP_MESSAGE_COMPRESSED_SIZE;
var SEMATTRS_MESSAGE_UNCOMPRESSED_SIZE = TMP_MESSAGE_UNCOMPRESSED_SIZE;
var SemanticAttributes = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_AWS_LAMBDA_INVOKED_ARN,
    TMP_DB_SYSTEM,
    TMP_DB_CONNECTION_STRING,
    TMP_DB_USER,
    TMP_DB_JDBC_DRIVER_CLASSNAME,
    TMP_DB_NAME,
    TMP_DB_STATEMENT,
    TMP_DB_OPERATION,
    TMP_DB_MSSQL_INSTANCE_NAME,
    TMP_DB_CASSANDRA_KEYSPACE,
    TMP_DB_CASSANDRA_PAGE_SIZE,
    TMP_DB_CASSANDRA_CONSISTENCY_LEVEL,
    TMP_DB_CASSANDRA_TABLE,
    TMP_DB_CASSANDRA_IDEMPOTENCE,
    TMP_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT,
    TMP_DB_CASSANDRA_COORDINATOR_ID,
    TMP_DB_CASSANDRA_COORDINATOR_DC,
    TMP_DB_HBASE_NAMESPACE,
    TMP_DB_REDIS_DATABASE_INDEX,
    TMP_DB_MONGODB_COLLECTION,
    TMP_DB_SQL_TABLE,
    TMP_EXCEPTION_TYPE,
    TMP_EXCEPTION_MESSAGE,
    TMP_EXCEPTION_STACKTRACE,
    TMP_EXCEPTION_ESCAPED,
    TMP_FAAS_TRIGGER,
    TMP_FAAS_EXECUTION,
    TMP_FAAS_DOCUMENT_COLLECTION,
    TMP_FAAS_DOCUMENT_OPERATION,
    TMP_FAAS_DOCUMENT_TIME,
    TMP_FAAS_DOCUMENT_NAME,
    TMP_FAAS_TIME,
    TMP_FAAS_CRON,
    TMP_FAAS_COLDSTART,
    TMP_FAAS_INVOKED_NAME,
    TMP_FAAS_INVOKED_PROVIDER,
    TMP_FAAS_INVOKED_REGION,
    TMP_NET_TRANSPORT,
    TMP_NET_PEER_IP,
    TMP_NET_PEER_PORT,
    TMP_NET_PEER_NAME,
    TMP_NET_HOST_IP,
    TMP_NET_HOST_PORT,
    TMP_NET_HOST_NAME,
    TMP_NET_HOST_CONNECTION_TYPE,
    TMP_NET_HOST_CONNECTION_SUBTYPE,
    TMP_NET_HOST_CARRIER_NAME,
    TMP_NET_HOST_CARRIER_MCC,
    TMP_NET_HOST_CARRIER_MNC,
    TMP_NET_HOST_CARRIER_ICC,
    TMP_PEER_SERVICE,
    TMP_ENDUSER_ID,
    TMP_ENDUSER_ROLE,
    TMP_ENDUSER_SCOPE,
    TMP_THREAD_ID,
    TMP_THREAD_NAME,
    TMP_CODE_FUNCTION,
    TMP_CODE_NAMESPACE,
    TMP_CODE_FILEPATH,
    TMP_CODE_LINENO,
    TMP_HTTP_METHOD,
    TMP_HTTP_URL,
    TMP_HTTP_TARGET,
    TMP_HTTP_HOST,
    TMP_HTTP_SCHEME,
    TMP_HTTP_STATUS_CODE,
    TMP_HTTP_FLAVOR,
    TMP_HTTP_USER_AGENT,
    TMP_HTTP_REQUEST_CONTENT_LENGTH,
    TMP_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED,
    TMP_HTTP_RESPONSE_CONTENT_LENGTH,
    TMP_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED,
    TMP_HTTP_SERVER_NAME,
    TMP_HTTP_ROUTE,
    TMP_HTTP_CLIENT_IP,
    TMP_AWS_DYNAMODB_TABLE_NAMES,
    TMP_AWS_DYNAMODB_CONSUMED_CAPACITY,
    TMP_AWS_DYNAMODB_ITEM_COLLECTION_METRICS,
    TMP_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY,
    TMP_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY,
    TMP_AWS_DYNAMODB_CONSISTENT_READ,
    TMP_AWS_DYNAMODB_PROJECTION,
    TMP_AWS_DYNAMODB_LIMIT,
    TMP_AWS_DYNAMODB_ATTRIBUTES_TO_GET,
    TMP_AWS_DYNAMODB_INDEX_NAME,
    TMP_AWS_DYNAMODB_SELECT,
    TMP_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES,
    TMP_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES,
    TMP_AWS_DYNAMODB_EXCLUSIVE_START_TABLE,
    TMP_AWS_DYNAMODB_TABLE_COUNT,
    TMP_AWS_DYNAMODB_SCAN_FORWARD,
    TMP_AWS_DYNAMODB_SEGMENT,
    TMP_AWS_DYNAMODB_TOTAL_SEGMENTS,
    TMP_AWS_DYNAMODB_COUNT,
    TMP_AWS_DYNAMODB_SCANNED_COUNT,
    TMP_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS,
    TMP_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES,
    TMP_MESSAGING_SYSTEM,
    TMP_MESSAGING_DESTINATION,
    TMP_MESSAGING_DESTINATION_KIND,
    TMP_MESSAGING_TEMP_DESTINATION,
    TMP_MESSAGING_PROTOCOL,
    TMP_MESSAGING_PROTOCOL_VERSION,
    TMP_MESSAGING_URL,
    TMP_MESSAGING_MESSAGE_ID,
    TMP_MESSAGING_CONVERSATION_ID,
    TMP_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES,
    TMP_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES,
    TMP_MESSAGING_OPERATION,
    TMP_MESSAGING_CONSUMER_ID,
    TMP_MESSAGING_RABBITMQ_ROUTING_KEY,
    TMP_MESSAGING_KAFKA_MESSAGE_KEY,
    TMP_MESSAGING_KAFKA_CONSUMER_GROUP,
    TMP_MESSAGING_KAFKA_CLIENT_ID,
    TMP_MESSAGING_KAFKA_PARTITION,
    TMP_MESSAGING_KAFKA_TOMBSTONE,
    TMP_RPC_SYSTEM,
    TMP_RPC_SERVICE,
    TMP_RPC_METHOD,
    TMP_RPC_GRPC_STATUS_CODE,
    TMP_RPC_JSONRPC_VERSION,
    TMP_RPC_JSONRPC_REQUEST_ID,
    TMP_RPC_JSONRPC_ERROR_CODE,
    TMP_RPC_JSONRPC_ERROR_MESSAGE,
    TMP_MESSAGE_TYPE,
    TMP_MESSAGE_ID,
    TMP_MESSAGE_COMPRESSED_SIZE,
    TMP_MESSAGE_UNCOMPRESSED_SIZE
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for DbSystemValues enum definition
 *
 * An identifier for the database management system (DBMS) product being used. See below for a list of well-known identifiers.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_DBSYSTEMVALUES_OTHER_SQL = 'other_sql';
var TMP_DBSYSTEMVALUES_MSSQL = 'mssql';
var TMP_DBSYSTEMVALUES_MYSQL = 'mysql';
var TMP_DBSYSTEMVALUES_ORACLE = 'oracle';
var TMP_DBSYSTEMVALUES_DB2 = 'db2';
var TMP_DBSYSTEMVALUES_POSTGRESQL = 'postgresql';
var TMP_DBSYSTEMVALUES_REDSHIFT = 'redshift';
var TMP_DBSYSTEMVALUES_HIVE = 'hive';
var TMP_DBSYSTEMVALUES_CLOUDSCAPE = 'cloudscape';
var TMP_DBSYSTEMVALUES_HSQLDB = 'hsqldb';
var TMP_DBSYSTEMVALUES_PROGRESS = 'progress';
var TMP_DBSYSTEMVALUES_MAXDB = 'maxdb';
var TMP_DBSYSTEMVALUES_HANADB = 'hanadb';
var TMP_DBSYSTEMVALUES_INGRES = 'ingres';
var TMP_DBSYSTEMVALUES_FIRSTSQL = 'firstsql';
var TMP_DBSYSTEMVALUES_EDB = 'edb';
var TMP_DBSYSTEMVALUES_CACHE = 'cache';
var TMP_DBSYSTEMVALUES_ADABAS = 'adabas';
var TMP_DBSYSTEMVALUES_FIREBIRD = 'firebird';
var TMP_DBSYSTEMVALUES_DERBY = 'derby';
var TMP_DBSYSTEMVALUES_FILEMAKER = 'filemaker';
var TMP_DBSYSTEMVALUES_INFORMIX = 'informix';
var TMP_DBSYSTEMVALUES_INSTANTDB = 'instantdb';
var TMP_DBSYSTEMVALUES_INTERBASE = 'interbase';
var TMP_DBSYSTEMVALUES_MARIADB = 'mariadb';
var TMP_DBSYSTEMVALUES_NETEZZA = 'netezza';
var TMP_DBSYSTEMVALUES_PERVASIVE = 'pervasive';
var TMP_DBSYSTEMVALUES_POINTBASE = 'pointbase';
var TMP_DBSYSTEMVALUES_SQLITE = 'sqlite';
var TMP_DBSYSTEMVALUES_SYBASE = 'sybase';
var TMP_DBSYSTEMVALUES_TERADATA = 'teradata';
var TMP_DBSYSTEMVALUES_VERTICA = 'vertica';
var TMP_DBSYSTEMVALUES_H2 = 'h2';
var TMP_DBSYSTEMVALUES_COLDFUSION = 'coldfusion';
var TMP_DBSYSTEMVALUES_CASSANDRA = 'cassandra';
var TMP_DBSYSTEMVALUES_HBASE = 'hbase';
var TMP_DBSYSTEMVALUES_MONGODB = 'mongodb';
var TMP_DBSYSTEMVALUES_REDIS = 'redis';
var TMP_DBSYSTEMVALUES_COUCHBASE = 'couchbase';
var TMP_DBSYSTEMVALUES_COUCHDB = 'couchdb';
var TMP_DBSYSTEMVALUES_COSMOSDB = 'cosmosdb';
var TMP_DBSYSTEMVALUES_DYNAMODB = 'dynamodb';
var TMP_DBSYSTEMVALUES_NEO4J = 'neo4j';
var TMP_DBSYSTEMVALUES_GEODE = 'geode';
var TMP_DBSYSTEMVALUES_ELASTICSEARCH = 'elasticsearch';
var TMP_DBSYSTEMVALUES_MEMCACHED = 'memcached';
var TMP_DBSYSTEMVALUES_COCKROACHDB = 'cockroachdb';
var DBSYSTEMVALUES_OTHER_SQL = TMP_DBSYSTEMVALUES_OTHER_SQL;
var DBSYSTEMVALUES_MSSQL = TMP_DBSYSTEMVALUES_MSSQL;
var DBSYSTEMVALUES_MYSQL = TMP_DBSYSTEMVALUES_MYSQL;
var DBSYSTEMVALUES_ORACLE = TMP_DBSYSTEMVALUES_ORACLE;
var DBSYSTEMVALUES_DB2 = TMP_DBSYSTEMVALUES_DB2;
var DBSYSTEMVALUES_POSTGRESQL = TMP_DBSYSTEMVALUES_POSTGRESQL;
var DBSYSTEMVALUES_REDSHIFT = TMP_DBSYSTEMVALUES_REDSHIFT;
var DBSYSTEMVALUES_HIVE = TMP_DBSYSTEMVALUES_HIVE;
var DBSYSTEMVALUES_CLOUDSCAPE = TMP_DBSYSTEMVALUES_CLOUDSCAPE;
var DBSYSTEMVALUES_HSQLDB = TMP_DBSYSTEMVALUES_HSQLDB;
var DBSYSTEMVALUES_PROGRESS = TMP_DBSYSTEMVALUES_PROGRESS;
var DBSYSTEMVALUES_MAXDB = TMP_DBSYSTEMVALUES_MAXDB;
var DBSYSTEMVALUES_HANADB = TMP_DBSYSTEMVALUES_HANADB;
var DBSYSTEMVALUES_INGRES = TMP_DBSYSTEMVALUES_INGRES;
var DBSYSTEMVALUES_FIRSTSQL = TMP_DBSYSTEMVALUES_FIRSTSQL;
var DBSYSTEMVALUES_EDB = TMP_DBSYSTEMVALUES_EDB;
var DBSYSTEMVALUES_CACHE = TMP_DBSYSTEMVALUES_CACHE;
var DBSYSTEMVALUES_ADABAS = TMP_DBSYSTEMVALUES_ADABAS;
var DBSYSTEMVALUES_FIREBIRD = TMP_DBSYSTEMVALUES_FIREBIRD;
var DBSYSTEMVALUES_DERBY = TMP_DBSYSTEMVALUES_DERBY;
var DBSYSTEMVALUES_FILEMAKER = TMP_DBSYSTEMVALUES_FILEMAKER;
var DBSYSTEMVALUES_INFORMIX = TMP_DBSYSTEMVALUES_INFORMIX;
var DBSYSTEMVALUES_INSTANTDB = TMP_DBSYSTEMVALUES_INSTANTDB;
var DBSYSTEMVALUES_INTERBASE = TMP_DBSYSTEMVALUES_INTERBASE;
var DBSYSTEMVALUES_MARIADB = TMP_DBSYSTEMVALUES_MARIADB;
var DBSYSTEMVALUES_NETEZZA = TMP_DBSYSTEMVALUES_NETEZZA;
var DBSYSTEMVALUES_PERVASIVE = TMP_DBSYSTEMVALUES_PERVASIVE;
var DBSYSTEMVALUES_POINTBASE = TMP_DBSYSTEMVALUES_POINTBASE;
var DBSYSTEMVALUES_SQLITE = TMP_DBSYSTEMVALUES_SQLITE;
var DBSYSTEMVALUES_SYBASE = TMP_DBSYSTEMVALUES_SYBASE;
var DBSYSTEMVALUES_TERADATA = TMP_DBSYSTEMVALUES_TERADATA;
var DBSYSTEMVALUES_VERTICA = TMP_DBSYSTEMVALUES_VERTICA;
var DBSYSTEMVALUES_H2 = TMP_DBSYSTEMVALUES_H2;
var DBSYSTEMVALUES_COLDFUSION = TMP_DBSYSTEMVALUES_COLDFUSION;
var DBSYSTEMVALUES_CASSANDRA = TMP_DBSYSTEMVALUES_CASSANDRA;
var DBSYSTEMVALUES_HBASE = TMP_DBSYSTEMVALUES_HBASE;
var DBSYSTEMVALUES_MONGODB = TMP_DBSYSTEMVALUES_MONGODB;
var DBSYSTEMVALUES_REDIS = TMP_DBSYSTEMVALUES_REDIS;
var DBSYSTEMVALUES_COUCHBASE = TMP_DBSYSTEMVALUES_COUCHBASE;
var DBSYSTEMVALUES_COUCHDB = TMP_DBSYSTEMVALUES_COUCHDB;
var DBSYSTEMVALUES_COSMOSDB = TMP_DBSYSTEMVALUES_COSMOSDB;
var DBSYSTEMVALUES_DYNAMODB = TMP_DBSYSTEMVALUES_DYNAMODB;
var DBSYSTEMVALUES_NEO4J = TMP_DBSYSTEMVALUES_NEO4J;
var DBSYSTEMVALUES_GEODE = TMP_DBSYSTEMVALUES_GEODE;
var DBSYSTEMVALUES_ELASTICSEARCH = TMP_DBSYSTEMVALUES_ELASTICSEARCH;
var DBSYSTEMVALUES_MEMCACHED = TMP_DBSYSTEMVALUES_MEMCACHED;
var DBSYSTEMVALUES_COCKROACHDB = TMP_DBSYSTEMVALUES_COCKROACHDB;
var DbSystemValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_DBSYSTEMVALUES_OTHER_SQL,
    TMP_DBSYSTEMVALUES_MSSQL,
    TMP_DBSYSTEMVALUES_MYSQL,
    TMP_DBSYSTEMVALUES_ORACLE,
    TMP_DBSYSTEMVALUES_DB2,
    TMP_DBSYSTEMVALUES_POSTGRESQL,
    TMP_DBSYSTEMVALUES_REDSHIFT,
    TMP_DBSYSTEMVALUES_HIVE,
    TMP_DBSYSTEMVALUES_CLOUDSCAPE,
    TMP_DBSYSTEMVALUES_HSQLDB,
    TMP_DBSYSTEMVALUES_PROGRESS,
    TMP_DBSYSTEMVALUES_MAXDB,
    TMP_DBSYSTEMVALUES_HANADB,
    TMP_DBSYSTEMVALUES_INGRES,
    TMP_DBSYSTEMVALUES_FIRSTSQL,
    TMP_DBSYSTEMVALUES_EDB,
    TMP_DBSYSTEMVALUES_CACHE,
    TMP_DBSYSTEMVALUES_ADABAS,
    TMP_DBSYSTEMVALUES_FIREBIRD,
    TMP_DBSYSTEMVALUES_DERBY,
    TMP_DBSYSTEMVALUES_FILEMAKER,
    TMP_DBSYSTEMVALUES_INFORMIX,
    TMP_DBSYSTEMVALUES_INSTANTDB,
    TMP_DBSYSTEMVALUES_INTERBASE,
    TMP_DBSYSTEMVALUES_MARIADB,
    TMP_DBSYSTEMVALUES_NETEZZA,
    TMP_DBSYSTEMVALUES_PERVASIVE,
    TMP_DBSYSTEMVALUES_POINTBASE,
    TMP_DBSYSTEMVALUES_SQLITE,
    TMP_DBSYSTEMVALUES_SYBASE,
    TMP_DBSYSTEMVALUES_TERADATA,
    TMP_DBSYSTEMVALUES_VERTICA,
    TMP_DBSYSTEMVALUES_H2,
    TMP_DBSYSTEMVALUES_COLDFUSION,
    TMP_DBSYSTEMVALUES_CASSANDRA,
    TMP_DBSYSTEMVALUES_HBASE,
    TMP_DBSYSTEMVALUES_MONGODB,
    TMP_DBSYSTEMVALUES_REDIS,
    TMP_DBSYSTEMVALUES_COUCHBASE,
    TMP_DBSYSTEMVALUES_COUCHDB,
    TMP_DBSYSTEMVALUES_COSMOSDB,
    TMP_DBSYSTEMVALUES_DYNAMODB,
    TMP_DBSYSTEMVALUES_NEO4J,
    TMP_DBSYSTEMVALUES_GEODE,
    TMP_DBSYSTEMVALUES_ELASTICSEARCH,
    TMP_DBSYSTEMVALUES_MEMCACHED,
    TMP_DBSYSTEMVALUES_COCKROACHDB
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for DbCassandraConsistencyLevelValues enum definition
 *
 * The consistency level of the query. Based on consistency values from [CQL](https://docs.datastax.com/en/cassandra-oss/3.0/cassandra/dml/dmlConfigConsistency.html).
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ALL = 'all';
var TMP_DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM = 'each_quorum';
var TMP_DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM = 'quorum';
var TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM = 'local_quorum';
var TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ONE = 'one';
var TMP_DBCASSANDRACONSISTENCYLEVELVALUES_TWO = 'two';
var TMP_DBCASSANDRACONSISTENCYLEVELVALUES_THREE = 'three';
var TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE = 'local_one';
var TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ANY = 'any';
var TMP_DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL = 'serial';
var TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL = 'local_serial';
var DBCASSANDRACONSISTENCYLEVELVALUES_ALL = TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ALL;
var DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM = TMP_DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM;
var DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM = TMP_DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM;
var DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM = TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM;
var DBCASSANDRACONSISTENCYLEVELVALUES_ONE = TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ONE;
var DBCASSANDRACONSISTENCYLEVELVALUES_TWO = TMP_DBCASSANDRACONSISTENCYLEVELVALUES_TWO;
var DBCASSANDRACONSISTENCYLEVELVALUES_THREE = TMP_DBCASSANDRACONSISTENCYLEVELVALUES_THREE;
var DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE = TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE;
var DBCASSANDRACONSISTENCYLEVELVALUES_ANY = TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ANY;
var DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL = TMP_DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL;
var DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL = TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL;
var DbCassandraConsistencyLevelValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ALL,
    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM,
    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM,
    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM,
    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ONE,
    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_TWO,
    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_THREE,
    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE,
    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_ANY,
    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL,
    TMP_DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for FaasTriggerValues enum definition
 *
 * Type of the trigger on which the function is executed.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_FAASTRIGGERVALUES_DATASOURCE = 'datasource';
var TMP_FAASTRIGGERVALUES_HTTP = 'http';
var TMP_FAASTRIGGERVALUES_PUBSUB = 'pubsub';
var TMP_FAASTRIGGERVALUES_TIMER = 'timer';
var TMP_FAASTRIGGERVALUES_OTHER = 'other';
var FAASTRIGGERVALUES_DATASOURCE = TMP_FAASTRIGGERVALUES_DATASOURCE;
var FAASTRIGGERVALUES_HTTP = TMP_FAASTRIGGERVALUES_HTTP;
var FAASTRIGGERVALUES_PUBSUB = TMP_FAASTRIGGERVALUES_PUBSUB;
var FAASTRIGGERVALUES_TIMER = TMP_FAASTRIGGERVALUES_TIMER;
var FAASTRIGGERVALUES_OTHER = TMP_FAASTRIGGERVALUES_OTHER;
var FaasTriggerValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_FAASTRIGGERVALUES_DATASOURCE,
    TMP_FAASTRIGGERVALUES_HTTP,
    TMP_FAASTRIGGERVALUES_PUBSUB,
    TMP_FAASTRIGGERVALUES_TIMER,
    TMP_FAASTRIGGERVALUES_OTHER
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for FaasDocumentOperationValues enum definition
 *
 * Describes the type of the operation that was performed on the data.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_FAASDOCUMENTOPERATIONVALUES_INSERT = 'insert';
var TMP_FAASDOCUMENTOPERATIONVALUES_EDIT = 'edit';
var TMP_FAASDOCUMENTOPERATIONVALUES_DELETE = 'delete';
var FAASDOCUMENTOPERATIONVALUES_INSERT = TMP_FAASDOCUMENTOPERATIONVALUES_INSERT;
var FAASDOCUMENTOPERATIONVALUES_EDIT = TMP_FAASDOCUMENTOPERATIONVALUES_EDIT;
var FAASDOCUMENTOPERATIONVALUES_DELETE = TMP_FAASDOCUMENTOPERATIONVALUES_DELETE;
var FaasDocumentOperationValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_FAASDOCUMENTOPERATIONVALUES_INSERT,
    TMP_FAASDOCUMENTOPERATIONVALUES_EDIT,
    TMP_FAASDOCUMENTOPERATIONVALUES_DELETE
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for FaasInvokedProviderValues enum definition
 *
 * The cloud provider of the invoked function.
 *
 * Note: SHOULD be equal to the `cloud.provider` resource attribute of the invoked function.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD = 'alibaba_cloud';
var TMP_FAASINVOKEDPROVIDERVALUES_AWS = 'aws';
var TMP_FAASINVOKEDPROVIDERVALUES_AZURE = 'azure';
var TMP_FAASINVOKEDPROVIDERVALUES_GCP = 'gcp';
var FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD = TMP_FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD;
var FAASINVOKEDPROVIDERVALUES_AWS = TMP_FAASINVOKEDPROVIDERVALUES_AWS;
var FAASINVOKEDPROVIDERVALUES_AZURE = TMP_FAASINVOKEDPROVIDERVALUES_AZURE;
var FAASINVOKEDPROVIDERVALUES_GCP = TMP_FAASINVOKEDPROVIDERVALUES_GCP;
var FaasInvokedProviderValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD,
    TMP_FAASINVOKEDPROVIDERVALUES_AWS,
    TMP_FAASINVOKEDPROVIDERVALUES_AZURE,
    TMP_FAASINVOKEDPROVIDERVALUES_GCP
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for NetTransportValues enum definition
 *
 * Transport protocol used. See note below.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_NETTRANSPORTVALUES_IP_TCP = 'ip_tcp';
var TMP_NETTRANSPORTVALUES_IP_UDP = 'ip_udp';
var TMP_NETTRANSPORTVALUES_IP = 'ip';
var TMP_NETTRANSPORTVALUES_UNIX = 'unix';
var TMP_NETTRANSPORTVALUES_PIPE = 'pipe';
var TMP_NETTRANSPORTVALUES_INPROC = 'inproc';
var TMP_NETTRANSPORTVALUES_OTHER = 'other';
var NETTRANSPORTVALUES_IP_TCP = TMP_NETTRANSPORTVALUES_IP_TCP;
var NETTRANSPORTVALUES_IP_UDP = TMP_NETTRANSPORTVALUES_IP_UDP;
var NETTRANSPORTVALUES_IP = TMP_NETTRANSPORTVALUES_IP;
var NETTRANSPORTVALUES_UNIX = TMP_NETTRANSPORTVALUES_UNIX;
var NETTRANSPORTVALUES_PIPE = TMP_NETTRANSPORTVALUES_PIPE;
var NETTRANSPORTVALUES_INPROC = TMP_NETTRANSPORTVALUES_INPROC;
var NETTRANSPORTVALUES_OTHER = TMP_NETTRANSPORTVALUES_OTHER;
var NetTransportValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_NETTRANSPORTVALUES_IP_TCP,
    TMP_NETTRANSPORTVALUES_IP_UDP,
    TMP_NETTRANSPORTVALUES_IP,
    TMP_NETTRANSPORTVALUES_UNIX,
    TMP_NETTRANSPORTVALUES_PIPE,
    TMP_NETTRANSPORTVALUES_INPROC,
    TMP_NETTRANSPORTVALUES_OTHER
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for NetHostConnectionTypeValues enum definition
 *
 * The internet connection type currently being used by the host.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_NETHOSTCONNECTIONTYPEVALUES_WIFI = 'wifi';
var TMP_NETHOSTCONNECTIONTYPEVALUES_WIRED = 'wired';
var TMP_NETHOSTCONNECTIONTYPEVALUES_CELL = 'cell';
var TMP_NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE = 'unavailable';
var TMP_NETHOSTCONNECTIONTYPEVALUES_UNKNOWN = 'unknown';
var NETHOSTCONNECTIONTYPEVALUES_WIFI = TMP_NETHOSTCONNECTIONTYPEVALUES_WIFI;
var NETHOSTCONNECTIONTYPEVALUES_WIRED = TMP_NETHOSTCONNECTIONTYPEVALUES_WIRED;
var NETHOSTCONNECTIONTYPEVALUES_CELL = TMP_NETHOSTCONNECTIONTYPEVALUES_CELL;
var NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE = TMP_NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE;
var NETHOSTCONNECTIONTYPEVALUES_UNKNOWN = TMP_NETHOSTCONNECTIONTYPEVALUES_UNKNOWN;
var NetHostConnectionTypeValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_NETHOSTCONNECTIONTYPEVALUES_WIFI,
    TMP_NETHOSTCONNECTIONTYPEVALUES_WIRED,
    TMP_NETHOSTCONNECTIONTYPEVALUES_CELL,
    TMP_NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE,
    TMP_NETHOSTCONNECTIONTYPEVALUES_UNKNOWN
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for NetHostConnectionSubtypeValues enum definition
 *
 * This describes more details regarding the connection.type. It may be the type of cell technology connection, but it could be used for describing details about a wifi connection.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_GPRS = 'gprs';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EDGE = 'edge';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_UMTS = 'umts';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_CDMA = 'cdma';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0 = 'evdo_0';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A = 'evdo_a';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT = 'cdma2000_1xrtt';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA = 'hsdpa';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA = 'hsupa';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSPA = 'hspa';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_IDEN = 'iden';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B = 'evdo_b';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_LTE = 'lte';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD = 'ehrpd';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP = 'hspap';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_GSM = 'gsm';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA = 'td_scdma';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN = 'iwlan';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_NR = 'nr';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA = 'nrnsa';
var TMP_NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA = 'lte_ca';
var NETHOSTCONNECTIONSUBTYPEVALUES_GPRS = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_GPRS;
var NETHOSTCONNECTIONSUBTYPEVALUES_EDGE = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EDGE;
var NETHOSTCONNECTIONSUBTYPEVALUES_UMTS = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_UMTS;
var NETHOSTCONNECTIONSUBTYPEVALUES_CDMA = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_CDMA;
var NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0 = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0;
var NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A;
var NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT;
var NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA;
var NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA;
var NETHOSTCONNECTIONSUBTYPEVALUES_HSPA = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSPA;
var NETHOSTCONNECTIONSUBTYPEVALUES_IDEN = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_IDEN;
var NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B;
var NETHOSTCONNECTIONSUBTYPEVALUES_LTE = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_LTE;
var NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD;
var NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP;
var NETHOSTCONNECTIONSUBTYPEVALUES_GSM = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_GSM;
var NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA;
var NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN;
var NETHOSTCONNECTIONSUBTYPEVALUES_NR = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_NR;
var NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA;
var NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA = TMP_NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA;
var NetHostConnectionSubtypeValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_GPRS,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EDGE,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_UMTS,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_CDMA,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSPA,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_IDEN,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_LTE,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_GSM,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_NR,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA,
    TMP_NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for HttpFlavorValues enum definition
 *
 * Kind of HTTP protocol used.
 *
 * Note: If `net.transport` is not specified, it can be assumed to be `IP.TCP` except if `http.flavor` is `QUIC`, in which case `IP.UDP` is assumed.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_HTTPFLAVORVALUES_HTTP_1_0 = '1.0';
var TMP_HTTPFLAVORVALUES_HTTP_1_1 = '1.1';
var TMP_HTTPFLAVORVALUES_HTTP_2_0 = '2.0';
var TMP_HTTPFLAVORVALUES_SPDY = 'SPDY';
var TMP_HTTPFLAVORVALUES_QUIC = 'QUIC';
var HTTPFLAVORVALUES_HTTP_1_0 = TMP_HTTPFLAVORVALUES_HTTP_1_0;
var HTTPFLAVORVALUES_HTTP_1_1 = TMP_HTTPFLAVORVALUES_HTTP_1_1;
var HTTPFLAVORVALUES_HTTP_2_0 = TMP_HTTPFLAVORVALUES_HTTP_2_0;
var HTTPFLAVORVALUES_SPDY = TMP_HTTPFLAVORVALUES_SPDY;
var HTTPFLAVORVALUES_QUIC = TMP_HTTPFLAVORVALUES_QUIC;
var HttpFlavorValues = {
    HTTP_1_0: TMP_HTTPFLAVORVALUES_HTTP_1_0,
    HTTP_1_1: TMP_HTTPFLAVORVALUES_HTTP_1_1,
    HTTP_2_0: TMP_HTTPFLAVORVALUES_HTTP_2_0,
    SPDY: TMP_HTTPFLAVORVALUES_SPDY,
    QUIC: TMP_HTTPFLAVORVALUES_QUIC
};
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for MessagingDestinationKindValues enum definition
 *
 * The kind of message destination.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_MESSAGINGDESTINATIONKINDVALUES_QUEUE = 'queue';
var TMP_MESSAGINGDESTINATIONKINDVALUES_TOPIC = 'topic';
var MESSAGINGDESTINATIONKINDVALUES_QUEUE = TMP_MESSAGINGDESTINATIONKINDVALUES_QUEUE;
var MESSAGINGDESTINATIONKINDVALUES_TOPIC = TMP_MESSAGINGDESTINATIONKINDVALUES_TOPIC;
var MessagingDestinationKindValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_MESSAGINGDESTINATIONKINDVALUES_QUEUE,
    TMP_MESSAGINGDESTINATIONKINDVALUES_TOPIC
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for MessagingOperationValues enum definition
 *
 * A string identifying the kind of message consumption as defined in the [Operation names](#operation-names) section above. If the operation is &#34;send&#34;, this attribute MUST NOT be set, since the operation can be inferred from the span kind in that case.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_MESSAGINGOPERATIONVALUES_RECEIVE = 'receive';
var TMP_MESSAGINGOPERATIONVALUES_PROCESS = 'process';
var MESSAGINGOPERATIONVALUES_RECEIVE = TMP_MESSAGINGOPERATIONVALUES_RECEIVE;
var MESSAGINGOPERATIONVALUES_PROCESS = TMP_MESSAGINGOPERATIONVALUES_PROCESS;
var MessagingOperationValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_MESSAGINGOPERATIONVALUES_RECEIVE,
    TMP_MESSAGINGOPERATIONVALUES_PROCESS
]);
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for RpcGrpcStatusCodeValues enum definition
 *
 * The [numeric status code](https://github.com/grpc/grpc/blob/v1.33.2/doc/statuscodes.md) of the gRPC request.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_RPCGRPCSTATUSCODEVALUES_OK = 0;
var TMP_RPCGRPCSTATUSCODEVALUES_CANCELLED = 1;
var TMP_RPCGRPCSTATUSCODEVALUES_UNKNOWN = 2;
var TMP_RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT = 3;
var TMP_RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED = 4;
var TMP_RPCGRPCSTATUSCODEVALUES_NOT_FOUND = 5;
var TMP_RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS = 6;
var TMP_RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED = 7;
var TMP_RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED = 8;
var TMP_RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION = 9;
var TMP_RPCGRPCSTATUSCODEVALUES_ABORTED = 10;
var TMP_RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE = 11;
var TMP_RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED = 12;
var TMP_RPCGRPCSTATUSCODEVALUES_INTERNAL = 13;
var TMP_RPCGRPCSTATUSCODEVALUES_UNAVAILABLE = 14;
var TMP_RPCGRPCSTATUSCODEVALUES_DATA_LOSS = 15;
var TMP_RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED = 16;
var RPCGRPCSTATUSCODEVALUES_OK = TMP_RPCGRPCSTATUSCODEVALUES_OK;
var RPCGRPCSTATUSCODEVALUES_CANCELLED = TMP_RPCGRPCSTATUSCODEVALUES_CANCELLED;
var RPCGRPCSTATUSCODEVALUES_UNKNOWN = TMP_RPCGRPCSTATUSCODEVALUES_UNKNOWN;
var RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT = TMP_RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT;
var RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED = TMP_RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED;
var RPCGRPCSTATUSCODEVALUES_NOT_FOUND = TMP_RPCGRPCSTATUSCODEVALUES_NOT_FOUND;
var RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS = TMP_RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS;
var RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED = TMP_RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED;
var RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED = TMP_RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED;
var RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION = TMP_RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION;
var RPCGRPCSTATUSCODEVALUES_ABORTED = TMP_RPCGRPCSTATUSCODEVALUES_ABORTED;
var RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE = TMP_RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE;
var RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED = TMP_RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED;
var RPCGRPCSTATUSCODEVALUES_INTERNAL = TMP_RPCGRPCSTATUSCODEVALUES_INTERNAL;
var RPCGRPCSTATUSCODEVALUES_UNAVAILABLE = TMP_RPCGRPCSTATUSCODEVALUES_UNAVAILABLE;
var RPCGRPCSTATUSCODEVALUES_DATA_LOSS = TMP_RPCGRPCSTATUSCODEVALUES_DATA_LOSS;
var RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED = TMP_RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED;
var RpcGrpcStatusCodeValues = {
    OK: TMP_RPCGRPCSTATUSCODEVALUES_OK,
    CANCELLED: TMP_RPCGRPCSTATUSCODEVALUES_CANCELLED,
    UNKNOWN: TMP_RPCGRPCSTATUSCODEVALUES_UNKNOWN,
    INVALID_ARGUMENT: TMP_RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT,
    DEADLINE_EXCEEDED: TMP_RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED,
    NOT_FOUND: TMP_RPCGRPCSTATUSCODEVALUES_NOT_FOUND,
    ALREADY_EXISTS: TMP_RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS,
    PERMISSION_DENIED: TMP_RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED,
    RESOURCE_EXHAUSTED: TMP_RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED,
    FAILED_PRECONDITION: TMP_RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION,
    ABORTED: TMP_RPCGRPCSTATUSCODEVALUES_ABORTED,
    OUT_OF_RANGE: TMP_RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE,
    UNIMPLEMENTED: TMP_RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED,
    INTERNAL: TMP_RPCGRPCSTATUSCODEVALUES_INTERNAL,
    UNAVAILABLE: TMP_RPCGRPCSTATUSCODEVALUES_UNAVAILABLE,
    DATA_LOSS: TMP_RPCGRPCSTATUSCODEVALUES_DATA_LOSS,
    UNAUTHENTICATED: TMP_RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED
};
/* ----------------------------------------------------------------------------------------------------------
 * Constant values for MessageTypeValues enum definition
 *
 * Whether this is a received or sent message.
 * ---------------------------------------------------------------------------------------------------------- */ // Temporary local constants to assign to the individual exports and the namespaced version
// Required to avoid the namespace exports using the unminifiable export names for some package types
var TMP_MESSAGETYPEVALUES_SENT = 'SENT';
var TMP_MESSAGETYPEVALUES_RECEIVED = 'RECEIVED';
var MESSAGETYPEVALUES_SENT = TMP_MESSAGETYPEVALUES_SENT;
var MESSAGETYPEVALUES_RECEIVED = TMP_MESSAGETYPEVALUES_RECEIVED;
var MessageTypeValues = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$internal$2f$utils$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["createConstMap"])([
    TMP_MESSAGETYPEVALUES_SENT,
    TMP_MESSAGETYPEVALUES_RECEIVED
]); //# sourceMappingURL=SemanticAttributes.js.map
}),
"[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/index.js [instrumentation] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "DBCASSANDRACONSISTENCYLEVELVALUES_ALL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBCASSANDRACONSISTENCYLEVELVALUES_ALL"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_ANY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBCASSANDRACONSISTENCYLEVELVALUES_ANY"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_ONE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBCASSANDRACONSISTENCYLEVELVALUES_ONE"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_THREE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBCASSANDRACONSISTENCYLEVELVALUES_THREE"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_TWO": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBCASSANDRACONSISTENCYLEVELVALUES_TWO"],
    "DBSYSTEMVALUES_ADABAS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_ADABAS"],
    "DBSYSTEMVALUES_CACHE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_CACHE"],
    "DBSYSTEMVALUES_CASSANDRA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_CASSANDRA"],
    "DBSYSTEMVALUES_CLOUDSCAPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_CLOUDSCAPE"],
    "DBSYSTEMVALUES_COCKROACHDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_COCKROACHDB"],
    "DBSYSTEMVALUES_COLDFUSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_COLDFUSION"],
    "DBSYSTEMVALUES_COSMOSDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_COSMOSDB"],
    "DBSYSTEMVALUES_COUCHBASE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_COUCHBASE"],
    "DBSYSTEMVALUES_COUCHDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_COUCHDB"],
    "DBSYSTEMVALUES_DB2": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_DB2"],
    "DBSYSTEMVALUES_DERBY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_DERBY"],
    "DBSYSTEMVALUES_DYNAMODB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_DYNAMODB"],
    "DBSYSTEMVALUES_EDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_EDB"],
    "DBSYSTEMVALUES_ELASTICSEARCH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_ELASTICSEARCH"],
    "DBSYSTEMVALUES_FILEMAKER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_FILEMAKER"],
    "DBSYSTEMVALUES_FIREBIRD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_FIREBIRD"],
    "DBSYSTEMVALUES_FIRSTSQL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_FIRSTSQL"],
    "DBSYSTEMVALUES_GEODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_GEODE"],
    "DBSYSTEMVALUES_H2": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_H2"],
    "DBSYSTEMVALUES_HANADB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_HANADB"],
    "DBSYSTEMVALUES_HBASE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_HBASE"],
    "DBSYSTEMVALUES_HIVE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_HIVE"],
    "DBSYSTEMVALUES_HSQLDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_HSQLDB"],
    "DBSYSTEMVALUES_INFORMIX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_INFORMIX"],
    "DBSYSTEMVALUES_INGRES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_INGRES"],
    "DBSYSTEMVALUES_INSTANTDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_INSTANTDB"],
    "DBSYSTEMVALUES_INTERBASE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_INTERBASE"],
    "DBSYSTEMVALUES_MARIADB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_MARIADB"],
    "DBSYSTEMVALUES_MAXDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_MAXDB"],
    "DBSYSTEMVALUES_MEMCACHED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_MEMCACHED"],
    "DBSYSTEMVALUES_MONGODB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_MONGODB"],
    "DBSYSTEMVALUES_MSSQL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_MSSQL"],
    "DBSYSTEMVALUES_MYSQL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_MYSQL"],
    "DBSYSTEMVALUES_NEO4J": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_NEO4J"],
    "DBSYSTEMVALUES_NETEZZA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_NETEZZA"],
    "DBSYSTEMVALUES_ORACLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_ORACLE"],
    "DBSYSTEMVALUES_OTHER_SQL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_OTHER_SQL"],
    "DBSYSTEMVALUES_PERVASIVE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_PERVASIVE"],
    "DBSYSTEMVALUES_POINTBASE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_POINTBASE"],
    "DBSYSTEMVALUES_POSTGRESQL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_POSTGRESQL"],
    "DBSYSTEMVALUES_PROGRESS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_PROGRESS"],
    "DBSYSTEMVALUES_REDIS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_REDIS"],
    "DBSYSTEMVALUES_REDSHIFT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_REDSHIFT"],
    "DBSYSTEMVALUES_SQLITE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_SQLITE"],
    "DBSYSTEMVALUES_SYBASE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_SYBASE"],
    "DBSYSTEMVALUES_TERADATA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_TERADATA"],
    "DBSYSTEMVALUES_VERTICA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DBSYSTEMVALUES_VERTICA"],
    "DbCassandraConsistencyLevelValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DbCassandraConsistencyLevelValues"],
    "DbSystemValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DbSystemValues"],
    "FAASDOCUMENTOPERATIONVALUES_DELETE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAASDOCUMENTOPERATIONVALUES_DELETE"],
    "FAASDOCUMENTOPERATIONVALUES_EDIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAASDOCUMENTOPERATIONVALUES_EDIT"],
    "FAASDOCUMENTOPERATIONVALUES_INSERT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAASDOCUMENTOPERATIONVALUES_INSERT"],
    "FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD"],
    "FAASINVOKEDPROVIDERVALUES_AWS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAASINVOKEDPROVIDERVALUES_AWS"],
    "FAASINVOKEDPROVIDERVALUES_AZURE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAASINVOKEDPROVIDERVALUES_AZURE"],
    "FAASINVOKEDPROVIDERVALUES_GCP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAASINVOKEDPROVIDERVALUES_GCP"],
    "FAASTRIGGERVALUES_DATASOURCE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAASTRIGGERVALUES_DATASOURCE"],
    "FAASTRIGGERVALUES_HTTP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAASTRIGGERVALUES_HTTP"],
    "FAASTRIGGERVALUES_OTHER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAASTRIGGERVALUES_OTHER"],
    "FAASTRIGGERVALUES_PUBSUB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAASTRIGGERVALUES_PUBSUB"],
    "FAASTRIGGERVALUES_TIMER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAASTRIGGERVALUES_TIMER"],
    "FaasDocumentOperationValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FaasDocumentOperationValues"],
    "FaasInvokedProviderValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FaasInvokedProviderValues"],
    "FaasTriggerValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FaasTriggerValues"],
    "HTTPFLAVORVALUES_HTTP_1_0": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTPFLAVORVALUES_HTTP_1_0"],
    "HTTPFLAVORVALUES_HTTP_1_1": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTPFLAVORVALUES_HTTP_1_1"],
    "HTTPFLAVORVALUES_HTTP_2_0": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTPFLAVORVALUES_HTTP_2_0"],
    "HTTPFLAVORVALUES_QUIC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTPFLAVORVALUES_QUIC"],
    "HTTPFLAVORVALUES_SPDY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTPFLAVORVALUES_SPDY"],
    "HttpFlavorValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HttpFlavorValues"],
    "MESSAGETYPEVALUES_RECEIVED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGETYPEVALUES_RECEIVED"],
    "MESSAGETYPEVALUES_SENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGETYPEVALUES_SENT"],
    "MESSAGINGDESTINATIONKINDVALUES_QUEUE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGINGDESTINATIONKINDVALUES_QUEUE"],
    "MESSAGINGDESTINATIONKINDVALUES_TOPIC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGINGDESTINATIONKINDVALUES_TOPIC"],
    "MESSAGINGOPERATIONVALUES_PROCESS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGINGOPERATIONVALUES_PROCESS"],
    "MESSAGINGOPERATIONVALUES_RECEIVE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGINGOPERATIONVALUES_RECEIVE"],
    "MessageTypeValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MessageTypeValues"],
    "MessagingDestinationKindValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MessagingDestinationKindValues"],
    "MessagingOperationValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MessagingOperationValues"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_CDMA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_CDMA"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_EDGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_EDGE"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_GPRS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_GPRS"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_GSM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_GSM"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_HSPA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_HSPA"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_IDEN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_IDEN"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_LTE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_LTE"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_NR": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_NR"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_UMTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONSUBTYPEVALUES_UMTS"],
    "NETHOSTCONNECTIONTYPEVALUES_CELL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONTYPEVALUES_CELL"],
    "NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE"],
    "NETHOSTCONNECTIONTYPEVALUES_UNKNOWN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONTYPEVALUES_UNKNOWN"],
    "NETHOSTCONNECTIONTYPEVALUES_WIFI": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONTYPEVALUES_WIFI"],
    "NETHOSTCONNECTIONTYPEVALUES_WIRED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETHOSTCONNECTIONTYPEVALUES_WIRED"],
    "NETTRANSPORTVALUES_INPROC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETTRANSPORTVALUES_INPROC"],
    "NETTRANSPORTVALUES_IP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETTRANSPORTVALUES_IP"],
    "NETTRANSPORTVALUES_IP_TCP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETTRANSPORTVALUES_IP_TCP"],
    "NETTRANSPORTVALUES_IP_UDP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETTRANSPORTVALUES_IP_UDP"],
    "NETTRANSPORTVALUES_OTHER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETTRANSPORTVALUES_OTHER"],
    "NETTRANSPORTVALUES_PIPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETTRANSPORTVALUES_PIPE"],
    "NETTRANSPORTVALUES_UNIX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETTRANSPORTVALUES_UNIX"],
    "NetHostConnectionSubtypeValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NetHostConnectionSubtypeValues"],
    "NetHostConnectionTypeValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NetHostConnectionTypeValues"],
    "NetTransportValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NetTransportValues"],
    "RPCGRPCSTATUSCODEVALUES_ABORTED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPCGRPCSTATUSCODEVALUES_ABORTED"],
    "RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS"],
    "RPCGRPCSTATUSCODEVALUES_CANCELLED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPCGRPCSTATUSCODEVALUES_CANCELLED"],
    "RPCGRPCSTATUSCODEVALUES_DATA_LOSS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPCGRPCSTATUSCODEVALUES_DATA_LOSS"],
    "RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED"],
    "RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION"],
    "RPCGRPCSTATUSCODEVALUES_INTERNAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPCGRPCSTATUSCODEVALUES_INTERNAL"],
    "RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT"],
    "RPCGRPCSTATUSCODEVALUES_NOT_FOUND": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPCGRPCSTATUSCODEVALUES_NOT_FOUND"],
    "RPCGRPCSTATUSCODEVALUES_OK": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPCGRPCSTATUSCODEVALUES_OK"],
    "RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE"],
    "RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED"],
    "RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED"],
    "RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED"],
    "RPCGRPCSTATUSCODEVALUES_UNAVAILABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPCGRPCSTATUSCODEVALUES_UNAVAILABLE"],
    "RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED"],
    "RPCGRPCSTATUSCODEVALUES_UNKNOWN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPCGRPCSTATUSCODEVALUES_UNKNOWN"],
    "RpcGrpcStatusCodeValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RpcGrpcStatusCodeValues"],
    "SEMATTRS_AWS_DYNAMODB_ATTRIBUTES_TO_GET": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_ATTRIBUTES_TO_GET"],
    "SEMATTRS_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS"],
    "SEMATTRS_AWS_DYNAMODB_CONSISTENT_READ": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_CONSISTENT_READ"],
    "SEMATTRS_AWS_DYNAMODB_CONSUMED_CAPACITY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_CONSUMED_CAPACITY"],
    "SEMATTRS_AWS_DYNAMODB_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_COUNT"],
    "SEMATTRS_AWS_DYNAMODB_EXCLUSIVE_START_TABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_EXCLUSIVE_START_TABLE"],
    "SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES"],
    "SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES"],
    "SEMATTRS_AWS_DYNAMODB_INDEX_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_INDEX_NAME"],
    "SEMATTRS_AWS_DYNAMODB_ITEM_COLLECTION_METRICS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_ITEM_COLLECTION_METRICS"],
    "SEMATTRS_AWS_DYNAMODB_LIMIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_LIMIT"],
    "SEMATTRS_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES"],
    "SEMATTRS_AWS_DYNAMODB_PROJECTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_PROJECTION"],
    "SEMATTRS_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY"],
    "SEMATTRS_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY"],
    "SEMATTRS_AWS_DYNAMODB_SCANNED_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_SCANNED_COUNT"],
    "SEMATTRS_AWS_DYNAMODB_SCAN_FORWARD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_SCAN_FORWARD"],
    "SEMATTRS_AWS_DYNAMODB_SEGMENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_SEGMENT"],
    "SEMATTRS_AWS_DYNAMODB_SELECT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_SELECT"],
    "SEMATTRS_AWS_DYNAMODB_TABLE_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_TABLE_COUNT"],
    "SEMATTRS_AWS_DYNAMODB_TABLE_NAMES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_TABLE_NAMES"],
    "SEMATTRS_AWS_DYNAMODB_TOTAL_SEGMENTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_DYNAMODB_TOTAL_SEGMENTS"],
    "SEMATTRS_AWS_LAMBDA_INVOKED_ARN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_AWS_LAMBDA_INVOKED_ARN"],
    "SEMATTRS_CODE_FILEPATH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_CODE_FILEPATH"],
    "SEMATTRS_CODE_FUNCTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_CODE_FUNCTION"],
    "SEMATTRS_CODE_LINENO": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_CODE_LINENO"],
    "SEMATTRS_CODE_NAMESPACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_CODE_NAMESPACE"],
    "SEMATTRS_DB_CASSANDRA_CONSISTENCY_LEVEL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_CASSANDRA_CONSISTENCY_LEVEL"],
    "SEMATTRS_DB_CASSANDRA_COORDINATOR_DC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_CASSANDRA_COORDINATOR_DC"],
    "SEMATTRS_DB_CASSANDRA_COORDINATOR_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_CASSANDRA_COORDINATOR_ID"],
    "SEMATTRS_DB_CASSANDRA_IDEMPOTENCE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_CASSANDRA_IDEMPOTENCE"],
    "SEMATTRS_DB_CASSANDRA_KEYSPACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_CASSANDRA_KEYSPACE"],
    "SEMATTRS_DB_CASSANDRA_PAGE_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_CASSANDRA_PAGE_SIZE"],
    "SEMATTRS_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT"],
    "SEMATTRS_DB_CASSANDRA_TABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_CASSANDRA_TABLE"],
    "SEMATTRS_DB_CONNECTION_STRING": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_CONNECTION_STRING"],
    "SEMATTRS_DB_HBASE_NAMESPACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_HBASE_NAMESPACE"],
    "SEMATTRS_DB_JDBC_DRIVER_CLASSNAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_JDBC_DRIVER_CLASSNAME"],
    "SEMATTRS_DB_MONGODB_COLLECTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_MONGODB_COLLECTION"],
    "SEMATTRS_DB_MSSQL_INSTANCE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_MSSQL_INSTANCE_NAME"],
    "SEMATTRS_DB_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_NAME"],
    "SEMATTRS_DB_OPERATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_OPERATION"],
    "SEMATTRS_DB_REDIS_DATABASE_INDEX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_REDIS_DATABASE_INDEX"],
    "SEMATTRS_DB_SQL_TABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_SQL_TABLE"],
    "SEMATTRS_DB_STATEMENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_STATEMENT"],
    "SEMATTRS_DB_SYSTEM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_SYSTEM"],
    "SEMATTRS_DB_USER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_DB_USER"],
    "SEMATTRS_ENDUSER_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_ENDUSER_ID"],
    "SEMATTRS_ENDUSER_ROLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_ENDUSER_ROLE"],
    "SEMATTRS_ENDUSER_SCOPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_ENDUSER_SCOPE"],
    "SEMATTRS_EXCEPTION_ESCAPED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_EXCEPTION_ESCAPED"],
    "SEMATTRS_EXCEPTION_MESSAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_EXCEPTION_MESSAGE"],
    "SEMATTRS_EXCEPTION_STACKTRACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_EXCEPTION_STACKTRACE"],
    "SEMATTRS_EXCEPTION_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_EXCEPTION_TYPE"],
    "SEMATTRS_FAAS_COLDSTART": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_FAAS_COLDSTART"],
    "SEMATTRS_FAAS_CRON": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_FAAS_CRON"],
    "SEMATTRS_FAAS_DOCUMENT_COLLECTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_FAAS_DOCUMENT_COLLECTION"],
    "SEMATTRS_FAAS_DOCUMENT_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_FAAS_DOCUMENT_NAME"],
    "SEMATTRS_FAAS_DOCUMENT_OPERATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_FAAS_DOCUMENT_OPERATION"],
    "SEMATTRS_FAAS_DOCUMENT_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_FAAS_DOCUMENT_TIME"],
    "SEMATTRS_FAAS_EXECUTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_FAAS_EXECUTION"],
    "SEMATTRS_FAAS_INVOKED_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_FAAS_INVOKED_NAME"],
    "SEMATTRS_FAAS_INVOKED_PROVIDER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_FAAS_INVOKED_PROVIDER"],
    "SEMATTRS_FAAS_INVOKED_REGION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_FAAS_INVOKED_REGION"],
    "SEMATTRS_FAAS_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_FAAS_TIME"],
    "SEMATTRS_FAAS_TRIGGER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_FAAS_TRIGGER"],
    "SEMATTRS_HTTP_CLIENT_IP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_HTTP_CLIENT_IP"],
    "SEMATTRS_HTTP_FLAVOR": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_HTTP_FLAVOR"],
    "SEMATTRS_HTTP_HOST": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_HTTP_HOST"],
    "SEMATTRS_HTTP_METHOD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_HTTP_METHOD"],
    "SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH"],
    "SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED"],
    "SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH"],
    "SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED"],
    "SEMATTRS_HTTP_ROUTE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_HTTP_ROUTE"],
    "SEMATTRS_HTTP_SCHEME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_HTTP_SCHEME"],
    "SEMATTRS_HTTP_SERVER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_HTTP_SERVER_NAME"],
    "SEMATTRS_HTTP_STATUS_CODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_HTTP_STATUS_CODE"],
    "SEMATTRS_HTTP_TARGET": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_HTTP_TARGET"],
    "SEMATTRS_HTTP_URL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_HTTP_URL"],
    "SEMATTRS_HTTP_USER_AGENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_HTTP_USER_AGENT"],
    "SEMATTRS_MESSAGE_COMPRESSED_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGE_COMPRESSED_SIZE"],
    "SEMATTRS_MESSAGE_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGE_ID"],
    "SEMATTRS_MESSAGE_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGE_TYPE"],
    "SEMATTRS_MESSAGE_UNCOMPRESSED_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGE_UNCOMPRESSED_SIZE"],
    "SEMATTRS_MESSAGING_CONSUMER_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_CONSUMER_ID"],
    "SEMATTRS_MESSAGING_CONVERSATION_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_CONVERSATION_ID"],
    "SEMATTRS_MESSAGING_DESTINATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_DESTINATION"],
    "SEMATTRS_MESSAGING_DESTINATION_KIND": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_DESTINATION_KIND"],
    "SEMATTRS_MESSAGING_KAFKA_CLIENT_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_KAFKA_CLIENT_ID"],
    "SEMATTRS_MESSAGING_KAFKA_CONSUMER_GROUP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_KAFKA_CONSUMER_GROUP"],
    "SEMATTRS_MESSAGING_KAFKA_MESSAGE_KEY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_KAFKA_MESSAGE_KEY"],
    "SEMATTRS_MESSAGING_KAFKA_PARTITION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_KAFKA_PARTITION"],
    "SEMATTRS_MESSAGING_KAFKA_TOMBSTONE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_KAFKA_TOMBSTONE"],
    "SEMATTRS_MESSAGING_MESSAGE_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_MESSAGE_ID"],
    "SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES"],
    "SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES"],
    "SEMATTRS_MESSAGING_OPERATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_OPERATION"],
    "SEMATTRS_MESSAGING_PROTOCOL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_PROTOCOL"],
    "SEMATTRS_MESSAGING_PROTOCOL_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_PROTOCOL_VERSION"],
    "SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY"],
    "SEMATTRS_MESSAGING_SYSTEM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_SYSTEM"],
    "SEMATTRS_MESSAGING_TEMP_DESTINATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_TEMP_DESTINATION"],
    "SEMATTRS_MESSAGING_URL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_MESSAGING_URL"],
    "SEMATTRS_NET_HOST_CARRIER_ICC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_NET_HOST_CARRIER_ICC"],
    "SEMATTRS_NET_HOST_CARRIER_MCC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_NET_HOST_CARRIER_MCC"],
    "SEMATTRS_NET_HOST_CARRIER_MNC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_NET_HOST_CARRIER_MNC"],
    "SEMATTRS_NET_HOST_CARRIER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_NET_HOST_CARRIER_NAME"],
    "SEMATTRS_NET_HOST_CONNECTION_SUBTYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_NET_HOST_CONNECTION_SUBTYPE"],
    "SEMATTRS_NET_HOST_CONNECTION_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_NET_HOST_CONNECTION_TYPE"],
    "SEMATTRS_NET_HOST_IP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_NET_HOST_IP"],
    "SEMATTRS_NET_HOST_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_NET_HOST_NAME"],
    "SEMATTRS_NET_HOST_PORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_NET_HOST_PORT"],
    "SEMATTRS_NET_PEER_IP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_NET_PEER_IP"],
    "SEMATTRS_NET_PEER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_NET_PEER_NAME"],
    "SEMATTRS_NET_PEER_PORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_NET_PEER_PORT"],
    "SEMATTRS_NET_TRANSPORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_NET_TRANSPORT"],
    "SEMATTRS_PEER_SERVICE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_PEER_SERVICE"],
    "SEMATTRS_RPC_GRPC_STATUS_CODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_RPC_GRPC_STATUS_CODE"],
    "SEMATTRS_RPC_JSONRPC_ERROR_CODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_RPC_JSONRPC_ERROR_CODE"],
    "SEMATTRS_RPC_JSONRPC_ERROR_MESSAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_RPC_JSONRPC_ERROR_MESSAGE"],
    "SEMATTRS_RPC_JSONRPC_REQUEST_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_RPC_JSONRPC_REQUEST_ID"],
    "SEMATTRS_RPC_JSONRPC_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_RPC_JSONRPC_VERSION"],
    "SEMATTRS_RPC_METHOD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_RPC_METHOD"],
    "SEMATTRS_RPC_SERVICE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_RPC_SERVICE"],
    "SEMATTRS_RPC_SYSTEM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_RPC_SYSTEM"],
    "SEMATTRS_THREAD_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_THREAD_ID"],
    "SEMATTRS_THREAD_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SEMATTRS_THREAD_NAME"],
    "SemanticAttributes": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SemanticAttributes"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$SemanticAttributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/SemanticAttributes.js [instrumentation] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/index.js [instrumentation] (ecmascript) <locals>");
}),
"[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/index.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "DBCASSANDRACONSISTENCYLEVELVALUES_ALL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBCASSANDRACONSISTENCYLEVELVALUES_ALL"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_ANY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBCASSANDRACONSISTENCYLEVELVALUES_ANY"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_ONE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBCASSANDRACONSISTENCYLEVELVALUES_ONE"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_THREE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBCASSANDRACONSISTENCYLEVELVALUES_THREE"],
    "DBCASSANDRACONSISTENCYLEVELVALUES_TWO": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBCASSANDRACONSISTENCYLEVELVALUES_TWO"],
    "DBSYSTEMVALUES_ADABAS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_ADABAS"],
    "DBSYSTEMVALUES_CACHE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_CACHE"],
    "DBSYSTEMVALUES_CASSANDRA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_CASSANDRA"],
    "DBSYSTEMVALUES_CLOUDSCAPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_CLOUDSCAPE"],
    "DBSYSTEMVALUES_COCKROACHDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_COCKROACHDB"],
    "DBSYSTEMVALUES_COLDFUSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_COLDFUSION"],
    "DBSYSTEMVALUES_COSMOSDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_COSMOSDB"],
    "DBSYSTEMVALUES_COUCHBASE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_COUCHBASE"],
    "DBSYSTEMVALUES_COUCHDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_COUCHDB"],
    "DBSYSTEMVALUES_DB2": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_DB2"],
    "DBSYSTEMVALUES_DERBY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_DERBY"],
    "DBSYSTEMVALUES_DYNAMODB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_DYNAMODB"],
    "DBSYSTEMVALUES_EDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_EDB"],
    "DBSYSTEMVALUES_ELASTICSEARCH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_ELASTICSEARCH"],
    "DBSYSTEMVALUES_FILEMAKER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_FILEMAKER"],
    "DBSYSTEMVALUES_FIREBIRD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_FIREBIRD"],
    "DBSYSTEMVALUES_FIRSTSQL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_FIRSTSQL"],
    "DBSYSTEMVALUES_GEODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_GEODE"],
    "DBSYSTEMVALUES_H2": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_H2"],
    "DBSYSTEMVALUES_HANADB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_HANADB"],
    "DBSYSTEMVALUES_HBASE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_HBASE"],
    "DBSYSTEMVALUES_HIVE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_HIVE"],
    "DBSYSTEMVALUES_HSQLDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_HSQLDB"],
    "DBSYSTEMVALUES_INFORMIX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_INFORMIX"],
    "DBSYSTEMVALUES_INGRES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_INGRES"],
    "DBSYSTEMVALUES_INSTANTDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_INSTANTDB"],
    "DBSYSTEMVALUES_INTERBASE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_INTERBASE"],
    "DBSYSTEMVALUES_MARIADB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_MARIADB"],
    "DBSYSTEMVALUES_MAXDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_MAXDB"],
    "DBSYSTEMVALUES_MEMCACHED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_MEMCACHED"],
    "DBSYSTEMVALUES_MONGODB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_MONGODB"],
    "DBSYSTEMVALUES_MSSQL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_MSSQL"],
    "DBSYSTEMVALUES_MYSQL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_MYSQL"],
    "DBSYSTEMVALUES_NEO4J": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_NEO4J"],
    "DBSYSTEMVALUES_NETEZZA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_NETEZZA"],
    "DBSYSTEMVALUES_ORACLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_ORACLE"],
    "DBSYSTEMVALUES_OTHER_SQL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_OTHER_SQL"],
    "DBSYSTEMVALUES_PERVASIVE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_PERVASIVE"],
    "DBSYSTEMVALUES_POINTBASE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_POINTBASE"],
    "DBSYSTEMVALUES_POSTGRESQL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_POSTGRESQL"],
    "DBSYSTEMVALUES_PROGRESS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_PROGRESS"],
    "DBSYSTEMVALUES_REDIS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_REDIS"],
    "DBSYSTEMVALUES_REDSHIFT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_REDSHIFT"],
    "DBSYSTEMVALUES_SQLITE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_SQLITE"],
    "DBSYSTEMVALUES_SYBASE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_SYBASE"],
    "DBSYSTEMVALUES_TERADATA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_TERADATA"],
    "DBSYSTEMVALUES_VERTICA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DBSYSTEMVALUES_VERTICA"],
    "DbCassandraConsistencyLevelValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DbCassandraConsistencyLevelValues"],
    "DbSystemValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DbSystemValues"],
    "FAASDOCUMENTOPERATIONVALUES_DELETE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["FAASDOCUMENTOPERATIONVALUES_DELETE"],
    "FAASDOCUMENTOPERATIONVALUES_EDIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["FAASDOCUMENTOPERATIONVALUES_EDIT"],
    "FAASDOCUMENTOPERATIONVALUES_INSERT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["FAASDOCUMENTOPERATIONVALUES_INSERT"],
    "FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD"],
    "FAASINVOKEDPROVIDERVALUES_AWS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["FAASINVOKEDPROVIDERVALUES_AWS"],
    "FAASINVOKEDPROVIDERVALUES_AZURE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["FAASINVOKEDPROVIDERVALUES_AZURE"],
    "FAASINVOKEDPROVIDERVALUES_GCP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["FAASINVOKEDPROVIDERVALUES_GCP"],
    "FAASTRIGGERVALUES_DATASOURCE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["FAASTRIGGERVALUES_DATASOURCE"],
    "FAASTRIGGERVALUES_HTTP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["FAASTRIGGERVALUES_HTTP"],
    "FAASTRIGGERVALUES_OTHER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["FAASTRIGGERVALUES_OTHER"],
    "FAASTRIGGERVALUES_PUBSUB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["FAASTRIGGERVALUES_PUBSUB"],
    "FAASTRIGGERVALUES_TIMER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["FAASTRIGGERVALUES_TIMER"],
    "FaasDocumentOperationValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["FaasDocumentOperationValues"],
    "FaasInvokedProviderValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["FaasInvokedProviderValues"],
    "FaasTriggerValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["FaasTriggerValues"],
    "HTTPFLAVORVALUES_HTTP_1_0": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["HTTPFLAVORVALUES_HTTP_1_0"],
    "HTTPFLAVORVALUES_HTTP_1_1": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["HTTPFLAVORVALUES_HTTP_1_1"],
    "HTTPFLAVORVALUES_HTTP_2_0": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["HTTPFLAVORVALUES_HTTP_2_0"],
    "HTTPFLAVORVALUES_QUIC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["HTTPFLAVORVALUES_QUIC"],
    "HTTPFLAVORVALUES_SPDY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["HTTPFLAVORVALUES_SPDY"],
    "HttpFlavorValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["HttpFlavorValues"],
    "MESSAGETYPEVALUES_RECEIVED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["MESSAGETYPEVALUES_RECEIVED"],
    "MESSAGETYPEVALUES_SENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["MESSAGETYPEVALUES_SENT"],
    "MESSAGINGDESTINATIONKINDVALUES_QUEUE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["MESSAGINGDESTINATIONKINDVALUES_QUEUE"],
    "MESSAGINGDESTINATIONKINDVALUES_TOPIC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["MESSAGINGDESTINATIONKINDVALUES_TOPIC"],
    "MESSAGINGOPERATIONVALUES_PROCESS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["MESSAGINGOPERATIONVALUES_PROCESS"],
    "MESSAGINGOPERATIONVALUES_RECEIVE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["MESSAGINGOPERATIONVALUES_RECEIVE"],
    "MessageTypeValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["MessageTypeValues"],
    "MessagingDestinationKindValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["MessagingDestinationKindValues"],
    "MessagingOperationValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["MessagingOperationValues"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_CDMA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_CDMA"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_EDGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_EDGE"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_GPRS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_GPRS"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_GSM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_GSM"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_HSPA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_HSPA"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_IDEN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_IDEN"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_LTE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_LTE"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_NR": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_NR"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA"],
    "NETHOSTCONNECTIONSUBTYPEVALUES_UMTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONSUBTYPEVALUES_UMTS"],
    "NETHOSTCONNECTIONTYPEVALUES_CELL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONTYPEVALUES_CELL"],
    "NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE"],
    "NETHOSTCONNECTIONTYPEVALUES_UNKNOWN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONTYPEVALUES_UNKNOWN"],
    "NETHOSTCONNECTIONTYPEVALUES_WIFI": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONTYPEVALUES_WIFI"],
    "NETHOSTCONNECTIONTYPEVALUES_WIRED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETHOSTCONNECTIONTYPEVALUES_WIRED"],
    "NETTRANSPORTVALUES_INPROC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETTRANSPORTVALUES_INPROC"],
    "NETTRANSPORTVALUES_IP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETTRANSPORTVALUES_IP"],
    "NETTRANSPORTVALUES_IP_TCP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETTRANSPORTVALUES_IP_TCP"],
    "NETTRANSPORTVALUES_IP_UDP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETTRANSPORTVALUES_IP_UDP"],
    "NETTRANSPORTVALUES_OTHER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETTRANSPORTVALUES_OTHER"],
    "NETTRANSPORTVALUES_PIPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETTRANSPORTVALUES_PIPE"],
    "NETTRANSPORTVALUES_UNIX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NETTRANSPORTVALUES_UNIX"],
    "NetHostConnectionSubtypeValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NetHostConnectionSubtypeValues"],
    "NetHostConnectionTypeValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NetHostConnectionTypeValues"],
    "NetTransportValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NetTransportValues"],
    "RPCGRPCSTATUSCODEVALUES_ABORTED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["RPCGRPCSTATUSCODEVALUES_ABORTED"],
    "RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS"],
    "RPCGRPCSTATUSCODEVALUES_CANCELLED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["RPCGRPCSTATUSCODEVALUES_CANCELLED"],
    "RPCGRPCSTATUSCODEVALUES_DATA_LOSS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["RPCGRPCSTATUSCODEVALUES_DATA_LOSS"],
    "RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED"],
    "RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION"],
    "RPCGRPCSTATUSCODEVALUES_INTERNAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["RPCGRPCSTATUSCODEVALUES_INTERNAL"],
    "RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT"],
    "RPCGRPCSTATUSCODEVALUES_NOT_FOUND": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["RPCGRPCSTATUSCODEVALUES_NOT_FOUND"],
    "RPCGRPCSTATUSCODEVALUES_OK": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["RPCGRPCSTATUSCODEVALUES_OK"],
    "RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE"],
    "RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED"],
    "RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED"],
    "RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED"],
    "RPCGRPCSTATUSCODEVALUES_UNAVAILABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["RPCGRPCSTATUSCODEVALUES_UNAVAILABLE"],
    "RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED"],
    "RPCGRPCSTATUSCODEVALUES_UNKNOWN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["RPCGRPCSTATUSCODEVALUES_UNKNOWN"],
    "RpcGrpcStatusCodeValues": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["RpcGrpcStatusCodeValues"],
    "SEMATTRS_AWS_DYNAMODB_ATTRIBUTES_TO_GET": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_ATTRIBUTES_TO_GET"],
    "SEMATTRS_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS"],
    "SEMATTRS_AWS_DYNAMODB_CONSISTENT_READ": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_CONSISTENT_READ"],
    "SEMATTRS_AWS_DYNAMODB_CONSUMED_CAPACITY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_CONSUMED_CAPACITY"],
    "SEMATTRS_AWS_DYNAMODB_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_COUNT"],
    "SEMATTRS_AWS_DYNAMODB_EXCLUSIVE_START_TABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_EXCLUSIVE_START_TABLE"],
    "SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES"],
    "SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES"],
    "SEMATTRS_AWS_DYNAMODB_INDEX_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_INDEX_NAME"],
    "SEMATTRS_AWS_DYNAMODB_ITEM_COLLECTION_METRICS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_ITEM_COLLECTION_METRICS"],
    "SEMATTRS_AWS_DYNAMODB_LIMIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_LIMIT"],
    "SEMATTRS_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES"],
    "SEMATTRS_AWS_DYNAMODB_PROJECTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_PROJECTION"],
    "SEMATTRS_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY"],
    "SEMATTRS_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY"],
    "SEMATTRS_AWS_DYNAMODB_SCANNED_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_SCANNED_COUNT"],
    "SEMATTRS_AWS_DYNAMODB_SCAN_FORWARD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_SCAN_FORWARD"],
    "SEMATTRS_AWS_DYNAMODB_SEGMENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_SEGMENT"],
    "SEMATTRS_AWS_DYNAMODB_SELECT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_SELECT"],
    "SEMATTRS_AWS_DYNAMODB_TABLE_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_TABLE_COUNT"],
    "SEMATTRS_AWS_DYNAMODB_TABLE_NAMES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_TABLE_NAMES"],
    "SEMATTRS_AWS_DYNAMODB_TOTAL_SEGMENTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_DYNAMODB_TOTAL_SEGMENTS"],
    "SEMATTRS_AWS_LAMBDA_INVOKED_ARN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_AWS_LAMBDA_INVOKED_ARN"],
    "SEMATTRS_CODE_FILEPATH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_CODE_FILEPATH"],
    "SEMATTRS_CODE_FUNCTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_CODE_FUNCTION"],
    "SEMATTRS_CODE_LINENO": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_CODE_LINENO"],
    "SEMATTRS_CODE_NAMESPACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_CODE_NAMESPACE"],
    "SEMATTRS_DB_CASSANDRA_CONSISTENCY_LEVEL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_CASSANDRA_CONSISTENCY_LEVEL"],
    "SEMATTRS_DB_CASSANDRA_COORDINATOR_DC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_CASSANDRA_COORDINATOR_DC"],
    "SEMATTRS_DB_CASSANDRA_COORDINATOR_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_CASSANDRA_COORDINATOR_ID"],
    "SEMATTRS_DB_CASSANDRA_IDEMPOTENCE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_CASSANDRA_IDEMPOTENCE"],
    "SEMATTRS_DB_CASSANDRA_KEYSPACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_CASSANDRA_KEYSPACE"],
    "SEMATTRS_DB_CASSANDRA_PAGE_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_CASSANDRA_PAGE_SIZE"],
    "SEMATTRS_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT"],
    "SEMATTRS_DB_CASSANDRA_TABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_CASSANDRA_TABLE"],
    "SEMATTRS_DB_CONNECTION_STRING": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_CONNECTION_STRING"],
    "SEMATTRS_DB_HBASE_NAMESPACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_HBASE_NAMESPACE"],
    "SEMATTRS_DB_JDBC_DRIVER_CLASSNAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_JDBC_DRIVER_CLASSNAME"],
    "SEMATTRS_DB_MONGODB_COLLECTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_MONGODB_COLLECTION"],
    "SEMATTRS_DB_MSSQL_INSTANCE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_MSSQL_INSTANCE_NAME"],
    "SEMATTRS_DB_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_NAME"],
    "SEMATTRS_DB_OPERATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_OPERATION"],
    "SEMATTRS_DB_REDIS_DATABASE_INDEX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_REDIS_DATABASE_INDEX"],
    "SEMATTRS_DB_SQL_TABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_SQL_TABLE"],
    "SEMATTRS_DB_STATEMENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_STATEMENT"],
    "SEMATTRS_DB_SYSTEM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_SYSTEM"],
    "SEMATTRS_DB_USER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_DB_USER"],
    "SEMATTRS_ENDUSER_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_ENDUSER_ID"],
    "SEMATTRS_ENDUSER_ROLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_ENDUSER_ROLE"],
    "SEMATTRS_ENDUSER_SCOPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_ENDUSER_SCOPE"],
    "SEMATTRS_EXCEPTION_ESCAPED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_EXCEPTION_ESCAPED"],
    "SEMATTRS_EXCEPTION_MESSAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_EXCEPTION_MESSAGE"],
    "SEMATTRS_EXCEPTION_STACKTRACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_EXCEPTION_STACKTRACE"],
    "SEMATTRS_EXCEPTION_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_EXCEPTION_TYPE"],
    "SEMATTRS_FAAS_COLDSTART": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_FAAS_COLDSTART"],
    "SEMATTRS_FAAS_CRON": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_FAAS_CRON"],
    "SEMATTRS_FAAS_DOCUMENT_COLLECTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_FAAS_DOCUMENT_COLLECTION"],
    "SEMATTRS_FAAS_DOCUMENT_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_FAAS_DOCUMENT_NAME"],
    "SEMATTRS_FAAS_DOCUMENT_OPERATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_FAAS_DOCUMENT_OPERATION"],
    "SEMATTRS_FAAS_DOCUMENT_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_FAAS_DOCUMENT_TIME"],
    "SEMATTRS_FAAS_EXECUTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_FAAS_EXECUTION"],
    "SEMATTRS_FAAS_INVOKED_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_FAAS_INVOKED_NAME"],
    "SEMATTRS_FAAS_INVOKED_PROVIDER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_FAAS_INVOKED_PROVIDER"],
    "SEMATTRS_FAAS_INVOKED_REGION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_FAAS_INVOKED_REGION"],
    "SEMATTRS_FAAS_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_FAAS_TIME"],
    "SEMATTRS_FAAS_TRIGGER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_FAAS_TRIGGER"],
    "SEMATTRS_HTTP_CLIENT_IP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_HTTP_CLIENT_IP"],
    "SEMATTRS_HTTP_FLAVOR": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_HTTP_FLAVOR"],
    "SEMATTRS_HTTP_HOST": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_HTTP_HOST"],
    "SEMATTRS_HTTP_METHOD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_HTTP_METHOD"],
    "SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH"],
    "SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED"],
    "SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH"],
    "SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED"],
    "SEMATTRS_HTTP_ROUTE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_HTTP_ROUTE"],
    "SEMATTRS_HTTP_SCHEME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_HTTP_SCHEME"],
    "SEMATTRS_HTTP_SERVER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_HTTP_SERVER_NAME"],
    "SEMATTRS_HTTP_STATUS_CODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_HTTP_STATUS_CODE"],
    "SEMATTRS_HTTP_TARGET": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_HTTP_TARGET"],
    "SEMATTRS_HTTP_URL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_HTTP_URL"],
    "SEMATTRS_HTTP_USER_AGENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_HTTP_USER_AGENT"],
    "SEMATTRS_MESSAGE_COMPRESSED_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGE_COMPRESSED_SIZE"],
    "SEMATTRS_MESSAGE_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGE_ID"],
    "SEMATTRS_MESSAGE_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGE_TYPE"],
    "SEMATTRS_MESSAGE_UNCOMPRESSED_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGE_UNCOMPRESSED_SIZE"],
    "SEMATTRS_MESSAGING_CONSUMER_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_CONSUMER_ID"],
    "SEMATTRS_MESSAGING_CONVERSATION_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_CONVERSATION_ID"],
    "SEMATTRS_MESSAGING_DESTINATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_DESTINATION"],
    "SEMATTRS_MESSAGING_DESTINATION_KIND": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_DESTINATION_KIND"],
    "SEMATTRS_MESSAGING_KAFKA_CLIENT_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_KAFKA_CLIENT_ID"],
    "SEMATTRS_MESSAGING_KAFKA_CONSUMER_GROUP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_KAFKA_CONSUMER_GROUP"],
    "SEMATTRS_MESSAGING_KAFKA_MESSAGE_KEY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_KAFKA_MESSAGE_KEY"],
    "SEMATTRS_MESSAGING_KAFKA_PARTITION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_KAFKA_PARTITION"],
    "SEMATTRS_MESSAGING_KAFKA_TOMBSTONE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_KAFKA_TOMBSTONE"],
    "SEMATTRS_MESSAGING_MESSAGE_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_MESSAGE_ID"],
    "SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES"],
    "SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES"],
    "SEMATTRS_MESSAGING_OPERATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_OPERATION"],
    "SEMATTRS_MESSAGING_PROTOCOL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_PROTOCOL"],
    "SEMATTRS_MESSAGING_PROTOCOL_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_PROTOCOL_VERSION"],
    "SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY"],
    "SEMATTRS_MESSAGING_SYSTEM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_SYSTEM"],
    "SEMATTRS_MESSAGING_TEMP_DESTINATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_TEMP_DESTINATION"],
    "SEMATTRS_MESSAGING_URL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_MESSAGING_URL"],
    "SEMATTRS_NET_HOST_CARRIER_ICC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_NET_HOST_CARRIER_ICC"],
    "SEMATTRS_NET_HOST_CARRIER_MCC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_NET_HOST_CARRIER_MCC"],
    "SEMATTRS_NET_HOST_CARRIER_MNC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_NET_HOST_CARRIER_MNC"],
    "SEMATTRS_NET_HOST_CARRIER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_NET_HOST_CARRIER_NAME"],
    "SEMATTRS_NET_HOST_CONNECTION_SUBTYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_NET_HOST_CONNECTION_SUBTYPE"],
    "SEMATTRS_NET_HOST_CONNECTION_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_NET_HOST_CONNECTION_TYPE"],
    "SEMATTRS_NET_HOST_IP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_NET_HOST_IP"],
    "SEMATTRS_NET_HOST_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_NET_HOST_NAME"],
    "SEMATTRS_NET_HOST_PORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_NET_HOST_PORT"],
    "SEMATTRS_NET_PEER_IP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_NET_PEER_IP"],
    "SEMATTRS_NET_PEER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_NET_PEER_NAME"],
    "SEMATTRS_NET_PEER_PORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_NET_PEER_PORT"],
    "SEMATTRS_NET_TRANSPORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_NET_TRANSPORT"],
    "SEMATTRS_PEER_SERVICE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_PEER_SERVICE"],
    "SEMATTRS_RPC_GRPC_STATUS_CODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_RPC_GRPC_STATUS_CODE"],
    "SEMATTRS_RPC_JSONRPC_ERROR_CODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_RPC_JSONRPC_ERROR_CODE"],
    "SEMATTRS_RPC_JSONRPC_ERROR_MESSAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_RPC_JSONRPC_ERROR_MESSAGE"],
    "SEMATTRS_RPC_JSONRPC_REQUEST_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_RPC_JSONRPC_REQUEST_ID"],
    "SEMATTRS_RPC_JSONRPC_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_RPC_JSONRPC_VERSION"],
    "SEMATTRS_RPC_METHOD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_RPC_METHOD"],
    "SEMATTRS_RPC_SERVICE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_RPC_SERVICE"],
    "SEMATTRS_RPC_SYSTEM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_RPC_SYSTEM"],
    "SEMATTRS_THREAD_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_THREAD_ID"],
    "SEMATTRS_THREAD_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SEMATTRS_THREAD_NAME"],
    "SemanticAttributes": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SemanticAttributes"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/index.js [instrumentation] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$trace$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/trace/index.js [instrumentation] (ecmascript) <exports>");
}),

};

//# sourceMappingURL=a7dda_%40opentelemetry_semantic-conventions_build_esm_trace_e2b927a8._.js.map