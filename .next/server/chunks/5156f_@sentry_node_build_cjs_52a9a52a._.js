module.exports = {

"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentation = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation@0.56.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation/build/esm/index.js [instrumentation] (ecmascript)");
/** Exported only for tests. */ const INSTRUMENTED = {};
/**
 * Instrument an OpenTelemetry instrumentation once.
 * This will skip running instrumentation again if it was already instrumented.
 */ function generateInstrumentOnce(name, creator) {
    return Object.assign((options)=>{
        const instrumented = INSTRUMENTED[name];
        if (instrumented) {
            // If options are provided, ensure we update them
            if (options) {
                instrumented.setConfig(options);
            }
            return;
        }
        const instrumentation$1 = creator(options);
        INSTRUMENTED[name] = instrumentation$1;
        instrumentation.registerInstrumentations({
            instrumentations: [
                instrumentation$1
            ]
        });
    }, {
        id: name
    });
}
exports.INSTRUMENTED = INSTRUMENTED;
exports.generateInstrumentOnce = generateInstrumentOnce; //# sourceMappingURL=instrument.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/addOriginToSpan.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
/** Adds an origin to an OTEL Span. */ function addOriginToSpan(span, origin) {
    span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, origin);
}
exports.addOriginToSpan = addOriginToSpan; //# sourceMappingURL=addOriginToSpan.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/getRequestUrl.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
/** Build a full URL from request options. */ function getRequestUrl(requestOptions) {
    const protocol = requestOptions.protocol || '';
    const hostname = requestOptions.hostname || requestOptions.host || '';
    // Don't log standard :80 (http) and :443 (https) ports to reduce the noise
    // Also don't add port if the hostname already includes a port
    const port = !requestOptions.port || requestOptions.port === 80 || requestOptions.port === 443 || /^(.*):(\d+)$/.test(hostname) ? '' : `:${requestOptions.port}`;
    const path = requestOptions.path ? requestOptions.path : '/';
    return `${protocol}//${hostname}${port}${path}`;
}
exports.getRequestUrl = getRequestUrl; //# sourceMappingURL=getRequestUrl.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/debug-build.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
/**
 * This serves as a build time flag that will be true by default, but false in non-debug builds or if users replace `__SENTRY_DEBUG__` in their generated code.
 *
 * ATTENTION: This constant must never cross package boundaries (i.e. be exported) to guarantee that it can be used for tree shaking.
 */ const DEBUG_BUILD = typeof __SENTRY_DEBUG__ === 'undefined' || __SENTRY_DEBUG__;
exports.DEBUG_BUILD = DEBUG_BUILD; //# sourceMappingURL=debug-build.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/http/vendor/getRequestInfo.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const url = __turbopack_context__.r("[externals]/url [external] (url, cjs)");
/**
 * Makes sure options is an url object
 * return an object with default value and parsed options
 * @param logger component logger
 * @param options original options for the request
 * @param [extraOptions] additional options for the request
 */ const getRequestInfo = (logger, options, extraOptions)=>{
    let pathname;
    let origin;
    let optionsParsed;
    let invalidUrl = false;
    if (typeof options === 'string') {
        try {
            const convertedOptions = stringUrlToHttpOptions(options);
            optionsParsed = convertedOptions;
            pathname = convertedOptions.pathname || '/';
        } catch (e) {
            invalidUrl = true;
            logger.verbose('Unable to parse URL provided to HTTP request, using fallback to determine path. Original error:', e);
            // for backward compatibility with how url.parse() behaved.
            optionsParsed = {
                path: options
            };
            pathname = optionsParsed.path || '/';
        }
        origin = `${optionsParsed.protocol || 'http:'}//${optionsParsed.host}`;
        if (extraOptions !== undefined) {
            Object.assign(optionsParsed, extraOptions);
        }
    } else if (options instanceof url.URL) {
        optionsParsed = {
            protocol: options.protocol,
            hostname: typeof options.hostname === 'string' && options.hostname.startsWith('[') ? options.hostname.slice(1, -1) : options.hostname,
            path: `${options.pathname || ''}${options.search || ''}`
        };
        if (options.port !== '') {
            optionsParsed.port = Number(options.port);
        }
        if (options.username || options.password) {
            optionsParsed.auth = `${options.username}:${options.password}`;
        }
        pathname = options.pathname;
        origin = options.origin;
        if (extraOptions !== undefined) {
            Object.assign(optionsParsed, extraOptions);
        }
    } else {
        optionsParsed = Object.assign({
            protocol: options.host ? 'http:' : undefined
        }, options);
        const hostname = optionsParsed.host || (optionsParsed.port != null ? `${optionsParsed.hostname}${optionsParsed.port}` : optionsParsed.hostname);
        origin = `${optionsParsed.protocol || 'http:'}//${hostname}`;
        pathname = options.pathname;
        if (!pathname && optionsParsed.path) {
            try {
                const parsedUrl = new URL(optionsParsed.path, origin);
                pathname = parsedUrl.pathname || '/';
            } catch (e) {
                pathname = '/';
            }
        }
    }
    // some packages return method in lowercase..
    // ensure upperCase for consistency
    const method = optionsParsed.method ? optionsParsed.method.toUpperCase() : 'GET';
    return {
        origin,
        pathname,
        method,
        optionsParsed,
        invalidUrl
    };
};
/**
 * Mimics Node.js conversion of URL strings to RequestOptions expected by
 * `http.request` and `https.request` APIs.
 *
 * See https://github.com/nodejs/node/blob/2505e217bba05fc581b572c685c5cf280a16c5a3/lib/internal/url.js#L1415-L1437
 *
 * @param stringUrl
 * @throws TypeError if the URL is not valid.
 */ function stringUrlToHttpOptions(stringUrl) {
    // This is heavily inspired by Node.js handling of the same situation, trying
    // to follow it as closely as possible while keeping in mind that we only
    // deal with string URLs, not URL objects.
    const { hostname, pathname, port, username, password, search, protocol, hash, href, origin, host } = new URL(stringUrl);
    const options = {
        protocol: protocol,
        hostname: hostname && hostname[0] === '[' ? hostname.slice(1, -1) : hostname,
        hash: hash,
        search: search,
        pathname: pathname,
        path: `${pathname || ''}${search || ''}`,
        href: href,
        origin: origin,
        host: host
    };
    if (port !== '') {
        options.port = Number(port);
    }
    if (username || password) {
        options.auth = `${decodeURIComponent(username)}:${decodeURIComponent(password)}`;
    }
    return options;
}
exports.getRequestInfo = getRequestInfo; //# sourceMappingURL=getRequestInfo.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/http/SentryHttpInstrumentation.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js [instrumentation] (ecmascript)");
const instrumentation = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation@0.56.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation/build/esm/index.js [instrumentation] (ecmascript)");
const core$1 = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const debugBuild = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/debug-build.js [instrumentation] (ecmascript)");
const getRequestUrl = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/getRequestUrl.js [instrumentation] (ecmascript)");
const getRequestInfo = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/http/vendor/getRequestInfo.js [instrumentation] (ecmascript)");
// We only want to capture request bodies up to 1mb.
const MAX_BODY_BYTE_LENGTH = 1024 * 1024;
/**
 * This custom HTTP instrumentation is used to isolate incoming requests and annotate them with additional information.
 * It does not emit any spans.
 *
 * The reason this is isolated from the OpenTelemetry instrumentation is that users may overwrite this,
 * which would lead to Sentry not working as expected.
 *
 * Important note: Contrary to other OTEL instrumentation, this one cannot be unwrapped.
 * It only does minimal things though and does not emit any spans.
 *
 * This is heavily inspired & adapted from:
 * https://github.com/open-telemetry/opentelemetry-js/blob/f8ab5592ddea5cba0a3b33bf8d74f27872c0367f/experimental/packages/opentelemetry-instrumentation-http/src/http.ts
 */ class SentryHttpInstrumentation extends instrumentation.InstrumentationBase {
    constructor(config = {}){
        super('@sentry/instrumentation-http', core.VERSION, config);
    }
    /** @inheritdoc */ init() {
        return [
            this._getHttpsInstrumentation(),
            this._getHttpInstrumentation()
        ];
    }
    /** Get the instrumentation for the http module. */ _getHttpInstrumentation() {
        return new instrumentation.InstrumentationNodeModuleDefinition('http', [
            '*'
        ], (moduleExports)=>{
            // Patch incoming requests for request isolation
            stealthWrap(moduleExports.Server.prototype, 'emit', this._getPatchIncomingRequestFunction());
            // Patch outgoing requests for breadcrumbs
            const patchedRequest = stealthWrap(moduleExports, 'request', this._getPatchOutgoingRequestFunction());
            stealthWrap(moduleExports, 'get', this._getPatchOutgoingGetFunction(patchedRequest));
            return moduleExports;
        }, ()=>{
        // no unwrap here
        });
    }
    /** Get the instrumentation for the https module. */ _getHttpsInstrumentation() {
        return new instrumentation.InstrumentationNodeModuleDefinition('https', [
            '*'
        ], (moduleExports)=>{
            // Patch incoming requests for request isolation
            stealthWrap(moduleExports.Server.prototype, 'emit', this._getPatchIncomingRequestFunction());
            // Patch outgoing requests for breadcrumbs
            const patchedRequest = stealthWrap(moduleExports, 'request', this._getPatchOutgoingRequestFunction());
            stealthWrap(moduleExports, 'get', this._getPatchOutgoingGetFunction(patchedRequest));
            return moduleExports;
        }, ()=>{
        // no unwrap here
        });
    }
    /**
   * Patch the incoming request function for request isolation.
   */ _getPatchIncomingRequestFunction() {
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        const instrumentation = this;
        return (original)=>{
            return function incomingRequest(event, ...args) {
                // Only traces request events
                if (event !== 'request') {
                    return original.apply(this, [
                        event,
                        ...args
                    ]);
                }
                instrumentation._diag.debug('http instrumentation for incoming request');
                const isolationScope = core$1.getIsolationScope().clone();
                const request = args[0];
                const normalizedRequest = core$1.httpRequestToRequestData(request);
                patchRequestToCaptureBody(request, isolationScope);
                // Update the isolation scope, isolate this request
                // TODO(v9): Stop setting `request`, we only rely on normalizedRequest anymore
                isolationScope.setSDKProcessingMetadata({
                    request,
                    normalizedRequest
                });
                const client = core$1.getClient();
                // eslint-disable-next-line deprecation/deprecation
                if (client && client.getOptions().autoSessionTracking) {
                    // eslint-disable-next-line deprecation/deprecation
                    isolationScope.setRequestSession({
                        status: 'ok'
                    });
                }
                // attempt to update the scope's `transactionName` based on the request URL
                // Ideally, framework instrumentations coming after the HttpInstrumentation
                // update the transactionName once we get a parameterized route.
                const httpMethod = (request.method || 'GET').toUpperCase();
                const httpTarget = core$1.stripUrlQueryAndFragment(request.url || '/');
                const bestEffortTransactionName = `${httpMethod} ${httpTarget}`;
                isolationScope.setTransactionName(bestEffortTransactionName);
                return core$1.withIsolationScope(isolationScope, ()=>{
                    return original.apply(this, [
                        event,
                        ...args
                    ]);
                });
            };
        };
    }
    /**
   * Patch the outgoing request function for breadcrumbs.
   */ _getPatchOutgoingRequestFunction() {
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        const instrumentation = this;
        return (original)=>{
            return function outgoingRequest(...args) {
                instrumentation._diag.debug('http instrumentation for outgoing requests');
                // Making a copy to avoid mutating the original args array
                // We need to access and reconstruct the request options object passed to `ignoreOutgoingRequests`
                // so that it matches what Otel instrumentation passes to `ignoreOutgoingRequestHook`.
                // @see https://github.com/open-telemetry/opentelemetry-js/blob/7293e69c1e55ca62e15d0724d22605e61bd58952/experimental/packages/opentelemetry-instrumentation-http/src/http.ts#L756-L789
                const argsCopy = [
                    ...args
                ];
                const options = argsCopy.shift();
                const extraOptions = typeof argsCopy[0] === 'object' && (typeof options === 'string' || options instanceof URL) ? argsCopy.shift() : undefined;
                const { optionsParsed } = getRequestInfo.getRequestInfo(instrumentation._diag, options, extraOptions);
                const request = original.apply(this, args);
                request.prependListener('response', (response)=>{
                    const _breadcrumbs = instrumentation.getConfig().breadcrumbs;
                    const breadCrumbsEnabled = typeof _breadcrumbs === 'undefined' ? true : _breadcrumbs;
                    const _ignoreOutgoingRequests = instrumentation.getConfig().ignoreOutgoingRequests;
                    const shouldCreateBreadcrumb = typeof _ignoreOutgoingRequests === 'function' ? !_ignoreOutgoingRequests(getRequestUrl.getRequestUrl(request), optionsParsed) : true;
                    if (breadCrumbsEnabled && shouldCreateBreadcrumb) {
                        addRequestBreadcrumb(request, response);
                    }
                });
                return request;
            };
        };
    }
    /** Path the outgoing get function for breadcrumbs. */ // eslint-disable-next-line @typescript-eslint/no-explicit-any
    _getPatchOutgoingGetFunction(clientRequest) {
        return (_original)=>{
            // Re-implement http.get. This needs to be done (instead of using
            // getPatchOutgoingRequestFunction to patch it) because we need to
            // set the trace context header before the returned http.ClientRequest is
            // ended. The Node.js docs state that the only differences between
            // request and get are that (1) get defaults to the HTTP GET method and
            // (2) the returned request object is ended immediately. The former is
            // already true (at least in supported Node versions up to v10), so we
            // simply follow the latter. Ref:
            // https://nodejs.org/dist/latest/docs/api/http.html#http_http_get_options_callback
            // https://github.com/googleapis/cloud-trace-nodejs/blob/master/src/instrumentations/instrumentation-http.ts#L198
            return function outgoingGetRequest(...args) {
                const req = clientRequest(...args);
                req.end();
                return req;
            };
        };
    }
}
/**
 * This is a minimal version of `wrap` from shimmer:
 * https://github.com/othiym23/shimmer/blob/master/index.js
 *
 * In contrast to the original implementation, this version does not allow to unwrap,
 * and does not make it clear that the method is wrapped.
 * This is necessary because we want to wrap the http module with our own code,
 * while still allowing to use the HttpInstrumentation from OTEL.
 *
 * Without this, if we'd just use `wrap` from shimmer, the OTEL instrumentation would remove our wrapping,
 * because it only allows any module to be wrapped a single time.
 */ function stealthWrap(nodule, name, wrapper) {
    const original = nodule[name];
    const wrapped = wrapper(original);
    defineProperty(nodule, name, wrapped);
    return wrapped;
}
// Sets a property on an object, preserving its enumerability.
function defineProperty(obj, name, value) {
    const enumerable = !!obj[name] && Object.prototype.propertyIsEnumerable.call(obj, name);
    Object.defineProperty(obj, name, {
        configurable: true,
        enumerable: enumerable,
        writable: true,
        value: value
    });
}
/** Add a breadcrumb for outgoing requests. */ function addRequestBreadcrumb(request, response) {
    const data = getBreadcrumbData(request);
    const statusCode = response.statusCode;
    const level = core$1.getBreadcrumbLogLevelFromHttpStatusCode(statusCode);
    core$1.addBreadcrumb({
        category: 'http',
        data: {
            status_code: statusCode,
            ...data
        },
        type: 'http',
        level
    }, {
        event: 'response',
        request,
        response
    });
}
function getBreadcrumbData(request) {
    try {
        // `request.host` does not contain the port, but the host header does
        const host = request.getHeader('host') || request.host;
        const url = new URL(request.path, `${request.protocol}//${host}`);
        const parsedUrl = core$1.parseUrl(url.toString());
        const data = {
            url: core$1.getSanitizedUrlString(parsedUrl),
            'http.method': request.method || 'GET'
        };
        if (parsedUrl.search) {
            data['http.query'] = parsedUrl.search;
        }
        if (parsedUrl.hash) {
            data['http.fragment'] = parsedUrl.hash;
        }
        return data;
    } catch (e) {
        return {};
    }
}
/**
 * This method patches the request object to capture the body.
 * Instead of actually consuming the streamed body ourselves, which has potential side effects,
 * we monkey patch `req.on('data')` to intercept the body chunks.
 * This way, we only read the body if the user also consumes the body, ensuring we do not change any behavior in unexpected ways.
 */ function patchRequestToCaptureBody(req, isolationScope) {
    const chunks = [];
    function getChunksSize() {
        return chunks.reduce((acc, chunk)=>acc + chunk.byteLength, 0);
    }
    /**
   * We need to keep track of the original callbacks, in order to be able to remove listeners again.
   * Since `off` depends on having the exact same function reference passed in, we need to be able to map
   * original listeners to our wrapped ones.
   */ const callbackMap = new WeakMap();
    try {
        // eslint-disable-next-line @typescript-eslint/unbound-method
        req.on = new Proxy(req.on, {
            apply: (target, thisArg, args)=>{
                const [event, listener, ...restArgs] = args;
                if (event === 'data') {
                    const callback = new Proxy(listener, {
                        apply: (target, thisArg, args)=>{
                            // If we have already read more than the max body length, we stop addiing chunks
                            // To avoid growing the memory indefinitely if a respons is e.g. streamed
                            if (getChunksSize() < MAX_BODY_BYTE_LENGTH) {
                                const chunk = args[0];
                                chunks.push(chunk);
                            } else if (debugBuild.DEBUG_BUILD) {
                                core$1.logger.log(`Dropping request body chunk because it maximum body length of ${MAX_BODY_BYTE_LENGTH}b is exceeded.`);
                            }
                            return Reflect.apply(target, thisArg, args);
                        }
                    });
                    callbackMap.set(listener, callback);
                    return Reflect.apply(target, thisArg, [
                        event,
                        callback,
                        ...restArgs
                    ]);
                }
                if (event === 'end') {
                    const callback = new Proxy(listener, {
                        apply: (target, thisArg, args)=>{
                            try {
                                const body = Buffer.concat(chunks).toString('utf-8');
                                if (body) {
                                    const normalizedRequest = {
                                        data: body
                                    };
                                    isolationScope.setSDKProcessingMetadata({
                                        normalizedRequest
                                    });
                                }
                            } catch (e2) {
                            // ignore errors here
                            }
                            return Reflect.apply(target, thisArg, args);
                        }
                    });
                    callbackMap.set(listener, callback);
                    return Reflect.apply(target, thisArg, [
                        event,
                        callback,
                        ...restArgs
                    ]);
                }
                return Reflect.apply(target, thisArg, args);
            }
        });
        // Ensure we also remove callbacks correctly
        // eslint-disable-next-line @typescript-eslint/unbound-method
        req.off = new Proxy(req.off, {
            apply: (target, thisArg, args)=>{
                const [, listener] = args;
                const callback = callbackMap.get(listener);
                if (callback) {
                    callbackMap.delete(listener);
                    const modifiedArgs = args.slice();
                    modifiedArgs[1] = callback;
                    return Reflect.apply(target, thisArg, modifiedArgs);
                }
                return Reflect.apply(target, thisArg, args);
            }
        });
    } catch (e3) {
    // ignore errors if we can't patch stuff
    }
}
exports.SentryHttpInstrumentation = SentryHttpInstrumentation; //# sourceMappingURL=SentryHttpInstrumentation.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/http/index.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const api = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js [instrumentation] (ecmascript)");
const instrumentationHttp = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-http@0.56.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-http/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const opentelemetry = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+opentelemetry@8.46.0_@opentelemetry+api@1.9.0_@opentelemetry+core@1.30.1_@opent_9f3efdcc19a3583a187ce7ebb26bfca9/node_modules/@sentry/opentelemetry/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const addOriginToSpan = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/addOriginToSpan.js [instrumentation] (ecmascript)");
const getRequestUrl = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/getRequestUrl.js [instrumentation] (ecmascript)");
const SentryHttpInstrumentation = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/http/SentryHttpInstrumentation.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Http';
const INSTRUMENTATION_NAME = '@opentelemetry_sentry-patched/instrumentation-http';
const instrumentSentryHttp = instrument.generateInstrumentOnce(`${INTEGRATION_NAME}.sentry`, (options)=>{
    return new SentryHttpInstrumentation.SentryHttpInstrumentation({
        breadcrumbs: _optionalChain([
            options,
            'optionalAccess',
            (_)=>_.breadcrumbs
        ]),
        ignoreOutgoingRequests: _optionalChain([
            options,
            'optionalAccess',
            (_2)=>_2.ignoreOutgoingRequests
        ])
    });
});
const instrumentOtelHttp = instrument.generateInstrumentOnce(INTEGRATION_NAME, (config)=>{
    const instrumentation = new instrumentationHttp.HttpInstrumentation(config);
    // We want to update the logger namespace so we can better identify what is happening here
    try {
        instrumentation['_diag'] = api.diag.createComponentLogger({
            namespace: INSTRUMENTATION_NAME
        });
        // @ts-expect-error We are writing a read-only property here...
        instrumentation.instrumentationName = INSTRUMENTATION_NAME;
    } catch (e) {
    // ignore errors here...
    }
    return instrumentation;
});
/**
 * Instrument the HTTP and HTTPS modules.
 */ const instrumentHttp = (options = {})=>{
    // This is the "regular" OTEL instrumentation that emits spans
    if (options.spans !== false) {
        const instrumentationConfig = getConfigWithDefaults(options);
        instrumentOtelHttp(instrumentationConfig);
    }
    // This is the Sentry-specific instrumentation that isolates requests & creates breadcrumbs
    // Note that this _has_ to be wrapped after the OTEL instrumentation,
    // otherwise the isolation will not work correctly
    instrumentSentryHttp(options);
};
/**
 * The http integration instruments Node's internal http and https modules.
 * It creates breadcrumbs and spans for outgoing HTTP requests which will be attached to the currently active span.
 */ const httpIntegration = core.defineIntegration((options = {})=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentHttp(options);
        }
    };
});
/**
 * Determines if @param req is a ClientRequest, meaning the request was created within the express app
 * and it's an outgoing request.
 * Checking for properties instead of using `instanceOf` to avoid importing the request classes.
 */ function _isClientRequest(req) {
    return 'outputData' in req && 'outputSize' in req && !('client' in req) && !('statusCode' in req);
}
/**
 * Detects if an incoming request is a prefetch request.
 */ function isKnownPrefetchRequest(req) {
    // Currently only handles Next.js prefetch requests but may check other frameworks in the future.
    return req.headers['next-router-prefetch'] === '1';
}
function getConfigWithDefaults(options = {}) {
    const instrumentationConfig = {
        ..._optionalChain([
            options,
            'access',
            (_3)=>_3.instrumentation,
            'optionalAccess',
            (_4)=>_4._experimentalConfig
        ]),
        disableIncomingRequestInstrumentation: options.disableIncomingRequestSpans,
        ignoreOutgoingRequestHook: (request)=>{
            const url = getRequestUrl.getRequestUrl(request);
            if (!url) {
                return false;
            }
            const _ignoreOutgoingRequests = options.ignoreOutgoingRequests;
            if (_ignoreOutgoingRequests && _ignoreOutgoingRequests(url, request)) {
                return true;
            }
            return false;
        },
        ignoreIncomingRequestHook: (request)=>{
            // request.url is the only property that holds any information about the url
            // it only consists of the URL path and query string (if any)
            const urlPath = request.url;
            const method = _optionalChain([
                request,
                'access',
                (_5)=>_5.method,
                'optionalAccess',
                (_6)=>_6.toUpperCase,
                'call',
                (_7)=>_7()
            ]);
            // We do not capture OPTIONS/HEAD requests as transactions
            if (method === 'OPTIONS' || method === 'HEAD') {
                return true;
            }
            const _ignoreIncomingRequests = options.ignoreIncomingRequests;
            if (urlPath && _ignoreIncomingRequests && _ignoreIncomingRequests(urlPath, request)) {
                return true;
            }
            return false;
        },
        requireParentforOutgoingSpans: false,
        requireParentforIncomingSpans: false,
        requestHook: (span, req)=>{
            addOriginToSpan.addOriginToSpan(span, 'auto.http.otel.http');
            if (!_isClientRequest(req) && isKnownPrefetchRequest(req)) {
                span.setAttribute('sentry.http.prefetch', true);
            }
            _optionalChain([
                options,
                'access',
                (_8)=>_8.instrumentation,
                'optionalAccess',
                (_9)=>_9.requestHook,
                'optionalCall',
                (_10)=>_10(span, req)
            ]);
        },
        responseHook: (span, res)=>{
            const client = opentelemetry.getClient();
            if (client && // eslint-disable-next-line deprecation/deprecation
            client.getOptions().autoSessionTracking !== false && options.trackIncomingRequestsAsSessions !== false) {
                setImmediate(()=>{
                    client['_captureRequestSession']();
                });
            }
            _optionalChain([
                options,
                'access',
                (_11)=>_11.instrumentation,
                'optionalAccess',
                (_12)=>_12.responseHook,
                'optionalCall',
                (_13)=>_13(span, res)
            ]);
        },
        applyCustomAttributesOnSpan: (span, request, response)=>{
            _optionalChain([
                options,
                'access',
                (_14)=>_14.instrumentation,
                'optionalAccess',
                (_15)=>_15.applyCustomAttributesOnSpan,
                'optionalCall',
                (_16)=>_16(span, request, response)
            ]);
        }
    };
    return instrumentationConfig;
}
exports.httpIntegration = httpIntegration;
exports.instrumentOtelHttp = instrumentOtelHttp;
exports.instrumentSentryHttp = instrumentSentryHttp; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/node-fetch.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentation = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation@0.56.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation/build/esm/index.js [instrumentation] (ecmascript)");
const instrumentationUndici = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-undici@0.9.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-undici/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const opentelemetry = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+opentelemetry@8.46.0_@opentelemetry+api@1.9.0_@opentelemetry+core@1.30.1_@opent_9f3efdcc19a3583a187ce7ebb26bfca9/node_modules/@sentry/opentelemetry/build/cjs/index.js [instrumentation] (ecmascript)");
const _nativeNodeFetchIntegration = (options = {})=>{
    const _breadcrumbs = typeof options.breadcrumbs === 'undefined' ? true : options.breadcrumbs;
    const _ignoreOutgoingRequests = options.ignoreOutgoingRequests;
    return {
        name: 'NodeFetch',
        setupOnce () {
            const propagationDecisionMap = new core.LRUMap(100);
            const instrumentation$1 = new instrumentationUndici.UndiciInstrumentation({
                requireParentforSpans: false,
                ignoreRequestHook: (request)=>{
                    const url = getAbsoluteUrl(request.origin, request.path);
                    const shouldIgnore = _ignoreOutgoingRequests && url && _ignoreOutgoingRequests(url);
                    if (shouldIgnore) {
                        return true;
                    }
                    // If tracing is disabled, we still want to propagate traces
                    // So we do that manually here, matching what the instrumentation does otherwise
                    if (!core.hasTracingEnabled()) {
                        const tracePropagationTargets = _optionalChain([
                            core.getClient,
                            'call',
                            (_2)=>_2(),
                            'optionalAccess',
                            (_3)=>_3.getOptions,
                            'call',
                            (_4)=>_4(),
                            'access',
                            (_5)=>_5.tracePropagationTargets
                        ]);
                        const addedHeaders = opentelemetry.shouldPropagateTraceForUrl(url, tracePropagationTargets, propagationDecisionMap) ? core.getTraceData() : {};
                        const requestHeaders = request.headers;
                        if (Array.isArray(requestHeaders)) {
                            Object.entries(addedHeaders).forEach((headers)=>requestHeaders.push(...headers));
                        } else {
                            request.headers += Object.entries(addedHeaders).map(([k, v])=>`${k}: ${v}\r\n`).join('');
                        }
                        // Prevent starting a span for this request
                        return true;
                    }
                    return false;
                },
                startSpanHook: ()=>{
                    return {
                        [core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.http.otel.node_fetch'
                    };
                },
                responseHook: (_, { request, response })=>{
                    if (_breadcrumbs) {
                        addRequestBreadcrumb(request, response);
                    }
                }
            });
            instrumentation.registerInstrumentations({
                instrumentations: [
                    instrumentation$1
                ]
            });
        }
    };
};
const nativeNodeFetchIntegration = core.defineIntegration(_nativeNodeFetchIntegration);
/** Add a breadcrumb for outgoing requests. */ function addRequestBreadcrumb(request, response) {
    const data = getBreadcrumbData(request);
    const statusCode = response.statusCode;
    const level = core.getBreadcrumbLogLevelFromHttpStatusCode(statusCode);
    core.addBreadcrumb({
        category: 'http',
        data: {
            status_code: statusCode,
            ...data
        },
        type: 'http',
        level
    }, {
        event: 'response',
        request,
        response
    });
}
function getBreadcrumbData(request) {
    try {
        const url = new URL(request.path, request.origin);
        const parsedUrl = core.parseUrl(url.toString());
        const data = {
            url: core.getSanitizedUrlString(parsedUrl),
            'http.method': request.method || 'GET'
        };
        if (parsedUrl.search) {
            data['http.query'] = parsedUrl.search;
        }
        if (parsedUrl.hash) {
            data['http.fragment'] = parsedUrl.hash;
        }
        return data;
    } catch (e) {
        return {};
    }
}
// Matching the behavior of the base instrumentation
function getAbsoluteUrl(origin, path = '/') {
    const url = `${origin}`;
    if (url.endsWith('/') && path.startsWith('/')) {
        return `${url}${path.slice(1)}`;
    }
    if (!url.endsWith('/') && !path.startsWith('/')) {
        return `${url}/${path.slice(1)}`;
    }
    return `${url}${path}`;
}
exports.nativeNodeFetchIntegration = nativeNodeFetchIntegration; //# sourceMappingURL=node-fetch.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/fs.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationFs = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-fs@0.18.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-fs/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'FileSystem';
/**
 * This integration will create spans for `fs` API operations, like reading and writing files.
 *
 * **WARNING:** This integration may add significant overhead to your application. Especially in scenarios with a lot of
 * file I/O, like for example when running a framework dev server, including this integration can massively slow down
 * your application.
 *
 * @param options Configuration for this integration.
 */ const fsIntegration = core.defineIntegration((options = {})=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>new instrumentationFs.FsInstrumentation({
                    requireParentSpan: true,
                    endHook (functionName, { args, span, error }) {
                        span.updateName(`fs.${functionName}`);
                        span.setAttributes({
                            [core.SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'file',
                            [core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.file.fs'
                        });
                        if (options.recordErrorMessagesAsSpanAttributes) {
                            if (typeof args[0] === 'string' && FS_OPERATIONS_WITH_PATH_ARG.includes(functionName)) {
                                span.setAttribute('path_argument', args[0]);
                            } else if (typeof args[0] === 'string' && typeof args[1] === 'string' && FS_OPERATIONS_WITH_TARGET_PATH.includes(functionName)) {
                                span.setAttribute('target_argument', args[0]);
                                span.setAttribute('path_argument', args[1]);
                            } else if (typeof args[0] === 'string' && FS_OPERATIONS_WITH_PREFIX.includes(functionName)) {
                                span.setAttribute('prefix_argument', args[0]);
                            } else if (typeof args[0] === 'string' && typeof args[1] === 'string' && FS_OPERATIONS_WITH_EXISTING_PATH_NEW_PATH.includes(functionName)) {
                                span.setAttribute('existing_path_argument', args[0]);
                                span.setAttribute('new_path_argument', args[1]);
                            } else if (typeof args[0] === 'string' && typeof args[1] === 'string' && FS_OPERATIONS_WITH_SRC_DEST.includes(functionName)) {
                                span.setAttribute('src_argument', args[0]);
                                span.setAttribute('dest_argument', args[1]);
                            } else if (typeof args[0] === 'string' && typeof args[1] === 'string' && FS_OPERATIONS_WITH_OLD_PATH_NEW_PATH.includes(functionName)) {
                                span.setAttribute('old_path_argument', args[0]);
                                span.setAttribute('new_path_argument', args[1]);
                            }
                        }
                        if (error && options.recordErrorMessagesAsSpanAttributes) {
                            span.setAttribute('fs_error', error.message);
                        }
                    }
                }))();
        }
    };
});
const FS_OPERATIONS_WITH_OLD_PATH_NEW_PATH = [
    'rename',
    'renameSync'
];
const FS_OPERATIONS_WITH_SRC_DEST = [
    'copyFile',
    'cp',
    'copyFileSync',
    'cpSync'
];
const FS_OPERATIONS_WITH_EXISTING_PATH_NEW_PATH = [
    'link',
    'linkSync'
];
const FS_OPERATIONS_WITH_PREFIX = [
    'mkdtemp',
    'mkdtempSync'
];
const FS_OPERATIONS_WITH_TARGET_PATH = [
    'symlink',
    'symlinkSync'
];
const FS_OPERATIONS_WITH_PATH_ARG = [
    'access',
    'appendFile',
    'chmod',
    'chown',
    'exists',
    'mkdir',
    'lchown',
    'lstat',
    'lutimes',
    'open',
    'opendir',
    'readdir',
    'readFile',
    'readlink',
    'realpath',
    'realpath.native',
    'rm',
    'rmdir',
    'stat',
    'truncate',
    'unlink',
    'utimes',
    'writeFile',
    'accessSync',
    'appendFileSync',
    'chmodSync',
    'chownSync',
    'existsSync',
    'lchownSync',
    'lstatSync',
    'lutimesSync',
    'opendirSync',
    'mkdirSync',
    'openSync',
    'readdirSync',
    'readFileSync',
    'readlinkSync',
    'realpathSync',
    'realpathSync.native',
    'rmdirSync',
    'rmSync',
    'statSync',
    'truncateSync',
    'unlinkSync',
    'utimesSync',
    'writeFileSync'
];
exports.fsIntegration = fsIntegration; //# sourceMappingURL=fs.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/console.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const util = __turbopack_context__.r("[externals]/node:util [external] (node:util, cjs)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Console';
/**
 * Capture console logs as breadcrumbs.
 */ const consoleIntegration = core.defineIntegration(()=>{
    return {
        name: INTEGRATION_NAME,
        setup (client) {
            core.addConsoleInstrumentationHandler(({ args, level })=>{
                if (core.getClient() !== client) {
                    return;
                }
                core.addBreadcrumb({
                    category: 'console',
                    level: core.severityLevelFromString(level),
                    message: core.truncate(util.format.apply(undefined, args), 2048)
                }, {
                    input: [
                        ...args
                    ],
                    level
                });
            });
        }
    };
});
exports.consoleIntegration = consoleIntegration; //# sourceMappingURL=console.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/context.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const node_child_process = __turbopack_context__.r("[externals]/node:child_process [external] (node:child_process, cjs)");
const node_fs = __turbopack_context__.r("[externals]/node:fs [external] (node:fs, cjs)");
const os = __turbopack_context__.r("[externals]/node:os [external] (node:os, cjs)");
const node_path = __turbopack_context__.r("[externals]/node:path [external] (node:path, cjs)");
const util = __turbopack_context__.r("[externals]/node:util [external] (node:util, cjs)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
/* eslint-disable max-lines */ const readFileAsync = util.promisify(node_fs.readFile);
const readDirAsync = util.promisify(node_fs.readdir);
// Process enhanced with methods from Node 18, 20, 22 as @types/node
// is on `14.18.0` to match minimum version requirements of the SDK
const INTEGRATION_NAME = 'Context';
const _nodeContextIntegration = (options = {})=>{
    let cachedContext;
    const _options = {
        app: true,
        os: true,
        device: true,
        culture: true,
        cloudResource: true,
        ...options
    };
    /** Add contexts to the event. Caches the context so we only look it up once. */ async function addContext(event) {
        if (cachedContext === undefined) {
            cachedContext = _getContexts();
        }
        const updatedContext = _updateContext(await cachedContext);
        event.contexts = {
            ...event.contexts,
            app: {
                ...updatedContext.app,
                ..._optionalChain([
                    event,
                    'access',
                    (_)=>_.contexts,
                    'optionalAccess',
                    (_2)=>_2.app
                ])
            },
            os: {
                ...updatedContext.os,
                ..._optionalChain([
                    event,
                    'access',
                    (_3)=>_3.contexts,
                    'optionalAccess',
                    (_4)=>_4.os
                ])
            },
            device: {
                ...updatedContext.device,
                ..._optionalChain([
                    event,
                    'access',
                    (_5)=>_5.contexts,
                    'optionalAccess',
                    (_6)=>_6.device
                ])
            },
            culture: {
                ...updatedContext.culture,
                ..._optionalChain([
                    event,
                    'access',
                    (_7)=>_7.contexts,
                    'optionalAccess',
                    (_8)=>_8.culture
                ])
            },
            cloud_resource: {
                ...updatedContext.cloud_resource,
                ..._optionalChain([
                    event,
                    'access',
                    (_9)=>_9.contexts,
                    'optionalAccess',
                    (_10)=>_10.cloud_resource
                ])
            }
        };
        return event;
    }
    /** Get the contexts from node. */ async function _getContexts() {
        const contexts = {};
        if (_options.os) {
            contexts.os = await getOsContext();
        }
        if (_options.app) {
            contexts.app = getAppContext();
        }
        if (_options.device) {
            contexts.device = getDeviceContext(_options.device);
        }
        if (_options.culture) {
            const culture = getCultureContext();
            if (culture) {
                contexts.culture = culture;
            }
        }
        if (_options.cloudResource) {
            contexts.cloud_resource = getCloudResourceContext();
        }
        return contexts;
    }
    return {
        name: INTEGRATION_NAME,
        processEvent (event) {
            return addContext(event);
        }
    };
};
/**
 * Capture context about the environment and the device that the client is running on, to events.
 */ const nodeContextIntegration = core.defineIntegration(_nodeContextIntegration);
/**
 * Updates the context with dynamic values that can change
 */ function _updateContext(contexts) {
    // Only update properties if they exist
    if (_optionalChain([
        contexts,
        'optionalAccess',
        (_11)=>_11.app,
        'optionalAccess',
        (_12)=>_12.app_memory
    ])) {
        contexts.app.app_memory = process.memoryUsage().rss;
    }
    if (_optionalChain([
        contexts,
        'optionalAccess',
        (_13)=>_13.app,
        'optionalAccess',
        (_14)=>_14.free_memory
    ]) && typeof process.availableMemory === 'function') {
        const freeMemory = _optionalChain([
            process,
            'access',
            (_15)=>_15.availableMemory,
            'optionalCall',
            (_16)=>_16()
        ]);
        if (freeMemory != null) {
            contexts.app.free_memory = freeMemory;
        }
    }
    if (_optionalChain([
        contexts,
        'optionalAccess',
        (_17)=>_17.device,
        'optionalAccess',
        (_18)=>_18.free_memory
    ])) {
        contexts.device.free_memory = os.freemem();
    }
    return contexts;
}
/**
 * Returns the operating system context.
 *
 * Based on the current platform, this uses a different strategy to provide the
 * most accurate OS information. Since this might involve spawning subprocesses
 * or accessing the file system, this should only be executed lazily and cached.
 *
 *  - On macOS (Darwin), this will execute the `sw_vers` utility. The context
 *    has a `name`, `version`, `build` and `kernel_version` set.
 *  - On Linux, this will try to load a distribution release from `/etc` and set
 *    the `name`, `version` and `kernel_version` fields.
 *  - On all other platforms, only a `name` and `version` will be returned. Note
 *    that `version` might actually be the kernel version.
 */ async function getOsContext() {
    const platformId = os.platform();
    switch(platformId){
        case 'darwin':
            return getDarwinInfo();
        case 'linux':
            return getLinuxInfo();
        default:
            return {
                name: PLATFORM_NAMES[platformId] || platformId,
                version: os.release()
            };
    }
}
function getCultureContext() {
    try {
        if (typeof process.versions.icu !== 'string') {
            // Node was built without ICU support
            return;
        }
        // Check that node was built with full Intl support. Its possible it was built without support for non-English
        // locales which will make resolvedOptions inaccurate
        //
        // https://nodejs.org/api/intl.html#detecting-internationalization-support
        const january = new Date(9e8);
        const spanish = new Intl.DateTimeFormat('es', {
            month: 'long'
        });
        if (spanish.format(january) === 'enero') {
            const options = Intl.DateTimeFormat().resolvedOptions();
            return {
                locale: options.locale,
                timezone: options.timeZone
            };
        }
    } catch (err) {
    //
    }
    return;
}
/**
 * Get app context information from process
 */ function getAppContext() {
    const app_memory = process.memoryUsage().rss;
    const app_start_time = new Date(Date.now() - process.uptime() * 1000).toISOString();
    // https://nodejs.org/api/process.html#processavailablememory
    const appContext = {
        app_start_time,
        app_memory
    };
    if (typeof process.availableMemory === 'function') {
        const freeMemory = _optionalChain([
            process,
            'access',
            (_19)=>_19.availableMemory,
            'optionalCall',
            (_20)=>_20()
        ]);
        if (freeMemory != null) {
            appContext.free_memory = freeMemory;
        }
    }
    return appContext;
}
/**
 * Gets device information from os
 */ function getDeviceContext(deviceOpt) {
    const device = {};
    // Sometimes os.uptime() throws due to lacking permissions: https://github.com/getsentry/sentry-javascript/issues/8202
    let uptime;
    try {
        uptime = os.uptime && os.uptime();
    } catch (e) {
    // noop
    }
    // os.uptime or its return value seem to be undefined in certain environments (e.g. Azure functions).
    // Hence, we only set boot time, if we get a valid uptime value.
    // @see https://github.com/getsentry/sentry-javascript/issues/5856
    if (typeof uptime === 'number') {
        device.boot_time = new Date(Date.now() - uptime * 1000).toISOString();
    }
    device.arch = os.arch();
    if (deviceOpt === true || deviceOpt.memory) {
        device.memory_size = os.totalmem();
        device.free_memory = os.freemem();
    }
    if (deviceOpt === true || deviceOpt.cpu) {
        const cpuInfo = os.cpus();
        const firstCpu = cpuInfo && cpuInfo[0];
        if (firstCpu) {
            device.processor_count = cpuInfo.length;
            device.cpu_description = firstCpu.model;
            device.processor_frequency = firstCpu.speed;
        }
    }
    return device;
}
/** Mapping of Node's platform names to actual OS names. */ const PLATFORM_NAMES = {
    aix: 'IBM AIX',
    freebsd: 'FreeBSD',
    openbsd: 'OpenBSD',
    sunos: 'SunOS',
    win32: 'Windows'
};
/** Linux version file to check for a distribution. */ /** Mapping of linux release files located in /etc to distributions. */ const LINUX_DISTROS = [
    {
        name: 'fedora-release',
        distros: [
            'Fedora'
        ]
    },
    {
        name: 'redhat-release',
        distros: [
            'Red Hat Linux',
            'Centos'
        ]
    },
    {
        name: 'redhat_version',
        distros: [
            'Red Hat Linux'
        ]
    },
    {
        name: 'SuSE-release',
        distros: [
            'SUSE Linux'
        ]
    },
    {
        name: 'lsb-release',
        distros: [
            'Ubuntu Linux',
            'Arch Linux'
        ]
    },
    {
        name: 'debian_version',
        distros: [
            'Debian'
        ]
    },
    {
        name: 'debian_release',
        distros: [
            'Debian'
        ]
    },
    {
        name: 'arch-release',
        distros: [
            'Arch Linux'
        ]
    },
    {
        name: 'gentoo-release',
        distros: [
            'Gentoo Linux'
        ]
    },
    {
        name: 'novell-release',
        distros: [
            'SUSE Linux'
        ]
    },
    {
        name: 'alpine-release',
        distros: [
            'Alpine Linux'
        ]
    }
];
/** Functions to extract the OS version from Linux release files. */ const LINUX_VERSIONS = {
    alpine: (content)=>content,
    arch: (content)=>matchFirst(/distrib_release=(.*)/, content),
    centos: (content)=>matchFirst(/release ([^ ]+)/, content),
    debian: (content)=>content,
    fedora: (content)=>matchFirst(/release (..)/, content),
    mint: (content)=>matchFirst(/distrib_release=(.*)/, content),
    red: (content)=>matchFirst(/release ([^ ]+)/, content),
    suse: (content)=>matchFirst(/VERSION = (.*)\n/, content),
    ubuntu: (content)=>matchFirst(/distrib_release=(.*)/, content)
};
/**
 * Executes a regular expression with one capture group.
 *
 * @param regex A regular expression to execute.
 * @param text Content to execute the RegEx on.
 * @returns The captured string if matched; otherwise undefined.
 */ function matchFirst(regex, text) {
    const match = regex.exec(text);
    return match ? match[1] : undefined;
}
/** Loads the macOS operating system context. */ async function getDarwinInfo() {
    // Default values that will be used in case no operating system information
    // can be loaded. The default version is computed via heuristics from the
    // kernel version, but the build ID is missing.
    const darwinInfo = {
        kernel_version: os.release(),
        name: 'Mac OS X',
        version: `10.${Number(os.release().split('.')[0]) - 4}`
    };
    try {
        // We try to load the actual macOS version by executing the `sw_vers` tool.
        // This tool should be available on every standard macOS installation. In
        // case this fails, we stick with the values computed above.
        const output = await new Promise((resolve, reject)=>{
            node_child_process.execFile('/usr/bin/sw_vers', (error, stdout)=>{
                if (error) {
                    reject(error);
                    return;
                }
                resolve(stdout);
            });
        });
        darwinInfo.name = matchFirst(/^ProductName:\s+(.*)$/m, output);
        darwinInfo.version = matchFirst(/^ProductVersion:\s+(.*)$/m, output);
        darwinInfo.build = matchFirst(/^BuildVersion:\s+(.*)$/m, output);
    } catch (e) {
    // ignore
    }
    return darwinInfo;
}
/** Returns a distribution identifier to look up version callbacks. */ function getLinuxDistroId(name) {
    return name.split(' ')[0].toLowerCase();
}
/** Loads the Linux operating system context. */ async function getLinuxInfo() {
    // By default, we cannot assume anything about the distribution or Linux
    // version. `os.release()` returns the kernel version and we assume a generic
    // "Linux" name, which will be replaced down below.
    const linuxInfo = {
        kernel_version: os.release(),
        name: 'Linux'
    };
    try {
        // We start guessing the distribution by listing files in the /etc
        // directory. This is were most Linux distributions (except Knoppix) store
        // release files with certain distribution-dependent meta data. We search
        // for exactly one known file defined in `LINUX_DISTROS` and exit if none
        // are found. In case there are more than one file, we just stick with the
        // first one.
        const etcFiles = await readDirAsync('/etc');
        const distroFile = LINUX_DISTROS.find((file)=>etcFiles.includes(file.name));
        if (!distroFile) {
            return linuxInfo;
        }
        // Once that file is known, load its contents. To make searching in those
        // files easier, we lowercase the file contents. Since these files are
        // usually quite small, this should not allocate too much memory and we only
        // hold on to it for a very short amount of time.
        const distroPath = node_path.join('/etc', distroFile.name);
        const contents = (await readFileAsync(distroPath, {
            encoding: 'utf-8'
        })).toLowerCase();
        // Some Linux distributions store their release information in the same file
        // (e.g. RHEL and Centos). In those cases, we scan the file for an
        // identifier, that basically consists of the first word of the linux
        // distribution name (e.g. "red" for Red Hat). In case there is no match, we
        // just assume the first distribution in our list.
        const { distros } = distroFile;
        linuxInfo.name = distros.find((d)=>contents.indexOf(getLinuxDistroId(d)) >= 0) || distros[0];
        // Based on the found distribution, we can now compute the actual version
        // number. This is different for every distribution, so several strategies
        // are computed in `LINUX_VERSIONS`.
        const id = getLinuxDistroId(linuxInfo.name);
        linuxInfo.version = _optionalChain([
            LINUX_VERSIONS,
            'access',
            (_21)=>_21[id],
            'optionalCall',
            (_22)=>_22(contents)
        ]);
    } catch (e) {
    // ignore
    }
    return linuxInfo;
}
/**
 * Grabs some information about hosting provider based on best effort.
 */ function getCloudResourceContext() {
    if (process.env.VERCEL) {
        // https://vercel.com/docs/concepts/projects/environment-variables/system-environment-variables#system-environment-variables
        return {
            'cloud.provider': 'vercel',
            'cloud.region': process.env.VERCEL_REGION
        };
    } else if (process.env.AWS_REGION) {
        // https://docs.aws.amazon.com/lambda/latest/dg/configuration-envvars.html
        return {
            'cloud.provider': 'aws',
            'cloud.region': process.env.AWS_REGION,
            'cloud.platform': process.env.AWS_EXECUTION_ENV
        };
    } else if (process.env.GCP_PROJECT) {
        // https://cloud.google.com/composer/docs/how-to/managing/environment-variables#reserved_variables
        return {
            'cloud.provider': 'gcp'
        };
    } else if (process.env.ALIYUN_REGION_ID) {
        // TODO: find where I found these environment variables - at least gc.github.com returns something
        return {
            'cloud.provider': 'alibaba_cloud',
            'cloud.region': process.env.ALIYUN_REGION_ID
        };
    } else if (process.env.WEBSITE_SITE_NAME && process.env.REGION_NAME) {
        // https://learn.microsoft.com/en-us/azure/app-service/reference-app-settings?tabs=kudu%2Cdotnet#app-environment
        return {
            'cloud.provider': 'azure',
            'cloud.region': process.env.REGION_NAME
        };
    } else if (process.env.IBM_CLOUD_REGION) {
        // TODO: find where I found these environment variables - at least gc.github.com returns something
        return {
            'cloud.provider': 'ibm_cloud',
            'cloud.region': process.env.IBM_CLOUD_REGION
        };
    } else if (process.env.TENCENTCLOUD_REGION) {
        // https://www.tencentcloud.com/document/product/583/32748
        return {
            'cloud.provider': 'tencent_cloud',
            'cloud.region': process.env.TENCENTCLOUD_REGION,
            'cloud.account.id': process.env.TENCENTCLOUD_APPID,
            'cloud.availability_zone': process.env.TENCENTCLOUD_ZONE
        };
    } else if (process.env.NETLIFY) {
        // https://docs.netlify.com/configure-builds/environment-variables/#read-only-variables
        return {
            'cloud.provider': 'netlify'
        };
    } else if (process.env.FLY_REGION) {
        // https://fly.io/docs/reference/runtime-environment/
        return {
            'cloud.provider': 'fly.io',
            'cloud.region': process.env.FLY_REGION
        };
    } else if (process.env.DYNO) {
        // https://devcenter.heroku.com/articles/dynos#local-environment-variables
        return {
            'cloud.provider': 'heroku'
        };
    } else {
        return undefined;
    }
}
exports.getAppContext = getAppContext;
exports.getDeviceContext = getDeviceContext;
exports.nodeContextIntegration = nodeContextIntegration;
exports.readDirAsync = readDirAsync;
exports.readFileAsync = readFileAsync; //# sourceMappingURL=context.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/contextlines.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const node_fs = __turbopack_context__.r("[externals]/node:fs [external] (node:fs, cjs)");
const node_readline = __turbopack_context__.r("[externals]/node:readline [external] (node:readline, cjs)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const debugBuild = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/debug-build.js [instrumentation] (ecmascript)");
const LRU_FILE_CONTENTS_CACHE = new core.LRUMap(10);
const LRU_FILE_CONTENTS_FS_READ_FAILED = new core.LRUMap(20);
const DEFAULT_LINES_OF_CONTEXT = 7;
const INTEGRATION_NAME = 'ContextLines';
// Determines the upper bound of lineno/colno that we will attempt to read. Large colno values are likely to be
// minified code while large lineno values are likely to be bundled code.
// Exported for testing purposes.
const MAX_CONTEXTLINES_COLNO = 1000;
const MAX_CONTEXTLINES_LINENO = 10000;
/**
 * Get or init map value
 */ function emplace(map, key, contents) {
    const value = map.get(key);
    if (value === undefined) {
        map.set(key, contents);
        return contents;
    }
    return value;
}
/**
 * Determines if context lines should be skipped for a file.
 * - .min.(mjs|cjs|js) files are and not useful since they dont point to the original source
 * - node: prefixed modules are part of the runtime and cannot be resolved to a file
 * - data: skip json, wasm and inline js https://nodejs.org/api/esm.html#data-imports
 */ function shouldSkipContextLinesForFile(path) {
    // Test the most common prefix and extension first. These are the ones we
    // are most likely to see in user applications and are the ones we can break out of first.
    if (path.startsWith('node:')) return true;
    if (path.endsWith('.min.js')) return true;
    if (path.endsWith('.min.cjs')) return true;
    if (path.endsWith('.min.mjs')) return true;
    if (path.startsWith('data:')) return true;
    return false;
}
/**
 * Determines if we should skip contextlines based off the max lineno and colno values.
 */ function shouldSkipContextLinesForFrame(frame) {
    if (frame.lineno !== undefined && frame.lineno > MAX_CONTEXTLINES_LINENO) return true;
    if (frame.colno !== undefined && frame.colno > MAX_CONTEXTLINES_COLNO) return true;
    return false;
}
/**
 * Checks if we have all the contents that we need in the cache.
 */ function rangeExistsInContentCache(file, range) {
    const contents = LRU_FILE_CONTENTS_CACHE.get(file);
    if (contents === undefined) return false;
    for(let i = range[0]; i <= range[1]; i++){
        if (contents[i] === undefined) {
            return false;
        }
    }
    return true;
}
/**
 * Creates contiguous ranges of lines to read from a file. In the case where context lines overlap,
 * the ranges are merged to create a single range.
 */ function makeLineReaderRanges(lines, linecontext) {
    if (!lines.length) {
        return [];
    }
    let i = 0;
    const line = lines[0];
    if (typeof line !== 'number') {
        return [];
    }
    let current = makeContextRange(line, linecontext);
    const out = [];
    // eslint-disable-next-line no-constant-condition
    while(true){
        if (i === lines.length - 1) {
            out.push(current);
            break;
        }
        // If the next line falls into the current range, extend the current range to lineno + linecontext.
        const next = lines[i + 1];
        if (typeof next !== 'number') {
            break;
        }
        if (next <= current[1]) {
            current[1] = next + linecontext;
        } else {
            out.push(current);
            current = makeContextRange(next, linecontext);
        }
        i++;
    }
    return out;
}
/**
 * Extracts lines from a file and stores them in a cache.
 */ function getContextLinesFromFile(path, ranges, output) {
    return new Promise((resolve, _reject)=>{
        // It is important *not* to have any async code between createInterface and the 'line' event listener
        // as it will cause the 'line' event to
        // be emitted before the listener is attached.
        const stream = node_fs.createReadStream(path);
        const lineReaded = node_readline.createInterface({
            input: stream
        });
        // Init at zero and increment at the start of the loop because lines are 1 indexed.
        let lineNumber = 0;
        let currentRangeIndex = 0;
        const range = ranges[currentRangeIndex];
        if (range === undefined) {
            // We should never reach this point, but if we do, we should resolve the promise to prevent it from hanging.
            resolve();
            return;
        }
        let rangeStart = range[0];
        let rangeEnd = range[1];
        // We use this inside Promise.all, so we need to resolve the promise even if there is an error
        // to prevent Promise.all from short circuiting the rest.
        function onStreamError(e) {
            // Mark file path as failed to read and prevent multiple read attempts.
            LRU_FILE_CONTENTS_FS_READ_FAILED.set(path, 1);
            debugBuild.DEBUG_BUILD && core.logger.error(`Failed to read file: ${path}. Error: ${e}`);
            lineReaded.close();
            lineReaded.removeAllListeners();
            resolve();
        }
        // We need to handle the error event to prevent the process from crashing in < Node 16
        // https://github.com/nodejs/node/pull/31603
        stream.on('error', onStreamError);
        lineReaded.on('error', onStreamError);
        lineReaded.on('close', resolve);
        lineReaded.on('line', (line)=>{
            lineNumber++;
            if (lineNumber < rangeStart) return;
            // !Warning: This mutates the cache by storing the snipped line into the cache.
            output[lineNumber] = core.snipLine(line, 0);
            if (lineNumber >= rangeEnd) {
                if (currentRangeIndex === ranges.length - 1) {
                    // We need to close the file stream and remove listeners, else the reader will continue to run our listener;
                    lineReaded.close();
                    lineReaded.removeAllListeners();
                    return;
                }
                currentRangeIndex++;
                const range = ranges[currentRangeIndex];
                if (range === undefined) {
                    // This should never happen as it means we have a bug in the context.
                    lineReaded.close();
                    lineReaded.removeAllListeners();
                    return;
                }
                rangeStart = range[0];
                rangeEnd = range[1];
            }
        });
    });
}
/**
 * Adds surrounding (context) lines of the line that an exception occurred on to the event.
 * This is done by reading the file line by line and extracting the lines. The extracted lines are stored in
 * a cache to prevent multiple reads of the same file. Failures to read a file are similarly cached to prevent multiple
 * failing reads from happening.
 */ /* eslint-disable complexity */ async function addSourceContext(event, contextLines) {
    // keep a lookup map of which files we've already enqueued to read,
    // so we don't enqueue the same file multiple times which would cause multiple i/o reads
    const filesToLines = {};
    if (contextLines > 0 && _optionalChain([
        event,
        'access',
        (_)=>_.exception,
        'optionalAccess',
        (_2)=>_2.values
    ])) {
        for (const exception of event.exception.values){
            if (!_optionalChain([
                exception,
                'access',
                (_3)=>_3.stacktrace,
                'optionalAccess',
                (_4)=>_4.frames,
                'optionalAccess',
                (_5)=>_5.length
            ])) {
                continue;
            }
            // Maps preserve insertion order, so we iterate in reverse, starting at the
            // outermost frame and closer to where the exception has occurred (poor mans priority)
            for(let i = exception.stacktrace.frames.length - 1; i >= 0; i--){
                const frame = exception.stacktrace.frames[i];
                const filename = _optionalChain([
                    frame,
                    'optionalAccess',
                    (_6)=>_6.filename
                ]);
                if (!frame || typeof filename !== 'string' || typeof frame.lineno !== 'number' || shouldSkipContextLinesForFile(filename) || shouldSkipContextLinesForFrame(frame)) {
                    continue;
                }
                const filesToLinesOutput = filesToLines[filename];
                if (!filesToLinesOutput) filesToLines[filename] = [];
                // @ts-expect-error this is defined above
                filesToLines[filename].push(frame.lineno);
            }
        }
    }
    const files = Object.keys(filesToLines);
    if (files.length == 0) {
        return event;
    }
    const readlinePromises = [];
    for (const file of files){
        // If we failed to read this before, dont try reading it again.
        if (LRU_FILE_CONTENTS_FS_READ_FAILED.get(file)) {
            continue;
        }
        const filesToLineRanges = filesToLines[file];
        if (!filesToLineRanges) {
            continue;
        }
        // Sort ranges so that they are sorted by line increasing order and match how the file is read.
        filesToLineRanges.sort((a, b)=>a - b);
        // Check if the contents are already in the cache and if we can avoid reading the file again.
        const ranges = makeLineReaderRanges(filesToLineRanges, contextLines);
        if (ranges.every((r)=>rangeExistsInContentCache(file, r))) {
            continue;
        }
        const cache = emplace(LRU_FILE_CONTENTS_CACHE, file, {});
        readlinePromises.push(getContextLinesFromFile(file, ranges, cache));
    }
    // The promise rejections are caught in order to prevent them from short circuiting Promise.all
    await Promise.all(readlinePromises).catch(()=>{
        debugBuild.DEBUG_BUILD && core.logger.log('Failed to read one or more source files and resolve context lines');
    });
    // Perform the same loop as above, but this time we can assume all files are in the cache
    // and attempt to add source context to frames.
    if (contextLines > 0 && _optionalChain([
        event,
        'access',
        (_7)=>_7.exception,
        'optionalAccess',
        (_8)=>_8.values
    ])) {
        for (const exception of event.exception.values){
            if (exception.stacktrace && exception.stacktrace.frames && exception.stacktrace.frames.length > 0) {
                addSourceContextToFrames(exception.stacktrace.frames, contextLines, LRU_FILE_CONTENTS_CACHE);
            }
        }
    }
    return event;
}
/* eslint-enable complexity */ /** Adds context lines to frames */ function addSourceContextToFrames(frames, contextLines, cache) {
    for (const frame of frames){
        // Only add context if we have a filename and it hasn't already been added
        if (frame.filename && frame.context_line === undefined && typeof frame.lineno === 'number') {
            const contents = cache.get(frame.filename);
            if (contents === undefined) {
                continue;
            }
            addContextToFrame(frame.lineno, frame, contextLines, contents);
        }
    }
}
/**
 * Clears the context lines from a frame, used to reset a frame to its original state
 * if we fail to resolve all context lines for it.
 */ function clearLineContext(frame) {
    delete frame.pre_context;
    delete frame.context_line;
    delete frame.post_context;
}
/**
 * Resolves context lines before and after the given line number and appends them to the frame;
 */ function addContextToFrame(lineno, frame, contextLines, contents) {
    // When there is no line number in the frame, attaching context is nonsensical and will even break grouping.
    // We already check for lineno before calling this, but since StackFrame lineno ism optional, we check it again.
    if (frame.lineno === undefined || contents === undefined) {
        debugBuild.DEBUG_BUILD && core.logger.error('Cannot resolve context for frame with no lineno or file contents');
        return;
    }
    frame.pre_context = [];
    for(let i = makeRangeStart(lineno, contextLines); i < lineno; i++){
        // We always expect the start context as line numbers cannot be negative. If we dont find a line, then
        // something went wrong somewhere. Clear the context and return without adding any linecontext.
        const line = contents[i];
        if (line === undefined) {
            clearLineContext(frame);
            debugBuild.DEBUG_BUILD && core.logger.error(`Could not find line ${i} in file ${frame.filename}`);
            return;
        }
        frame.pre_context.push(line);
    }
    // We should always have the context line. If we dont, something went wrong, so we clear the context and return
    // without adding any linecontext.
    if (contents[lineno] === undefined) {
        clearLineContext(frame);
        debugBuild.DEBUG_BUILD && core.logger.error(`Could not find line ${lineno} in file ${frame.filename}`);
        return;
    }
    frame.context_line = contents[lineno];
    const end = makeRangeEnd(lineno, contextLines);
    frame.post_context = [];
    for(let i = lineno + 1; i <= end; i++){
        // Since we dont track when the file ends, we cant clear the context if we dont find a line as it could
        // just be that we reached the end of the file.
        const line = contents[i];
        if (line === undefined) {
            break;
        }
        frame.post_context.push(line);
    }
}
// Helper functions for generating line context ranges. They take a line number and the number of lines of context to
// include before and after the line and generate an inclusive range of indices.
// Compute inclusive end context range
function makeRangeStart(line, linecontext) {
    return Math.max(1, line - linecontext);
}
// Compute inclusive start context range
function makeRangeEnd(line, linecontext) {
    return line + linecontext;
}
// Determine start and end indices for context range (inclusive);
function makeContextRange(line, linecontext) {
    return [
        makeRangeStart(line, linecontext),
        makeRangeEnd(line, linecontext)
    ];
}
/** Exported only for tests, as a type-safe variant. */ const _contextLinesIntegration = (options = {})=>{
    const contextLines = options.frameContextLines !== undefined ? options.frameContextLines : DEFAULT_LINES_OF_CONTEXT;
    return {
        name: INTEGRATION_NAME,
        processEvent (event) {
            return addSourceContext(event, contextLines);
        }
    };
};
/**
 * Capture the lines before and after the frame's context.
 */ const contextLinesIntegration = core.defineIntegration(_contextLinesIntegration);
exports.MAX_CONTEXTLINES_COLNO = MAX_CONTEXTLINES_COLNO;
exports.MAX_CONTEXTLINES_LINENO = MAX_CONTEXTLINES_LINENO;
exports._contextLinesIntegration = _contextLinesIntegration;
exports.addContextToFrame = addContextToFrame;
exports.contextLinesIntegration = contextLinesIntegration; //# sourceMappingURL=contextlines.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/nodeVersion.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const NODE_VERSION = core.parseSemver(process.versions.node);
const NODE_MAJOR = NODE_VERSION.major;
exports.NODE_MAJOR = NODE_MAJOR;
exports.NODE_VERSION = NODE_VERSION; //# sourceMappingURL=nodeVersion.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/debug.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
let cachedDebuggerEnabled;
/**
 * Was the debugger enabled when this function was first called?
 */ async function isDebuggerEnabled() {
    if (cachedDebuggerEnabled === undefined) {
        try {
            // Node can be built without inspector support
            const inspector = await __turbopack_context__.r("[externals]/node:inspector [external] (node:inspector, cjs, async loader)")(__turbopack_context__.i);
            cachedDebuggerEnabled = !!inspector.url();
        } catch (_) {
            cachedDebuggerEnabled = false;
        }
    }
    return cachedDebuggerEnabled;
}
exports.isDebuggerEnabled = isDebuggerEnabled; //# sourceMappingURL=debug.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/local-variables/common.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
/**
 * The key used to store the local variables on the error object.
 */ const LOCAL_VARIABLES_KEY = '__SENTRY_ERROR_LOCAL_VARIABLES__';
/**
 * Creates a rate limiter that will call the disable callback when the rate limit is reached and the enable callback
 * when a timeout has occurred.
 * @param maxPerSecond Maximum number of calls per second
 * @param enable Callback to enable capture
 * @param disable Callback to disable capture
 * @returns A function to call to increment the rate limiter count
 */ function createRateLimiter(maxPerSecond, enable, disable) {
    let count = 0;
    let retrySeconds = 5;
    let disabledTimeout = 0;
    setInterval(()=>{
        if (disabledTimeout === 0) {
            if (count > maxPerSecond) {
                retrySeconds *= 2;
                disable(retrySeconds);
                // Cap at one day
                if (retrySeconds > 86400) {
                    retrySeconds = 86400;
                }
                disabledTimeout = retrySeconds;
            }
        } else {
            disabledTimeout -= 1;
            if (disabledTimeout === 0) {
                enable();
            }
        }
        count = 0;
    }, 1000).unref();
    return ()=>{
        count += 1;
    };
}
// Add types for the exception event data
/** Could this be an anonymous function? */ function isAnonymous(name) {
    return name !== undefined && (name.length === 0 || name === '?' || name === '<anonymous>');
}
/** Do the function names appear to match? */ function functionNamesMatch(a, b) {
    return a === b || isAnonymous(a) && isAnonymous(b);
}
exports.LOCAL_VARIABLES_KEY = LOCAL_VARIABLES_KEY;
exports.createRateLimiter = createRateLimiter;
exports.functionNamesMatch = functionNamesMatch;
exports.isAnonymous = isAnonymous; //# sourceMappingURL=common.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/local-variables/local-variables-async.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const node_worker_threads = __turbopack_context__.r("[externals]/node:worker_threads [external] (node:worker_threads, cjs)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const debug = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/debug.js [instrumentation] (ecmascript)");
const common = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/local-variables/common.js [instrumentation] (ecmascript)");
// This string is a placeholder that gets overwritten with the worker code.
const base64WorkerScript = '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';
function log(...args) {
    core.logger.log('[LocalVariables]', ...args);
}
/**
 * Adds local variables to exception frames
 */ const localVariablesAsyncIntegration = core.defineIntegration((integrationOptions = {})=>{
    function addLocalVariablesToException(exception, localVariables) {
        // Filter out frames where the function name is `new Promise` since these are in the error.stack frames
        // but do not appear in the debugger call frames
        const frames = (_optionalChain([
            exception,
            'access',
            (_)=>_.stacktrace,
            'optionalAccess',
            (_2)=>_2.frames
        ]) || []).filter((frame)=>frame.function !== 'new Promise');
        for(let i = 0; i < frames.length; i++){
            // Sentry frames are in reverse order
            const frameIndex = frames.length - i - 1;
            const frameLocalVariables = localVariables[i];
            const frame = frames[frameIndex];
            if (!frame || !frameLocalVariables) {
                break;
            }
            if (// We need to have vars to add
            frameLocalVariables.vars === undefined || // We're not interested in frames that are not in_app because the vars are not relevant
            frame.in_app === false || // The function names need to match
            !common.functionNamesMatch(frame.function, frameLocalVariables.function)) {
                continue;
            }
            frame.vars = frameLocalVariables.vars;
        }
    }
    function addLocalVariablesToEvent(event, hint) {
        if (hint.originalException && typeof hint.originalException === 'object' && common.LOCAL_VARIABLES_KEY in hint.originalException && Array.isArray(hint.originalException[common.LOCAL_VARIABLES_KEY])) {
            for (const exception of _optionalChain([
                event,
                'access',
                (_3)=>_3.exception,
                'optionalAccess',
                (_4)=>_4.values
            ]) || []){
                addLocalVariablesToException(exception, hint.originalException[common.LOCAL_VARIABLES_KEY]);
            }
            hint.originalException[common.LOCAL_VARIABLES_KEY] = undefined;
        }
        return event;
    }
    async function startInspector() {
        // We load inspector dynamically because on some platforms Node is built without inspector support
        const inspector = await __turbopack_context__.r("[externals]/node:inspector [external] (node:inspector, cjs, async loader)")(__turbopack_context__.i);
        if (!inspector.url()) {
            inspector.open(0);
        }
    }
    function startWorker(options) {
        const worker = new node_worker_threads.Worker(new URL(`data:application/javascript;base64,${base64WorkerScript}`), {
            workerData: options,
            // We don't want any Node args to be passed to the worker
            execArgv: []
        });
        process.on('exit', ()=>{
            // eslint-disable-next-line @typescript-eslint/no-floating-promises
            worker.terminate();
        });
        worker.once('error', (err)=>{
            log('Worker error', err);
        });
        worker.once('exit', (code)=>{
            log('Worker exit', code);
        });
        // Ensure this thread can't block app exit
        worker.unref();
    }
    return {
        name: 'LocalVariablesAsync',
        async setup (client) {
            const clientOptions = client.getOptions();
            if (!clientOptions.includeLocalVariables) {
                return;
            }
            if (await debug.isDebuggerEnabled()) {
                core.logger.warn('Local variables capture has been disabled because the debugger was already enabled');
                return;
            }
            const options = {
                ...integrationOptions,
                debug: core.logger.isEnabled()
            };
            startInspector().then(()=>{
                try {
                    startWorker(options);
                } catch (e) {
                    core.logger.error('Failed to start worker', e);
                }
            }, (e)=>{
                core.logger.error('Failed to start inspector', e);
            });
        },
        processEvent (event, hint) {
            return addLocalVariablesToEvent(event, hint);
        }
    };
});
exports.base64WorkerScript = base64WorkerScript;
exports.localVariablesAsyncIntegration = localVariablesAsyncIntegration; //# sourceMappingURL=local-variables-async.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/local-variables/local-variables-sync.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const nodeVersion = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/nodeVersion.js [instrumentation] (ecmascript)");
const debug = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/debug.js [instrumentation] (ecmascript)");
const common = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/local-variables/common.js [instrumentation] (ecmascript)");
/** Creates a unique hash from stack frames */ function hashFrames(frames) {
    if (frames === undefined) {
        return;
    }
    // Only hash the 10 most recent frames (ie. the last 10)
    return frames.slice(-10).reduce((acc, frame)=>`${acc},${frame.function},${frame.lineno},${frame.colno}`, '');
}
/**
 * We use the stack parser to create a unique hash from the exception stack trace
 * This is used to lookup vars when the exception passes through the event processor
 */ function hashFromStack(stackParser, stack) {
    if (stack === undefined) {
        return undefined;
    }
    return hashFrames(stackParser(stack, 1));
}
/** Creates a container for callbacks to be called sequentially */ function createCallbackList(complete) {
    // A collection of callbacks to be executed last to first
    let callbacks = [];
    let completedCalled = false;
    function checkedComplete(result) {
        callbacks = [];
        if (completedCalled) {
            return;
        }
        completedCalled = true;
        complete(result);
    }
    // complete should be called last
    callbacks.push(checkedComplete);
    function add(fn) {
        callbacks.push(fn);
    }
    function next(result) {
        const popped = callbacks.pop() || checkedComplete;
        try {
            popped(result);
        } catch (_) {
            // If there is an error, we still want to call the complete callback
            checkedComplete(result);
        }
    }
    return {
        add,
        next
    };
}
/**
 * Promise API is available as `Experimental` and in Node 19 only.
 *
 * Callback-based API is `Stable` since v14 and `Experimental` since v8.
 * Because of that, we are creating our own `AsyncSession` class.
 *
 * https://nodejs.org/docs/latest-v19.x/api/inspector.html#promises-api
 * https://nodejs.org/docs/latest-v14.x/api/inspector.html
 */ class AsyncSession {
    /** Throws if inspector API is not available */ constructor(_session){
        this._session = _session;
    //
    }
    static async create(orDefault) {
        if (orDefault) {
            return orDefault;
        }
        const inspector = await __turbopack_context__.r("[externals]/node:inspector [external] (node:inspector, cjs, async loader)")(__turbopack_context__.i);
        return new AsyncSession(new inspector.Session());
    }
    /** @inheritdoc */ configureAndConnect(onPause, captureAll) {
        this._session.connect();
        this._session.on('Debugger.paused', (event)=>{
            onPause(event, ()=>{
                // After the pause work is complete, resume execution or the exception context memory is leaked
                this._session.post('Debugger.resume');
            });
        });
        this._session.post('Debugger.enable');
        this._session.post('Debugger.setPauseOnExceptions', {
            state: captureAll ? 'all' : 'uncaught'
        });
    }
    setPauseOnExceptions(captureAll) {
        this._session.post('Debugger.setPauseOnExceptions', {
            state: captureAll ? 'all' : 'uncaught'
        });
    }
    /** @inheritdoc */ getLocalVariables(objectId, complete) {
        this._getProperties(objectId, (props)=>{
            const { add, next } = createCallbackList(complete);
            for (const prop of props){
                if (_optionalChain([
                    prop,
                    'optionalAccess',
                    (_2)=>_2.value,
                    'optionalAccess',
                    (_3)=>_3.objectId
                ]) && _optionalChain([
                    prop,
                    'optionalAccess',
                    (_4)=>_4.value,
                    'access',
                    (_5)=>_5.className
                ]) === 'Array') {
                    const id = prop.value.objectId;
                    add((vars)=>this._unrollArray(id, prop.name, vars, next));
                } else if (_optionalChain([
                    prop,
                    'optionalAccess',
                    (_6)=>_6.value,
                    'optionalAccess',
                    (_7)=>_7.objectId
                ]) && _optionalChain([
                    prop,
                    'optionalAccess',
                    (_8)=>_8.value,
                    'optionalAccess',
                    (_9)=>_9.className
                ]) === 'Object') {
                    const id = prop.value.objectId;
                    add((vars)=>this._unrollObject(id, prop.name, vars, next));
                } else if (_optionalChain([
                    prop,
                    'optionalAccess',
                    (_10)=>_10.value
                ])) {
                    add((vars)=>this._unrollOther(prop, vars, next));
                }
            }
            next({});
        });
    }
    /**
   * Gets all the PropertyDescriptors of an object
   */ _getProperties(objectId, next) {
        this._session.post('Runtime.getProperties', {
            objectId,
            ownProperties: true
        }, (err, params)=>{
            if (err) {
                next([]);
            } else {
                next(params.result);
            }
        });
    }
    /**
   * Unrolls an array property
   */ _unrollArray(objectId, name, vars, next) {
        this._getProperties(objectId, (props)=>{
            vars[name] = props.filter((v)=>v.name !== 'length' && !isNaN(parseInt(v.name, 10))).sort((a, b)=>parseInt(a.name, 10) - parseInt(b.name, 10)).map((v)=>_optionalChain([
                    v,
                    'optionalAccess',
                    (_11)=>_11.value,
                    'optionalAccess',
                    (_12)=>_12.value
                ]));
            next(vars);
        });
    }
    /**
   * Unrolls an object property
   */ _unrollObject(objectId, name, vars, next) {
        this._getProperties(objectId, (props)=>{
            vars[name] = props.map((v)=>[
                    v.name,
                    _optionalChain([
                        v,
                        'optionalAccess',
                        (_13)=>_13.value,
                        'optionalAccess',
                        (_14)=>_14.value
                    ])
                ]).reduce((obj, [key, val])=>{
                obj[key] = val;
                return obj;
            }, {});
            next(vars);
        });
    }
    /**
   * Unrolls other properties
   */ _unrollOther(prop, vars, next) {
        if (prop.value) {
            if ('value' in prop.value) {
                if (prop.value.value === undefined || prop.value.value === null) {
                    vars[prop.name] = `<${prop.value.value}>`;
                } else {
                    vars[prop.name] = prop.value.value;
                }
            } else if ('description' in prop.value && prop.value.type !== 'function') {
                vars[prop.name] = `<${prop.value.description}>`;
            } else if (prop.value.type === 'undefined') {
                vars[prop.name] = '<undefined>';
            }
        }
        next(vars);
    }
}
const INTEGRATION_NAME = 'LocalVariables';
/**
 * Adds local variables to exception frames
 */ const _localVariablesSyncIntegration = (options = {}, sessionOverride)=>{
    const cachedFrames = new core.LRUMap(20);
    let rateLimiter;
    let shouldProcessEvent = false;
    function addLocalVariablesToException(exception) {
        const hash = hashFrames(_optionalChain([
            exception,
            'optionalAccess',
            (_15)=>_15.stacktrace,
            'optionalAccess',
            (_16)=>_16.frames
        ]));
        if (hash === undefined) {
            return;
        }
        // Check if we have local variables for an exception that matches the hash
        // remove is identical to get but also removes the entry from the cache
        const cachedFrame = cachedFrames.remove(hash);
        if (cachedFrame === undefined) {
            return;
        }
        // Filter out frames where the function name is `new Promise` since these are in the error.stack frames
        // but do not appear in the debugger call frames
        const frames = (_optionalChain([
            exception,
            'access',
            (_17)=>_17.stacktrace,
            'optionalAccess',
            (_18)=>_18.frames
        ]) || []).filter((frame)=>frame.function !== 'new Promise');
        for(let i = 0; i < frames.length; i++){
            // Sentry frames are in reverse order
            const frameIndex = frames.length - i - 1;
            const cachedFrameVariable = cachedFrame[i];
            const frameVariable = frames[frameIndex];
            // Drop out if we run out of frames to match up
            if (!frameVariable || !cachedFrameVariable) {
                break;
            }
            if (// We need to have vars to add
            cachedFrameVariable.vars === undefined || // We're not interested in frames that are not in_app because the vars are not relevant
            frameVariable.in_app === false || // The function names need to match
            !common.functionNamesMatch(frameVariable.function, cachedFrameVariable.function)) {
                continue;
            }
            frameVariable.vars = cachedFrameVariable.vars;
        }
    }
    function addLocalVariablesToEvent(event) {
        for (const exception of _optionalChain([
            event,
            'optionalAccess',
            (_19)=>_19.exception,
            'optionalAccess',
            (_20)=>_20.values
        ]) || []){
            addLocalVariablesToException(exception);
        }
        return event;
    }
    return {
        name: INTEGRATION_NAME,
        async setupOnce () {
            const client = core.getClient();
            const clientOptions = _optionalChain([
                client,
                'optionalAccess',
                (_21)=>_21.getOptions,
                'call',
                (_22)=>_22()
            ]);
            if (!_optionalChain([
                clientOptions,
                'optionalAccess',
                (_23)=>_23.includeLocalVariables
            ])) {
                return;
            }
            // Only setup this integration if the Node version is >= v18
            // https://github.com/getsentry/sentry-javascript/issues/7697
            const unsupportedNodeVersion = nodeVersion.NODE_MAJOR < 18;
            if (unsupportedNodeVersion) {
                core.logger.log('The `LocalVariables` integration is only supported on Node >= v18.');
                return;
            }
            if (await debug.isDebuggerEnabled()) {
                core.logger.warn('Local variables capture has been disabled because the debugger was already enabled');
                return;
            }
            AsyncSession.create(sessionOverride).then((session)=>{
                function handlePaused(stackParser, { params: { reason, data, callFrames } }, complete) {
                    if (reason !== 'exception' && reason !== 'promiseRejection') {
                        complete();
                        return;
                    }
                    _optionalChain([
                        rateLimiter,
                        'optionalCall',
                        (_24)=>_24()
                    ]);
                    // data.description contains the original error.stack
                    const exceptionHash = hashFromStack(stackParser, _optionalChain([
                        data,
                        'optionalAccess',
                        (_25)=>_25.description
                    ]));
                    if (exceptionHash == undefined) {
                        complete();
                        return;
                    }
                    const { add, next } = createCallbackList((frames)=>{
                        cachedFrames.set(exceptionHash, frames);
                        complete();
                    });
                    // Because we're queuing up and making all these calls synchronously, we can potentially overflow the stack
                    // For this reason we only attempt to get local variables for the first 5 frames
                    for(let i = 0; i < Math.min(callFrames.length, 5); i++){
                        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                        const { scopeChain, functionName, this: obj } = callFrames[i];
                        const localScope = scopeChain.find((scope)=>scope.type === 'local');
                        // obj.className is undefined in ESM modules
                        const fn = obj.className === 'global' || !obj.className ? functionName : `${obj.className}.${functionName}`;
                        if (_optionalChain([
                            localScope,
                            'optionalAccess',
                            (_26)=>_26.object,
                            'access',
                            (_27)=>_27.objectId
                        ]) === undefined) {
                            add((frames)=>{
                                frames[i] = {
                                    function: fn
                                };
                                next(frames);
                            });
                        } else {
                            const id = localScope.object.objectId;
                            add((frames)=>_optionalChain([
                                    session,
                                    'optionalAccess',
                                    (_28)=>_28.getLocalVariables,
                                    'call',
                                    (_29)=>_29(id, (vars)=>{
                                            frames[i] = {
                                                function: fn,
                                                vars
                                            };
                                            next(frames);
                                        })
                                ]));
                        }
                    }
                    next([]);
                }
                const captureAll = options.captureAllExceptions !== false;
                session.configureAndConnect((ev, complete)=>handlePaused(clientOptions.stackParser, ev, complete), captureAll);
                if (captureAll) {
                    const max = options.maxExceptionsPerSecond || 50;
                    rateLimiter = common.createRateLimiter(max, ()=>{
                        core.logger.log('Local variables rate-limit lifted.');
                        _optionalChain([
                            session,
                            'optionalAccess',
                            (_30)=>_30.setPauseOnExceptions,
                            'call',
                            (_31)=>_31(true)
                        ]);
                    }, (seconds)=>{
                        core.logger.log(`Local variables rate-limit exceeded. Disabling capturing of caught exceptions for ${seconds} seconds.`);
                        _optionalChain([
                            session,
                            'optionalAccess',
                            (_32)=>_32.setPauseOnExceptions,
                            'call',
                            (_33)=>_33(false)
                        ]);
                    });
                }
                shouldProcessEvent = true;
            }, (error)=>{
                core.logger.log('The `LocalVariables` integration failed to start.', error);
            });
        },
        processEvent (event) {
            if (shouldProcessEvent) {
                return addLocalVariablesToEvent(event);
            }
            return event;
        },
        // These are entirely for testing
        _getCachedFramesCount () {
            return cachedFrames.size;
        },
        _getFirstCachedFrame () {
            return cachedFrames.values()[0];
        }
    };
};
/**
 * Adds local variables to exception frames.
 */ const localVariablesSyncIntegration = core.defineIntegration(_localVariablesSyncIntegration);
exports.createCallbackList = createCallbackList;
exports.hashFrames = hashFrames;
exports.hashFromStack = hashFromStack;
exports.localVariablesSyncIntegration = localVariablesSyncIntegration; //# sourceMappingURL=local-variables-sync.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/local-variables/index.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const nodeVersion = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/nodeVersion.js [instrumentation] (ecmascript)");
const localVariablesAsync = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/local-variables/local-variables-async.js [instrumentation] (ecmascript)");
const localVariablesSync = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/local-variables/local-variables-sync.js [instrumentation] (ecmascript)");
const localVariablesIntegration = (options = {})=>{
    return nodeVersion.NODE_VERSION.major < 19 ? localVariablesSync.localVariablesSyncIntegration(options) : localVariablesAsync.localVariablesAsyncIntegration(options);
};
exports.localVariablesIntegration = localVariablesIntegration; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/commonjs.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
/** Detect CommonJS. */ function isCjs() {
    return ("TURBOPACK compile-time value", "function") !== 'undefined';
}
exports.isCjs = isCjs; //# sourceMappingURL=commonjs.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/modules.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const node_fs = __turbopack_context__.r("[externals]/node:fs [external] (node:fs, cjs)");
const node_path = __turbopack_context__.r("[externals]/node:path [external] (node:path, cjs)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const debugBuild = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/debug-build.js [instrumentation] (ecmascript)");
const commonjs = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/commonjs.js [instrumentation] (ecmascript)");
let moduleCache;
const INTEGRATION_NAME = 'Modules';
const _modulesIntegration = ()=>{
    // This integration only works in CJS contexts
    if (!commonjs.isCjs()) {
        debugBuild.DEBUG_BUILD && core.logger.warn('modulesIntegration only works in CommonJS (CJS) environments. Remove this integration if you are using ESM.');
        return {
            name: INTEGRATION_NAME
        };
    }
    return {
        name: INTEGRATION_NAME,
        processEvent (event) {
            event.modules = {
                ...event.modules,
                ..._getModules()
            };
            return event;
        }
    };
};
/**
 * Add node modules / packages to the event.
 *
 * Only works in CommonJS (CJS) environments.
 */ const modulesIntegration = core.defineIntegration(_modulesIntegration);
/** Extract information about paths */ function getPaths() {
    try {
        return ("TURBOPACK compile-time truthy", 1) ? Object.keys(__turbopack_context__.c) : "TURBOPACK unreachable";
    } catch (e) {
        return [];
    }
}
/** Extract information about package.json modules */ function collectModules() {
    const mainPaths = ("TURBOPACK member replacement", __turbopack_context__.t).main && ("TURBOPACK member replacement", __turbopack_context__.t).main.paths || [];
    const paths = getPaths();
    const infos = {};
    const seen = {};
    paths.forEach((path)=>{
        let dir = path;
        /** Traverse directories upward in the search of package.json file */ const updir = ()=>{
            const orig = dir;
            dir = node_path.dirname(orig);
            if (!dir || orig === dir || seen[orig]) {
                return undefined;
            }
            if (mainPaths.indexOf(dir) < 0) {
                return updir();
            }
            const pkgfile = node_path.join(orig, 'package.json');
            seen[orig] = true;
            if (!node_fs.existsSync(pkgfile)) {
                return updir();
            }
            try {
                const info = JSON.parse(node_fs.readFileSync(pkgfile, 'utf8'));
                infos[info.name] = info.version;
            } catch (_oO) {
            // no-empty
            }
        };
        updir();
    });
    return infos;
}
/** Fetches the list of modules and the versions loaded by the entry file for your node.js app. */ function _getModules() {
    if (!moduleCache) {
        moduleCache = collectModules();
    }
    return moduleCache;
}
exports.modulesIntegration = modulesIntegration; //# sourceMappingURL=modules.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/errorhandling.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const debugBuild = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/debug-build.js [instrumentation] (ecmascript)");
const DEFAULT_SHUTDOWN_TIMEOUT = 2000;
/**
 * @hidden
 */ function logAndExitProcess(error) {
    core.consoleSandbox(()=>{
        // eslint-disable-next-line no-console
        console.error(error);
    });
    const client = core.getClient();
    if (client === undefined) {
        debugBuild.DEBUG_BUILD && core.logger.warn('No NodeClient was defined, we are exiting the process now.');
        ("TURBOPACK ident replacement", globalThis).process.exit(1);
        return;
    }
    const options = client.getOptions();
    const timeout = options && options.shutdownTimeout && options.shutdownTimeout > 0 && options.shutdownTimeout || DEFAULT_SHUTDOWN_TIMEOUT;
    client.close(timeout).then((result)=>{
        if (!result) {
            debugBuild.DEBUG_BUILD && core.logger.warn('We reached the timeout for emptying the request buffer, still exiting now!');
        }
        ("TURBOPACK ident replacement", globalThis).process.exit(1);
    }, (error)=>{
        debugBuild.DEBUG_BUILD && core.logger.error(error);
    });
}
exports.logAndExitProcess = logAndExitProcess; //# sourceMappingURL=errorhandling.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/onuncaughtexception.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const debugBuild = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/debug-build.js [instrumentation] (ecmascript)");
const errorhandling = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/errorhandling.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'OnUncaughtException';
/**
 * Add a global exception handler.
 */ const onUncaughtExceptionIntegration = core.defineIntegration((options = {})=>{
    const optionsWithDefaults = {
        exitEvenIfOtherHandlersAreRegistered: false,
        ...options
    };
    return {
        name: INTEGRATION_NAME,
        setup (client) {
            ("TURBOPACK ident replacement", globalThis).process.on('uncaughtException', makeErrorHandler(client, optionsWithDefaults));
        }
    };
});
/** Exported only for tests */ function makeErrorHandler(client, options) {
    const timeout = 2000;
    let caughtFirstError = false;
    let caughtSecondError = false;
    let calledFatalError = false;
    let firstError;
    const clientOptions = client.getOptions();
    return Object.assign((error)=>{
        let onFatalError = errorhandling.logAndExitProcess;
        if (options.onFatalError) {
            onFatalError = options.onFatalError;
        } else if (clientOptions.onFatalError) {
            onFatalError = clientOptions.onFatalError;
        }
        // Attaching a listener to `uncaughtException` will prevent the node process from exiting. We generally do not
        // want to alter this behaviour so we check for other listeners that users may have attached themselves and adjust
        // exit behaviour of the SDK accordingly:
        // - If other listeners are attached, do not exit.
        // - If the only listener attached is ours, exit.
        const userProvidedListenersCount = ("TURBOPACK ident replacement", globalThis).process.listeners('uncaughtException').filter((listener)=>{
            // There are 3 listeners we ignore:
            return(// as soon as we're using domains this listener is attached by node itself
            listener.name !== 'domainUncaughtExceptionClear' && // the handler we register for tracing
            listener.tag !== 'sentry_tracingErrorCallback' && // the handler we register in this integration
            listener._errorHandler !== true);
        }).length;
        const processWouldExit = userProvidedListenersCount === 0;
        const shouldApplyFatalHandlingLogic = options.exitEvenIfOtherHandlersAreRegistered || processWouldExit;
        if (!caughtFirstError) {
            // this is the first uncaught error and the ultimate reason for shutting down
            // we want to do absolutely everything possible to ensure it gets captured
            // also we want to make sure we don't go recursion crazy if more errors happen after this one
            firstError = error;
            caughtFirstError = true;
            if (core.getClient() === client) {
                core.captureException(error, {
                    originalException: error,
                    captureContext: {
                        level: 'fatal'
                    },
                    mechanism: {
                        handled: false,
                        type: 'onuncaughtexception'
                    }
                });
            }
            if (!calledFatalError && shouldApplyFatalHandlingLogic) {
                calledFatalError = true;
                onFatalError(error);
            }
        } else {
            if (shouldApplyFatalHandlingLogic) {
                if (calledFatalError) {
                    // we hit an error *after* calling onFatalError - pretty boned at this point, just shut it down
                    debugBuild.DEBUG_BUILD && core.logger.warn('uncaught exception after calling fatal error shutdown callback - this is bad! forcing shutdown');
                    errorhandling.logAndExitProcess(error);
                } else if (!caughtSecondError) {
                    // two cases for how we can hit this branch:
                    //   - capturing of first error blew up and we just caught the exception from that
                    //     - quit trying to capture, proceed with shutdown
                    //   - a second independent error happened while waiting for first error to capture
                    //     - want to avoid causing premature shutdown before first error capture finishes
                    // it's hard to immediately tell case 1 from case 2 without doing some fancy/questionable domain stuff
                    // so let's instead just delay a bit before we proceed with our action here
                    // in case 1, we just wait a bit unnecessarily but ultimately do the same thing
                    // in case 2, the delay hopefully made us wait long enough for the capture to finish
                    // two potential nonideal outcomes:
                    //   nonideal case 1: capturing fails fast, we sit around for a few seconds unnecessarily before proceeding correctly by calling onFatalError
                    //   nonideal case 2: case 2 happens, 1st error is captured but slowly, timeout completes before capture and we treat second error as the sendErr of (nonexistent) failure from trying to capture first error
                    // note that after hitting this branch, we might catch more errors where (caughtSecondError && !calledFatalError)
                    //   we ignore them - they don't matter to us, we're just waiting for the second error timeout to finish
                    caughtSecondError = true;
                    setTimeout(()=>{
                        if (!calledFatalError) {
                            // it was probably case 1, let's treat err as the sendErr and call onFatalError
                            calledFatalError = true;
                            onFatalError(firstError, error);
                        }
                    }, timeout); // capturing could take at least sendTimeout to fail, plus an arbitrary second for how long it takes to collect surrounding source etc
                }
            }
        }
    }, {
        _errorHandler: true
    });
}
exports.makeErrorHandler = makeErrorHandler;
exports.onUncaughtExceptionIntegration = onUncaughtExceptionIntegration; //# sourceMappingURL=onuncaughtexception.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/onunhandledrejection.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const errorhandling = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/errorhandling.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'OnUnhandledRejection';
const _onUnhandledRejectionIntegration = (options = {})=>{
    const mode = options.mode || 'warn';
    return {
        name: INTEGRATION_NAME,
        setup (client) {
            ("TURBOPACK ident replacement", globalThis).process.on('unhandledRejection', makeUnhandledPromiseHandler(client, {
                mode
            }));
        }
    };
};
/**
 * Add a global promise rejection handler.
 */ const onUnhandledRejectionIntegration = core.defineIntegration(_onUnhandledRejectionIntegration);
/**
 * Send an exception with reason
 * @param reason string
 * @param promise promise
 *
 * Exported only for tests.
 */ function makeUnhandledPromiseHandler(client, options) {
    return function sendUnhandledPromise(reason, promise) {
        if (core.getClient() !== client) {
            return;
        }
        core.captureException(reason, {
            originalException: promise,
            captureContext: {
                extra: {
                    unhandledPromiseRejection: true
                }
            },
            mechanism: {
                handled: false,
                type: 'onunhandledrejection'
            }
        });
        handleRejection(reason, options);
    };
}
/**
 * Handler for `mode` option
 */ function handleRejection(reason, options) {
    // https://github.com/nodejs/node/blob/7cf6f9e964aa00772965391c23acda6d71972a9a/lib/internal/process/promises.js#L234-L240
    const rejectionWarning = 'This error originated either by ' + 'throwing inside of an async function without a catch block, ' + 'or by rejecting a promise which was not handled with .catch().' + ' The promise rejected with the reason:';
    /* eslint-disable no-console */ if (options.mode === 'warn') {
        core.consoleSandbox(()=>{
            console.warn(rejectionWarning);
            console.error(reason && typeof reason === 'object' && 'stack' in reason ? reason.stack : reason);
        });
    } else if (options.mode === 'strict') {
        core.consoleSandbox(()=>{
            console.warn(rejectionWarning);
        });
        errorhandling.logAndExitProcess(reason);
    }
/* eslint-enable no-console */ }
exports.makeUnhandledPromiseHandler = makeUnhandledPromiseHandler;
exports.onUnhandledRejectionIntegration = onUnhandledRejectionIntegration; //# sourceMappingURL=onunhandledrejection.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/anr/index.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain, _optionalChainDelete } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const util = __turbopack_context__.r("[externals]/node:util [external] (node:util, cjs)");
const node_worker_threads = __turbopack_context__.r("[externals]/node:worker_threads [external] (node:worker_threads, cjs)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const nodeVersion = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/nodeVersion.js [instrumentation] (ecmascript)");
const debug = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/debug.js [instrumentation] (ecmascript)");
const { isPromise } = util.types;
// This string is a placeholder that gets overwritten with the worker code.
const base64WorkerScript = '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';
const DEFAULT_INTERVAL = 50;
const DEFAULT_HANG_THRESHOLD = 5000;
function log(message, ...args) {
    core.logger.log(`[ANR] ${message}`, ...args);
}
function globalWithScopeFetchFn() {
    return core.GLOBAL_OBJ;
}
/** Fetches merged scope data */ function getScopeData() {
    const scope = core.getGlobalScope().getScopeData();
    core.mergeScopeData(scope, core.getIsolationScope().getScopeData());
    core.mergeScopeData(scope, core.getCurrentScope().getScopeData());
    // We remove attachments because they likely won't serialize well as json
    scope.attachments = [];
    // We can't serialize event processor functions
    scope.eventProcessors = [];
    return scope;
}
/**
 * Gets contexts by calling all event processors. This shouldn't be called until all integrations are setup
 */ async function getContexts(client) {
    let event = {
        message: 'ANR'
    };
    const eventHint = {};
    for (const processor of client.getEventProcessors()){
        if (event === null) break;
        event = await processor(event, eventHint);
    }
    return _optionalChain([
        event,
        'optionalAccess',
        (_2)=>_2.contexts
    ]) || {};
}
const INTEGRATION_NAME = 'Anr';
const _anrIntegration = (options = {})=>{
    if (nodeVersion.NODE_VERSION.major < 16 || nodeVersion.NODE_VERSION.major === 16 && nodeVersion.NODE_VERSION.minor < 17) {
        throw new Error('ANR detection requires Node 16.17.0 or later');
    }
    let worker;
    let client;
    // Hookup the scope fetch function to the global object so that it can be called from the worker thread via the
    // debugger when it pauses
    const gbl = globalWithScopeFetchFn();
    gbl.__SENTRY_GET_SCOPES__ = getScopeData;
    return {
        name: INTEGRATION_NAME,
        startWorker: ()=>{
            if (worker) {
                return;
            }
            if (client) {
                worker = _startWorker(client, options);
            }
        },
        stopWorker: ()=>{
            if (worker) {
                // eslint-disable-next-line @typescript-eslint/no-floating-promises
                worker.then((stop)=>{
                    stop();
                    worker = undefined;
                });
            }
        },
        async setup (initClient) {
            client = initClient;
            if (options.captureStackTrace && await debug.isDebuggerEnabled()) {
                core.logger.warn('ANR captureStackTrace has been disabled because the debugger was already enabled');
                options.captureStackTrace = false;
            }
            // setImmediate is used to ensure that all other integrations have had their setup called first.
            // This allows us to call into all integrations to fetch the full context
            setImmediate(()=>this.startWorker());
        }
    };
};
const anrIntegration = core.defineIntegration(_anrIntegration);
/**
 * Starts the ANR worker thread
 *
 * @returns A function to stop the worker
 */ async function _startWorker(client, integrationOptions) {
    const dsn = client.getDsn();
    if (!dsn) {
        return ()=>{
        //
        };
    }
    const contexts = await getContexts(client);
    // These will not be accurate if sent later from the worker thread
    _optionalChainDelete([
        contexts,
        'access',
        (_3)=>_3.app,
        'optionalAccess',
        (_4)=>delete _4.app_memory
    ]);
    _optionalChainDelete([
        contexts,
        'access',
        (_5)=>_5.device,
        'optionalAccess',
        (_6)=>delete _6.free_memory
    ]);
    const initOptions = client.getOptions();
    const sdkMetadata = client.getSdkMetadata() || {};
    if (sdkMetadata.sdk) {
        sdkMetadata.sdk.integrations = initOptions.integrations.map((i)=>i.name);
    }
    const options = {
        debug: core.logger.isEnabled(),
        dsn,
        tunnel: initOptions.tunnel,
        environment: initOptions.environment || 'production',
        release: initOptions.release,
        dist: initOptions.dist,
        sdkMetadata,
        appRootPath: integrationOptions.appRootPath,
        pollInterval: integrationOptions.pollInterval || DEFAULT_INTERVAL,
        anrThreshold: integrationOptions.anrThreshold || DEFAULT_HANG_THRESHOLD,
        captureStackTrace: !!integrationOptions.captureStackTrace,
        maxAnrEvents: integrationOptions.maxAnrEvents || 1,
        staticTags: integrationOptions.staticTags || {},
        contexts
    };
    if (options.captureStackTrace) {
        const inspector = await __turbopack_context__.r("[externals]/node:inspector [external] (node:inspector, cjs, async loader)")(__turbopack_context__.i);
        if (!inspector.url()) {
            inspector.open(0);
        }
    }
    const worker = new node_worker_threads.Worker(new URL(`data:application/javascript;base64,${base64WorkerScript}`), {
        workerData: options,
        // We don't want any Node args to be passed to the worker
        execArgv: []
    });
    process.on('exit', ()=>{
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        worker.terminate();
    });
    const timer = setInterval(()=>{
        try {
            const currentSession = core.getCurrentScope().getSession();
            // We need to copy the session object and remove the toJSON method so it can be sent to the worker
            // serialized without making it a SerializedSession
            const session = currentSession ? {
                ...currentSession,
                toJSON: undefined
            } : undefined;
            // message the worker to tell it the main event loop is still running
            worker.postMessage({
                session,
                debugImages: core.getFilenameToDebugIdMap(initOptions.stackParser)
            });
        } catch (_) {
        //
        }
    }, options.pollInterval);
    // Timer should not block exit
    timer.unref();
    worker.on('message', (msg)=>{
        if (msg === 'session-ended') {
            log('ANR event sent from ANR worker. Clearing session in this thread.');
            core.getCurrentScope().setSession(undefined);
        }
    });
    worker.once('error', (err)=>{
        clearInterval(timer);
        log('ANR worker error', err);
    });
    worker.once('exit', (code)=>{
        clearInterval(timer);
        log('ANR worker exit', code);
    });
    // Ensure this thread can't block app exit
    worker.unref();
    return ()=>{
        // eslint-disable-next-line @typescript-eslint/no-floating-promises
        worker.terminate();
        clearInterval(timer);
    };
}
/**
 * Disables ANR detection for the duration of the callback
 */ function disableAnrDetectionForCallback(callback) {
    const integration = _optionalChain([
        core.getClient,
        'call',
        (_7)=>_7(),
        'optionalAccess',
        (_8)=>_8.getIntegrationByName,
        'call',
        (_9)=>_9(INTEGRATION_NAME)
    ]);
    if (!integration) {
        return callback();
    }
    integration.stopWorker();
    const result = callback();
    if (isPromise(result)) {
        return result.finally(()=>integration.startWorker());
    }
    integration.startWorker();
    return result;
}
exports.anrIntegration = anrIntegration;
exports.base64WorkerScript = base64WorkerScript;
exports.disableAnrDetectionForCallback = disableAnrDetectionForCallback; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/createMissingInstrumentationContext.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const commonjs = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/commonjs.js [instrumentation] (ecmascript)");
const createMissingInstrumentationContext = (pkg)=>({
        package: pkg,
        'javascript.is_cjs': commonjs.isCjs()
    });
exports.createMissingInstrumentationContext = createMissingInstrumentationContext; //# sourceMappingURL=createMissingInstrumentationContext.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/ensureIsWrapped.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const core$1 = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const commonjs = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/commonjs.js [instrumentation] (ecmascript)");
const createMissingInstrumentationContext = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/createMissingInstrumentationContext.js [instrumentation] (ecmascript)");
/**
 * Checks and warns if a framework isn't wrapped by opentelemetry.
 */ function ensureIsWrapped(maybeWrappedFunction, name) {
    const client = core.getClient();
    if (!_optionalChain([
        client,
        'optionalAccess',
        (_)=>_.getOptions,
        'call',
        (_2)=>_2(),
        'access',
        (_3)=>_3.disableInstrumentationWarnings
    ]) && !core$1.isWrapped(maybeWrappedFunction) && core.isEnabled() && core.hasTracingEnabled()) {
        core.consoleSandbox(()=>{
            if (commonjs.isCjs()) {
                // eslint-disable-next-line no-console
                console.warn(`[Sentry] ${name} is not instrumented. This is likely because you required/imported ${name} before calling \`Sentry.init()\`.`);
            } else {
                // eslint-disable-next-line no-console
                console.warn(`[Sentry] ${name} is not instrumented. Please make sure to initialize Sentry in a separate file that you \`--import\` when running node, see: https://docs.sentry.io/platforms/javascript/guides/${name}/install/esm/.`);
            }
        });
        core.getGlobalScope().setContext('missing_instrumentation', createMissingInstrumentationContext.createMissingInstrumentationContext(name));
    }
}
exports.ensureIsWrapped = ensureIsWrapped; //# sourceMappingURL=ensureIsWrapped.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/express.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationExpress = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-express@0.46.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-express/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const debugBuild = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/debug-build.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const addOriginToSpan = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/addOriginToSpan.js [instrumentation] (ecmascript)");
const ensureIsWrapped = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/ensureIsWrapped.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Express';
const instrumentExpress = instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>new instrumentationExpress.ExpressInstrumentation({
        requestHook (span) {
            addOriginToSpan.addOriginToSpan(span, 'auto.http.otel.express');
            const attributes = core.spanToJSON(span).data || {};
            // this is one of: middleware, request_handler, router
            const type = attributes['express.type'];
            if (type) {
                span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_OP, `${type}.express`);
            }
            // Also update the name, we don't need to "middleware - " prefix
            const name = attributes['express.name'];
            if (typeof name === 'string') {
                span.updateName(name);
            }
        },
        spanNameHook (info, defaultName) {
            if (core.getIsolationScope() === core.getDefaultIsolationScope()) {
                debugBuild.DEBUG_BUILD && core.logger.warn('Isolation scope is still default isolation scope - skipping setting transactionName');
                return defaultName;
            }
            if (info.layerType === 'request_handler') {
                // type cast b/c Otel unfortunately types info.request as any :(
                const req = info.request;
                const method = req.method ? req.method.toUpperCase() : 'GET';
                core.getIsolationScope().setTransactionName(`${method} ${info.route}`);
            }
            return defaultName;
        }
    }));
const _expressIntegration = ()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentExpress();
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for [Express](https://expressjs.com/).
 *
 * If you also want to capture errors, you need to call `setupExpressErrorHandler(app)` after you set up your Express server.
 *
 * For more information, see the [express documentation](https://docs.sentry.io/platforms/javascript/guides/express/).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *   integrations: [Sentry.expressIntegration()],
 * })
 * ```
 */ const expressIntegration = core.defineIntegration(_expressIntegration);
/**
 * An Express-compatible error handler.
 */ function expressErrorHandler(options) {
    return function sentryErrorMiddleware(error, _req, res, next) {
        const shouldHandleError = _optionalChain([
            options,
            'optionalAccess',
            (_)=>_.shouldHandleError
        ]) || defaultShouldHandleError;
        if (shouldHandleError(error)) {
            const client = core.getClient();
            // eslint-disable-next-line deprecation/deprecation
            if (client && client.getOptions().autoSessionTracking) {
                // Check if the `SessionFlusher` is instantiated on the client to go into this branch that marks the
                // `requestSession.status` as `Crashed`, and this check is necessary because the `SessionFlusher` is only
                // instantiated when the the`requestHandler` middleware is initialised, which indicates that we should be
                // running in SessionAggregates mode
                const isSessionAggregatesMode = client['_sessionFlusher'] !== undefined;
                if (isSessionAggregatesMode) {
                    // eslint-disable-next-line deprecation/deprecation
                    const requestSession = core.getIsolationScope().getRequestSession();
                    // If an error bubbles to the `errorHandler`, then this is an unhandled error, and should be reported as a
                    // Crashed session. The `_requestSession.status` is checked to ensure that this error is happening within
                    // the bounds of a request, and if so the status is updated
                    if (requestSession && requestSession.status !== undefined) {
                        requestSession.status = 'crashed';
                    }
                }
            }
            const eventId = core.captureException(error, {
                mechanism: {
                    type: 'middleware',
                    handled: false
                }
            });
            res.sentry = eventId;
            next(error);
            return;
        }
        next(error);
    };
}
/**
 * Add an Express error handler to capture errors to Sentry.
 *
 * The error handler must be before any other middleware and after all controllers.
 *
 * @param app The Express instances
 * @param options {ExpressHandlerOptions} Configuration options for the handler
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 * const express = require("express");
 *
 * const app = express();
 *
 * // Add your routes, etc.
 *
 * // Add this after all routes,
 * // but before any and other error-handling middlewares are defined
 * Sentry.setupExpressErrorHandler(app);
 *
 * app.listen(3000);
 * ```
 */ function setupExpressErrorHandler(app, options) {
    app.use(expressErrorHandler(options));
    ensureIsWrapped.ensureIsWrapped(app.use, 'express');
}
function getStatusCodeFromResponse(error) {
    const statusCode = error.status || error.statusCode || error.status_code || error.output && error.output.statusCode;
    return statusCode ? parseInt(statusCode, 10) : 500;
}
/** Returns true if response code is internal server error */ function defaultShouldHandleError(error) {
    const status = getStatusCodeFromResponse(error);
    return status >= 500;
}
exports.expressErrorHandler = expressErrorHandler;
exports.expressIntegration = expressIntegration;
exports.instrumentExpress = instrumentExpress;
exports.setupExpressErrorHandler = setupExpressErrorHandler; //# sourceMappingURL=express.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/fastify.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationFastify = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-fastify@0.43.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-fastify/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const ensureIsWrapped = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/ensureIsWrapped.js [instrumentation] (ecmascript)");
// We inline the types we care about here
const INTEGRATION_NAME = 'Fastify';
const instrumentFastify = instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>new instrumentationFastify.FastifyInstrumentation({
        requestHook (span) {
            addFastifySpanAttributes(span);
        }
    }));
const _fastifyIntegration = ()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentFastify();
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for [Fastify](https://fastify.dev/).
 *
 * If you also want to capture errors, you need to call `setupFastifyErrorHandler(app)` after you set up your Fastify server.
 *
 * For more information, see the [fastify documentation](https://docs.sentry.io/platforms/javascript/guides/fastify/).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *   integrations: [Sentry.fastifyIntegration()],
 * })
 * ```
 */ const fastifyIntegration = core.defineIntegration(_fastifyIntegration);
/**
 * Add an Fastify error handler to capture errors to Sentry.
 *
 * @param fastify The Fastify instance to which to add the error handler
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 * const Fastify = require("fastify");
 *
 * const app = Fastify();
 *
 * Sentry.setupFastifyErrorHandler(app);
 *
 * // Add your routes, etc.
 *
 * app.listen({ port: 3000 });
 * ```
 */ function setupFastifyErrorHandler(fastify) {
    const plugin = Object.assign(function(fastify, _options, done) {
        fastify.addHook('onError', async (_request, _reply, error)=>{
            core.captureException(error);
        });
        // registering `onRequest` hook here instead of using Otel `onRequest` callback b/c `onRequest` hook
        // is ironically called in the fastify `preHandler` hook which is called later in the lifecycle:
        // https://fastify.dev/docs/latest/Reference/Lifecycle/
        fastify.addHook('onRequest', async (request, _reply)=>{
            const reqWithRouteInfo = request;
            // Taken from Otel Fastify instrumentation:
            // https://github.com/open-telemetry/opentelemetry-js-contrib/blob/main/plugins/node/opentelemetry-instrumentation-fastify/src/instrumentation.ts#L94-L96
            const routeName = _optionalChain([
                reqWithRouteInfo,
                'access',
                (_)=>_.routeOptions,
                'optionalAccess',
                (_2)=>_2.url
            ]) || reqWithRouteInfo.routerPath;
            const method = _optionalChain([
                reqWithRouteInfo,
                'access',
                (_3)=>_3.routeOptions,
                'optionalAccess',
                (_4)=>_4.method
            ]) || 'GET';
            core.getIsolationScope().setTransactionName(`${method} ${routeName}`);
        });
        done();
    }, {
        [Symbol.for('skip-override')]: true,
        [Symbol.for('fastify.display-name')]: 'sentry-fastify-error-handler'
    });
    fastify.register(plugin);
    // Sadly, middleware spans do not go through `requestHook`, so we handle those here
    // We register this hook in this method, because if we register it in the integration `setup`,
    // it would always run even for users that are not even using fastify
    const client = core.getClient();
    if (client) {
        client.on('spanStart', (span)=>{
            addFastifySpanAttributes(span);
        });
    }
    ensureIsWrapped.ensureIsWrapped(fastify.addHook, 'fastify');
}
function addFastifySpanAttributes(span) {
    const attributes = core.spanToJSON(span).data || {};
    // this is one of: middleware, request_handler
    const type = attributes['fastify.type'];
    // If this is already set, or we have no fastify span, no need to process again...
    if (attributes[core.SEMANTIC_ATTRIBUTE_SENTRY_OP] || !type) {
        return;
    }
    span.setAttributes({
        [core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.http.otel.fastify',
        [core.SEMANTIC_ATTRIBUTE_SENTRY_OP]: `${type}.fastify`
    });
    // Also update the name, we don't need to "middleware - " prefix
    const name = attributes['fastify.name'] || attributes['plugin.name'] || attributes['hook.name'];
    if (typeof name === 'string') {
        // Also remove `fastify -> ` prefix
        span.updateName(name.replace(/^fastify -> /, ''));
    }
}
exports.fastifyIntegration = fastifyIntegration;
exports.instrumentFastify = instrumentFastify;
exports.setupFastifyErrorHandler = setupFastifyErrorHandler; //# sourceMappingURL=fastify.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/graphql.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationGraphql = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-graphql@0.46.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-graphql/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const opentelemetry = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+opentelemetry@8.46.0_@opentelemetry+api@1.9.0_@opentelemetry+core@1.30.1_@opent_9f3efdcc19a3583a187ce7ebb26bfca9/node_modules/@sentry/opentelemetry/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const addOriginToSpan = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/addOriginToSpan.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Graphql';
const instrumentGraphql = instrument.generateInstrumentOnce(INTEGRATION_NAME, (_options = {})=>{
    const options = getOptionsWithDefaults(_options);
    return new instrumentationGraphql.GraphQLInstrumentation({
        ...options,
        responseHook (span) {
            addOriginToSpan.addOriginToSpan(span, 'auto.graphql.otel.graphql');
            const attributes = core.spanToJSON(span).data || {};
            // If operation.name is not set, we fall back to use operation.type only
            const operationType = attributes['graphql.operation.type'];
            const operationName = attributes['graphql.operation.name'];
            if (options.useOperationNameForRootSpan && operationType) {
                const rootSpan = core.getRootSpan(span);
                // We guard to only do this on http.server spans
                const rootSpanAttributes = core.spanToJSON(rootSpan).data || {};
                const existingOperations = rootSpanAttributes[opentelemetry.SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION] || [];
                const newOperation = operationName ? `${operationType} ${operationName}` : `${operationType}`;
                // We keep track of each operation on the root span
                // This can either be a string, or an array of strings (if there are multiple operations)
                if (Array.isArray(existingOperations)) {
                    existingOperations.push(newOperation);
                    rootSpan.setAttribute(opentelemetry.SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION, existingOperations);
                } else if ("TURBOPACK compile-time truthy", 1) {
                    rootSpan.setAttribute(opentelemetry.SEMANTIC_ATTRIBUTE_SENTRY_GRAPHQL_OPERATION, [
                        existingOperations,
                        newOperation
                    ]);
                } else //TURBOPACK unreachable
                ;
            }
        }
    });
});
const _graphqlIntegration = (options = {})=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            // We set defaults here, too, because otherwise we'd update the instrumentation config
            // to the config without defaults, as `generateInstrumentOnce` automatically calls `setConfig(options)`
            // when being called the second time
            instrumentGraphql(getOptionsWithDefaults(options));
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for the [graphql](https://www.npmjs.com/package/graphql) library.
 *
 * For more information, see the [`graphqlIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/graphql/).
 *
 * @param {GraphqlOptions} options Configuration options for the GraphQL integration.
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *  integrations: [Sentry.graphqlIntegration()],
 * });
 */ const graphqlIntegration = core.defineIntegration(_graphqlIntegration);
function getOptionsWithDefaults(options) {
    return {
        ignoreResolveSpans: true,
        ignoreTrivialResolveSpans: true,
        useOperationNameForRootSpan: true,
        ...options
    };
}
exports.graphqlIntegration = graphqlIntegration;
exports.instrumentGraphql = instrumentGraphql; //# sourceMappingURL=graphql.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/kafka.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationKafkajs = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-kafkajs@0.6.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-kafkajs/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const addOriginToSpan = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/addOriginToSpan.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Kafka';
const instrumentKafka = instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>new instrumentationKafkajs.KafkaJsInstrumentation({
        consumerHook (span) {
            addOriginToSpan.addOriginToSpan(span, 'auto.kafkajs.otel.consumer');
        },
        producerHook (span) {
            addOriginToSpan.addOriginToSpan(span, 'auto.kafkajs.otel.producer');
        }
    }));
const _kafkaIntegration = ()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentKafka();
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for the [kafkajs](https://www.npmjs.com/package/kafkajs) library.
 *
 * For more information, see the [`kafkaIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/kafka/).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *  integrations: [Sentry.kafkaIntegration()],
 * });
 */ const kafkaIntegration = core.defineIntegration(_kafkaIntegration);
exports.instrumentKafka = instrumentKafka;
exports.kafkaIntegration = kafkaIntegration; //# sourceMappingURL=kafka.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/lrumemoizer.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationLruMemoizer = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-lru-memoizer@0.43.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-lru-memoizer/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'LruMemoizer';
const instrumentLruMemoizer = instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>new instrumentationLruMemoizer.LruMemoizerInstrumentation());
const _lruMemoizerIntegration = ()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentLruMemoizer();
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for the [lru-memoizer](https://www.npmjs.com/package/lru-memoizer) library.
 *
 * For more information, see the [`lruMemoizerIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/lrumemoizer/).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *  integrations: [Sentry.lruMemoizerIntegration()],
 * });
 */ const lruMemoizerIntegration = core.defineIntegration(_lruMemoizerIntegration);
exports.instrumentLruMemoizer = instrumentLruMemoizer;
exports.lruMemoizerIntegration = lruMemoizerIntegration; //# sourceMappingURL=lrumemoizer.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/mongo.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationMongodb = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-mongodb@0.50.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-mongodb/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const addOriginToSpan = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/addOriginToSpan.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Mongo';
const instrumentMongo = instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>new instrumentationMongodb.MongoDBInstrumentation({
        dbStatementSerializer: _defaultDbStatementSerializer,
        responseHook (span) {
            addOriginToSpan.addOriginToSpan(span, 'auto.db.otel.mongo');
        }
    }));
/**
 * Replaces values in document with '?', hiding PII and helping grouping.
 */ function _defaultDbStatementSerializer(commandObj) {
    const resultObj = _scrubStatement(commandObj);
    return JSON.stringify(resultObj);
}
function _scrubStatement(value) {
    if (Array.isArray(value)) {
        return value.map((element)=>_scrubStatement(element));
    }
    if (isCommandObj(value)) {
        const initial = {};
        return Object.entries(value).map(([key, element])=>[
                key,
                _scrubStatement(element)
            ]).reduce((prev, current)=>{
            if (isCommandEntry(current)) {
                prev[current[0]] = current[1];
            }
            return prev;
        }, initial);
    }
    // A value like string or number, possible contains PII, scrub it
    return '?';
}
function isCommandObj(value) {
    return typeof value === 'object' && value !== null && !isBuffer(value);
}
function isBuffer(value) {
    let isBuffer = false;
    if (typeof Buffer !== 'undefined') {
        isBuffer = Buffer.isBuffer(value);
    }
    return isBuffer;
}
function isCommandEntry(value) {
    return Array.isArray(value);
}
const _mongoIntegration = ()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentMongo();
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for the [mongodb](https://www.npmjs.com/package/mongodb) library.
 *
 * For more information, see the [`mongoIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/mongo/).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *  integrations: [Sentry.mongoIntegration()],
 * });
 * ```
 */ const mongoIntegration = core.defineIntegration(_mongoIntegration);
exports._defaultDbStatementSerializer = _defaultDbStatementSerializer;
exports.instrumentMongo = instrumentMongo;
exports.mongoIntegration = mongoIntegration; //# sourceMappingURL=mongo.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/mongoose.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationMongoose = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-mongoose@0.45.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-mongoose/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const addOriginToSpan = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/addOriginToSpan.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Mongoose';
const instrumentMongoose = instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>new instrumentationMongoose.MongooseInstrumentation({
        responseHook (span) {
            addOriginToSpan.addOriginToSpan(span, 'auto.db.otel.mongoose');
        }
    }));
const _mongooseIntegration = ()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentMongoose();
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for the [mongoose](https://www.npmjs.com/package/mongoose) library.
 *
 * For more information, see the [`mongooseIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/mongoose/).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *  integrations: [Sentry.mongooseIntegration()],
 * });
 * ```
 */ const mongooseIntegration = core.defineIntegration(_mongooseIntegration);
exports.instrumentMongoose = instrumentMongoose;
exports.mongooseIntegration = mongooseIntegration; //# sourceMappingURL=mongoose.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/mysql.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationMysql = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-mysql@0.44.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-mysql/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Mysql';
const instrumentMysql = instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>new instrumentationMysql.MySQLInstrumentation({}));
const _mysqlIntegration = ()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentMysql();
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for the [mysql](https://www.npmjs.com/package/mysql) library.
 *
 * For more information, see the [`mysqlIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/mysql/).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *  integrations: [Sentry.mysqlIntegration()],
 * });
 * ```
 */ const mysqlIntegration = core.defineIntegration(_mysqlIntegration);
exports.instrumentMysql = instrumentMysql;
exports.mysqlIntegration = mysqlIntegration; //# sourceMappingURL=mysql.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/mysql2.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationMysql2 = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-mysql2@0.44.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-mysql2/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const addOriginToSpan = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/addOriginToSpan.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Mysql2';
const instrumentMysql2 = instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>new instrumentationMysql2.MySQL2Instrumentation({
        responseHook (span) {
            addOriginToSpan.addOriginToSpan(span, 'auto.db.otel.mysql2');
        }
    }));
const _mysql2Integration = ()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentMysql2();
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for the [mysql2](https://www.npmjs.com/package/mysql2) library.
 *
 * For more information, see the [`mysql2Integration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/mysql2/).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *  integrations: [Sentry.mysqlIntegration()],
 * });
 * ```
 */ const mysql2Integration = core.defineIntegration(_mysql2Integration);
exports.instrumentMysql2 = instrumentMysql2;
exports.mysql2Integration = mysql2Integration; //# sourceMappingURL=mysql2.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/redisCache.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const SINGLE_ARG_COMMANDS = [
    'get',
    'set',
    'setex'
];
const GET_COMMANDS = [
    'get',
    'mget'
];
const SET_COMMANDS = [
    'set',
    'setex'
];
// todo: del, expire
/** Checks if a given command is in the list of redis commands.
 *  Useful because commands can come in lowercase or uppercase (depending on the library). */ function isInCommands(redisCommands, command) {
    return redisCommands.includes(command.toLowerCase());
}
/** Determine cache operation based on redis statement */ function getCacheOperation(command) {
    if (isInCommands(GET_COMMANDS, command)) {
        return 'cache.get';
    } else if (isInCommands(SET_COMMANDS, command)) {
        return 'cache.put';
    } else {
        return undefined;
    }
}
function keyHasPrefix(key, prefixes) {
    return prefixes.some((prefix)=>key.startsWith(prefix));
}
/** Safely converts a redis key to a string (comma-separated if there are multiple keys) */ function getCacheKeySafely(redisCommand, cmdArgs) {
    try {
        if (cmdArgs.length === 0) {
            return undefined;
        }
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const processArg = (arg)=>{
            if (typeof arg === 'string' || typeof arg === 'number' || Buffer.isBuffer(arg)) {
                return [
                    arg.toString()
                ];
            } else if (Array.isArray(arg)) {
                return flatten(arg.map((arg)=>processArg(arg)));
            } else {
                return [
                    '<unknown>'
                ];
            }
        };
        const firstArg = cmdArgs[0];
        if (isInCommands(SINGLE_ARG_COMMANDS, redisCommand) && firstArg != null) {
            return processArg(firstArg);
        }
        return flatten(cmdArgs.map((arg)=>processArg(arg)));
    } catch (e) {
        return undefined;
    }
}
/** Determines whether a redis operation should be considered as "cache operation" by checking if a key is prefixed.
 *  We only support certain commands (such as 'set', 'get', 'mget'). */ function shouldConsiderForCache(redisCommand, keys, prefixes) {
    if (!getCacheOperation(redisCommand)) {
        return false;
    }
    for (const key of keys){
        if (keyHasPrefix(key, prefixes)) {
            return true;
        }
    }
    return false;
}
/** Calculates size based on the cache response value */ function calculateCacheItemSize(response) {
    const getSize = (value)=>{
        try {
            if (Buffer.isBuffer(value)) return value.byteLength;
            else if (typeof value === 'string') return value.length;
            else if (typeof value === 'number') return value.toString().length;
            else if (value === null || value === undefined) return 0;
            return JSON.stringify(value).length;
        } catch (e) {
            return undefined;
        }
    };
    return Array.isArray(response) ? response.reduce((acc, curr)=>{
        const size = getSize(curr);
        return typeof size === 'number' ? acc !== undefined ? acc + size : size : acc;
    }, 0) : getSize(response);
}
// TODO(v9): This is inlined from core so we can deprecate `flatten`.
//           It's usage can be replaced with `Array.flat` in v9.
function flatten(input) {
    const result = [];
    const flattenHelper = (input)=>{
        input.forEach((el)=>{
            if (Array.isArray(el)) {
                flattenHelper(el);
            } else {
                result.push(el);
            }
        });
    };
    flattenHelper(input);
    return result;
}
exports.GET_COMMANDS = GET_COMMANDS;
exports.SET_COMMANDS = SET_COMMANDS;
exports.calculateCacheItemSize = calculateCacheItemSize;
exports.getCacheKeySafely = getCacheKeySafely;
exports.getCacheOperation = getCacheOperation;
exports.isInCommands = isInCommands;
exports.shouldConsiderForCache = shouldConsiderForCache; //# sourceMappingURL=redisCache.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/redis.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationIoredis = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-ioredis@0.46.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-ioredis/build/src/index.js [instrumentation] (ecmascript)");
const instrumentationRedis4 = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-redis-4@0.45.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-redis-4/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const redisCache = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/redisCache.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Redis';
let _redisOptions = {};
const cacheResponseHook = (span, redisCommand, cmdArgs, response)=>{
    span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, 'auto.db.otel.redis');
    const safeKey = redisCache.getCacheKeySafely(redisCommand, cmdArgs);
    const cacheOperation = redisCache.getCacheOperation(redisCommand);
    if (!safeKey || !cacheOperation || !_optionalChain([
        _redisOptions,
        'optionalAccess',
        (_)=>_.cachePrefixes
    ]) || !redisCache.shouldConsiderForCache(redisCommand, safeKey, _redisOptions.cachePrefixes)) {
        // not relevant for cache
        return;
    }
    // otel/ioredis seems to be using the old standard, as there was a change to those params: https://github.com/open-telemetry/opentelemetry-specification/issues/3199
    // We are using params based on the docs: https://opentelemetry.io/docs/specs/semconv/attributes-registry/network/
    const networkPeerAddress = _optionalChain([
        core.spanToJSON,
        'call',
        (_2)=>_2(span),
        'access',
        (_3)=>_3.data,
        'optionalAccess',
        (_4)=>_4['net.peer.name']
    ]);
    const networkPeerPort = _optionalChain([
        core.spanToJSON,
        'call',
        (_5)=>_5(span),
        'access',
        (_6)=>_6.data,
        'optionalAccess',
        (_7)=>_7['net.peer.port']
    ]);
    if (networkPeerPort && networkPeerAddress) {
        span.setAttributes({
            'network.peer.address': networkPeerAddress,
            'network.peer.port': networkPeerPort
        });
    }
    const cacheItemSize = redisCache.calculateCacheItemSize(response);
    if (cacheItemSize) {
        span.setAttribute(core.SEMANTIC_ATTRIBUTE_CACHE_ITEM_SIZE, cacheItemSize);
    }
    if (redisCache.isInCommands(redisCache.GET_COMMANDS, redisCommand) && cacheItemSize !== undefined) {
        span.setAttribute(core.SEMANTIC_ATTRIBUTE_CACHE_HIT, cacheItemSize > 0);
    }
    span.setAttributes({
        [core.SEMANTIC_ATTRIBUTE_SENTRY_OP]: cacheOperation,
        [core.SEMANTIC_ATTRIBUTE_CACHE_KEY]: safeKey
    });
    const spanDescription = safeKey.join(', ');
    span.updateName(core.truncate(spanDescription, 1024));
};
const instrumentIORedis = instrument.generateInstrumentOnce('IORedis', ()=>{
    return new instrumentationIoredis.IORedisInstrumentation({
        responseHook: cacheResponseHook
    });
});
const instrumentRedis4 = instrument.generateInstrumentOnce('Redis-4', ()=>{
    return new instrumentationRedis4.RedisInstrumentation({
        responseHook: cacheResponseHook
    });
});
/** To be able to preload all Redis OTel instrumentations with just one ID ("Redis"), all the instrumentations are generated in this one function  */ const instrumentRedis = Object.assign(()=>{
    instrumentIORedis();
    instrumentRedis4();
// todo: implement them gradually
// new LegacyRedisInstrumentation({}),
}, {
    id: INTEGRATION_NAME
});
const _redisIntegration = (options = {})=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            _redisOptions = options;
            instrumentRedis();
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for the [redis](https://www.npmjs.com/package/redis) and
 * [ioredis](https://www.npmjs.com/package/ioredis) libraries.
 *
 * For more information, see the [`redisIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/redis/).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *  integrations: [Sentry.redisIntegration()],
 * });
 * ```
 */ const redisIntegration = core.defineIntegration(_redisIntegration);
exports.instrumentRedis = instrumentRedis;
exports.redisIntegration = redisIntegration; //# sourceMappingURL=redis.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/nest/helpers.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _nullishCoalesce } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const sentryPatched = 'sentryPatched';
/**
 * Helper checking if a concrete target class is already patched.
 *
 * We already guard duplicate patching with isWrapped. However, isWrapped checks whether a file has been patched, whereas we use this check for concrete target classes.
 * This check might not be necessary, but better to play it safe.
 */ function isPatched(target) {
    if (target.sentryPatched) {
        return true;
    }
    core.addNonEnumerableProperty(target, sentryPatched, true);
    return false;
}
/**
 * Returns span options for nest middleware spans.
 */ // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
function getMiddlewareSpanOptions(target, name = undefined) {
    const span_name = _nullishCoalesce(name, ()=>target.name); // fallback to class name if no name is provided
    return {
        name: span_name,
        attributes: {
            [core.SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'middleware.nestjs',
            [core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.middleware.nestjs'
        }
    };
}
/**
 * Returns span options for nest event spans.
 */ function getEventSpanOptions(event) {
    return {
        name: `event ${event}`,
        attributes: {
            [core.SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'event.nestjs',
            [core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.event.nestjs'
        },
        forceTransaction: true
    };
}
/**
 * Adds instrumentation to a js observable and attaches the span to an active parent span.
 */ function instrumentObservable(observable, activeSpan) {
    if (activeSpan) {
        // eslint-disable-next-line @typescript-eslint/unbound-method
        observable.subscribe = new Proxy(observable.subscribe, {
            apply: (originalSubscribe, thisArgSubscribe, argsSubscribe)=>{
                return core.withActiveSpan(activeSpan, ()=>{
                    const subscription = originalSubscribe.apply(thisArgSubscribe, argsSubscribe);
                    subscription.add(()=>activeSpan.end());
                    return subscription;
                });
            }
        });
    }
}
/**
 * Proxies the next() call in a nestjs middleware to end the span when it is called.
 */ function getNextProxy(next, span, prevSpan) {
    return new Proxy(next, {
        apply: (originalNext, thisArgNext, argsNext)=>{
            span.end();
            if (prevSpan) {
                return core.withActiveSpan(prevSpan, ()=>{
                    return Reflect.apply(originalNext, thisArgNext, argsNext);
                });
            } else {
                return Reflect.apply(originalNext, thisArgNext, argsNext);
            }
        }
    });
}
exports.getEventSpanOptions = getEventSpanOptions;
exports.getMiddlewareSpanOptions = getMiddlewareSpanOptions;
exports.getNextProxy = getNextProxy;
exports.instrumentObservable = instrumentObservable;
exports.isPatched = isPatched; //# sourceMappingURL=helpers.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/nest/sentry-nest-event-instrumentation.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const core$1 = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js [instrumentation] (ecmascript)");
const instrumentation = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation@0.56.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation/build/esm/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const helpers = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/nest/helpers.js [instrumentation] (ecmascript)");
const supportedVersions = [
    '>=2.0.0'
];
/**
 * Custom instrumentation for nestjs event-emitter
 *
 * This hooks into the `OnEvent` decorator, which is applied on event handlers.
 */ class SentryNestEventInstrumentation extends instrumentation.InstrumentationBase {
    static __initStatic() {
        this.COMPONENT = '@nestjs/event-emitter';
    }
    static __initStatic2() {
        this.COMMON_ATTRIBUTES = {
            component: SentryNestEventInstrumentation.COMPONENT
        };
    }
    constructor(config = {}){
        super('sentry-nestjs-event', core.SDK_VERSION, config);
    }
    /**
   * Initializes the instrumentation by defining the modules to be patched.
   */ init() {
        const moduleDef = new instrumentation.InstrumentationNodeModuleDefinition(SentryNestEventInstrumentation.COMPONENT, supportedVersions);
        moduleDef.files.push(this._getOnEventFileInstrumentation(supportedVersions));
        return moduleDef;
    }
    /**
   * Wraps the @OnEvent decorator.
   */ _getOnEventFileInstrumentation(versions) {
        return new instrumentation.InstrumentationNodeModuleFile('@nestjs/event-emitter/dist/decorators/on-event.decorator.js', versions, (moduleExports)=>{
            if (core$1.isWrapped(moduleExports.OnEvent)) {
                this._unwrap(moduleExports, 'OnEvent');
            }
            this._wrap(moduleExports, 'OnEvent', this._createWrapOnEvent());
            return moduleExports;
        }, (moduleExports)=>{
            this._unwrap(moduleExports, 'OnEvent');
        });
    }
    /**
   * Creates a wrapper function for the @OnEvent decorator.
   */ _createWrapOnEvent() {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        return function wrapOnEvent(original) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            return function wrappedOnEvent(event, options) {
                const eventName = Array.isArray(event) ? event.join(',') : typeof event === 'string' || typeof event === 'symbol' ? event.toString() : '<unknown_event>';
                // Get the original decorator result
                const decoratorResult = original(event, options);
                // Return a new decorator function that wraps the handler
                return function(target, propertyKey, descriptor) {
                    if (!descriptor.value || typeof descriptor.value !== 'function' || target.__SENTRY_INTERNAL__) {
                        return decoratorResult(target, propertyKey, descriptor);
                    }
                    // Get the original handler
                    const originalHandler = descriptor.value;
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                    const handlerName = originalHandler.name || propertyKey;
                    // Instrument the handler
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    descriptor.value = async function(...args) {
                        return core.startSpan(helpers.getEventSpanOptions(eventName), async ()=>{
                            try {
                                // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
                                const result = await originalHandler.apply(this, args);
                                return result;
                            } catch (error) {
                                // exceptions from event handlers are not caught by global error filter
                                core.captureException(error);
                                throw error;
                            }
                        });
                    };
                    // Preserve the original function name
                    Object.defineProperty(descriptor.value, 'name', {
                        value: handlerName,
                        configurable: true
                    });
                    // Apply the original decorator
                    return decoratorResult(target, propertyKey, descriptor);
                };
            };
        };
    }
}
SentryNestEventInstrumentation.__initStatic();
SentryNestEventInstrumentation.__initStatic2();
exports.SentryNestEventInstrumentation = SentryNestEventInstrumentation; //# sourceMappingURL=sentry-nest-event-instrumentation.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/nest/sentry-nest-instrumentation.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _nullishCoalesce, _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const core$1 = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+core@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/core/build/esm/index.js [instrumentation] (ecmascript)");
const instrumentation = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation@0.56.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation/build/esm/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const helpers = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/nest/helpers.js [instrumentation] (ecmascript)");
const supportedVersions = [
    '>=8.0.0 <11'
];
/**
 * Custom instrumentation for nestjs.
 *
 * This hooks into
 * 1. @Injectable decorator, which is applied on class middleware, interceptors and guards.
 * 2. @Catch decorator, which is applied on exception filters.
 */ class SentryNestInstrumentation extends instrumentation.InstrumentationBase {
    static __initStatic() {
        this.COMPONENT = '@nestjs/common';
    }
    static __initStatic2() {
        this.COMMON_ATTRIBUTES = {
            component: SentryNestInstrumentation.COMPONENT
        };
    }
    constructor(config = {}){
        super('sentry-nestjs', core.SDK_VERSION, config);
    }
    /**
   * Initializes the instrumentation by defining the modules to be patched.
   */ init() {
        const moduleDef = new instrumentation.InstrumentationNodeModuleDefinition(SentryNestInstrumentation.COMPONENT, supportedVersions);
        moduleDef.files.push(this._getInjectableFileInstrumentation(supportedVersions), this._getCatchFileInstrumentation(supportedVersions));
        return moduleDef;
    }
    /**
   * Wraps the @Injectable decorator.
   */ _getInjectableFileInstrumentation(versions) {
        return new instrumentation.InstrumentationNodeModuleFile('@nestjs/common/decorators/core/injectable.decorator.js', versions, (moduleExports)=>{
            if (core$1.isWrapped(moduleExports.Injectable)) {
                this._unwrap(moduleExports, 'Injectable');
            }
            this._wrap(moduleExports, 'Injectable', this._createWrapInjectable());
            return moduleExports;
        }, (moduleExports)=>{
            this._unwrap(moduleExports, 'Injectable');
        });
    }
    /**
   * Wraps the @Catch decorator.
   */ _getCatchFileInstrumentation(versions) {
        return new instrumentation.InstrumentationNodeModuleFile('@nestjs/common/decorators/core/catch.decorator.js', versions, (moduleExports)=>{
            if (core$1.isWrapped(moduleExports.Catch)) {
                this._unwrap(moduleExports, 'Catch');
            }
            this._wrap(moduleExports, 'Catch', this._createWrapCatch());
            return moduleExports;
        }, (moduleExports)=>{
            this._unwrap(moduleExports, 'Catch');
        });
    }
    /**
   * Creates a wrapper function for the @Injectable decorator.
   */ _createWrapInjectable() {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        return function wrapInjectable(original) {
            return function wrappedInjectable(options) {
                return function(target) {
                    // patch middleware
                    if (typeof target.prototype.use === 'function' && !target.__SENTRY_INTERNAL__) {
                        // patch only once
                        if (helpers.isPatched(target)) {
                            return original(options)(target);
                        }
                        target.prototype.use = new Proxy(target.prototype.use, {
                            apply: (originalUse, thisArgUse, argsUse)=>{
                                const [req, res, next, ...args] = argsUse;
                                // Check that we can reasonably assume that the target is a middleware.
                                // Without these guards, instrumentation will fail if a function named 'use' on a service, which is
                                // decorated with @Injectable, is called.
                                if (!req || !res || !next || typeof next !== 'function') {
                                    return originalUse.apply(thisArgUse, argsUse);
                                }
                                const prevSpan = core.getActiveSpan();
                                return core.startSpanManual(helpers.getMiddlewareSpanOptions(target), (span)=>{
                                    // proxy next to end span on call
                                    const nextProxy = helpers.getNextProxy(next, span, prevSpan);
                                    return originalUse.apply(thisArgUse, [
                                        req,
                                        res,
                                        nextProxy,
                                        args
                                    ]);
                                });
                            }
                        });
                    }
                    // patch guards
                    if (typeof target.prototype.canActivate === 'function' && !target.__SENTRY_INTERNAL__) {
                        // patch only once
                        if (helpers.isPatched(target)) {
                            return original(options)(target);
                        }
                        target.prototype.canActivate = new Proxy(target.prototype.canActivate, {
                            apply: (originalCanActivate, thisArgCanActivate, argsCanActivate)=>{
                                const context = argsCanActivate[0];
                                if (!context) {
                                    return originalCanActivate.apply(thisArgCanActivate, argsCanActivate);
                                }
                                return core.startSpan(helpers.getMiddlewareSpanOptions(target), ()=>{
                                    return originalCanActivate.apply(thisArgCanActivate, argsCanActivate);
                                });
                            }
                        });
                    }
                    // patch pipes
                    if (typeof target.prototype.transform === 'function' && !target.__SENTRY_INTERNAL__) {
                        if (helpers.isPatched(target)) {
                            return original(options)(target);
                        }
                        target.prototype.transform = new Proxy(target.prototype.transform, {
                            apply: (originalTransform, thisArgTransform, argsTransform)=>{
                                const value = argsTransform[0];
                                const metadata = argsTransform[1];
                                if (!value || !metadata) {
                                    return originalTransform.apply(thisArgTransform, argsTransform);
                                }
                                return core.startSpan(helpers.getMiddlewareSpanOptions(target), ()=>{
                                    return originalTransform.apply(thisArgTransform, argsTransform);
                                });
                            }
                        });
                    }
                    // patch interceptors
                    if (typeof target.prototype.intercept === 'function' && !target.__SENTRY_INTERNAL__) {
                        if (helpers.isPatched(target)) {
                            return original(options)(target);
                        }
                        target.prototype.intercept = new Proxy(target.prototype.intercept, {
                            apply: (originalIntercept, thisArgIntercept, argsIntercept)=>{
                                const context = argsIntercept[0];
                                const next = argsIntercept[1];
                                const parentSpan = core.getActiveSpan();
                                let afterSpan;
                                // Check that we can reasonably assume that the target is an interceptor.
                                if (!context || !next || typeof next.handle !== 'function') {
                                    return originalIntercept.apply(thisArgIntercept, argsIntercept);
                                }
                                return core.startSpanManual(helpers.getMiddlewareSpanOptions(target), (beforeSpan)=>{
                                    // eslint-disable-next-line @typescript-eslint/unbound-method
                                    next.handle = new Proxy(next.handle, {
                                        apply: (originalHandle, thisArgHandle, argsHandle)=>{
                                            beforeSpan.end();
                                            if (parentSpan) {
                                                return core.withActiveSpan(parentSpan, ()=>{
                                                    const handleReturnObservable = Reflect.apply(originalHandle, thisArgHandle, argsHandle);
                                                    if (!context._sentryInterceptorInstrumented) {
                                                        core.addNonEnumerableProperty(context, '_sentryInterceptorInstrumented', true);
                                                        afterSpan = core.startInactiveSpan(helpers.getMiddlewareSpanOptions(target, 'Interceptors - After Route'));
                                                    }
                                                    return handleReturnObservable;
                                                });
                                            } else {
                                                const handleReturnObservable = Reflect.apply(originalHandle, thisArgHandle, argsHandle);
                                                if (!context._sentryInterceptorInstrumented) {
                                                    core.addNonEnumerableProperty(context, '_sentryInterceptorInstrumented', true);
                                                    afterSpan = core.startInactiveSpan(helpers.getMiddlewareSpanOptions(target, 'Interceptors - After Route'));
                                                }
                                                return handleReturnObservable;
                                            }
                                        }
                                    });
                                    let returnedObservableInterceptMaybePromise;
                                    try {
                                        returnedObservableInterceptMaybePromise = originalIntercept.apply(thisArgIntercept, argsIntercept);
                                    } catch (e) {
                                        _optionalChain([
                                            beforeSpan,
                                            'optionalAccess',
                                            (_)=>_.end,
                                            'call',
                                            (_2)=>_2()
                                        ]);
                                        _optionalChain([
                                            afterSpan,
                                            'optionalAccess',
                                            (_3)=>_3.end,
                                            'call',
                                            (_4)=>_4()
                                        ]);
                                        throw e;
                                    }
                                    if (!afterSpan) {
                                        return returnedObservableInterceptMaybePromise;
                                    }
                                    // handle async interceptor
                                    if (core.isThenable(returnedObservableInterceptMaybePromise)) {
                                        return returnedObservableInterceptMaybePromise.then((observable)=>{
                                            helpers.instrumentObservable(observable, _nullishCoalesce(afterSpan, ()=>parentSpan));
                                            return observable;
                                        }, (e)=>{
                                            _optionalChain([
                                                beforeSpan,
                                                'optionalAccess',
                                                (_5)=>_5.end,
                                                'call',
                                                (_6)=>_6()
                                            ]);
                                            _optionalChain([
                                                afterSpan,
                                                'optionalAccess',
                                                (_7)=>_7.end,
                                                'call',
                                                (_8)=>_8()
                                            ]);
                                            throw e;
                                        });
                                    }
                                    // handle sync interceptor
                                    if (typeof returnedObservableInterceptMaybePromise.subscribe === 'function') {
                                        helpers.instrumentObservable(returnedObservableInterceptMaybePromise, _nullishCoalesce(afterSpan, ()=>parentSpan));
                                    }
                                    return returnedObservableInterceptMaybePromise;
                                });
                            }
                        });
                    }
                    return original(options)(target);
                };
            };
        };
    }
    /**
   * Creates a wrapper function for the @Catch decorator. Used to instrument exception filters.
   */ _createWrapCatch() {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        return function wrapCatch(original) {
            return function wrappedCatch(...exceptions) {
                return function(target) {
                    if (typeof target.prototype.catch === 'function' && !target.__SENTRY_INTERNAL__) {
                        // patch only once
                        if (helpers.isPatched(target)) {
                            return original(...exceptions)(target);
                        }
                        target.prototype.catch = new Proxy(target.prototype.catch, {
                            apply: (originalCatch, thisArgCatch, argsCatch)=>{
                                const exception = argsCatch[0];
                                const host = argsCatch[1];
                                if (!exception || !host) {
                                    return originalCatch.apply(thisArgCatch, argsCatch);
                                }
                                return core.startSpan(helpers.getMiddlewareSpanOptions(target), ()=>{
                                    return originalCatch.apply(thisArgCatch, argsCatch);
                                });
                            }
                        });
                    }
                    return original(...exceptions)(target);
                };
            };
        };
    }
}
SentryNestInstrumentation.__initStatic();
SentryNestInstrumentation.__initStatic2();
exports.SentryNestInstrumentation = SentryNestInstrumentation; //# sourceMappingURL=sentry-nest-instrumentation.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/nest/nest.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationNestjsCore = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-nestjs-core@0.43.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-nestjs-core/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const sentryNestEventInstrumentation = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/nest/sentry-nest-event-instrumentation.js [instrumentation] (ecmascript)");
const sentryNestInstrumentation = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/nest/sentry-nest-instrumentation.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Nest';
const instrumentNestCore = instrument.generateInstrumentOnce('Nest-Core', ()=>{
    return new instrumentationNestjsCore.NestInstrumentation();
});
const instrumentNestCommon = instrument.generateInstrumentOnce('Nest-Common', ()=>{
    return new sentryNestInstrumentation.SentryNestInstrumentation();
});
const instrumentNestEvent = instrument.generateInstrumentOnce('Nest-Event', ()=>{
    return new sentryNestEventInstrumentation.SentryNestEventInstrumentation();
});
const instrumentNest = Object.assign(()=>{
    instrumentNestCore();
    instrumentNestCommon();
    instrumentNestEvent();
}, {
    id: INTEGRATION_NAME
});
/**
 * Integration capturing tracing data for NestJS.
 *
 * @deprecated The `nestIntegration` is deprecated. Instead, use the NestJS SDK directly (`@sentry/nestjs`), or use the `nestIntegration` export from `@sentry/nestjs`.
 */ const nestIntegration = core.defineIntegration(()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentNest();
        }
    };
});
/**
 * Setup an error handler for Nest.
 *
 * @deprecated `setupNestErrorHandler` is deprecated.
 * Instead use the `@sentry/nestjs` package, which has more functional APIs for capturing errors.
 * See the [`@sentry/nestjs` Setup Guide](https://docs.sentry.io/platforms/javascript/guides/nestjs/) for how to set up the Sentry NestJS SDK.
 */ function setupNestErrorHandler(app, baseFilter) {
    core.consoleSandbox(()=>{
        // eslint-disable-next-line no-console
        console.warn('[Sentry] Warning: You used the `setupNestErrorHandler()` method to set up Sentry error monitoring. This function is deprecated and will be removed in the next major version. Instead, it is recommended to use the `@sentry/nestjs` package. To set up the NestJS SDK see: https://docs.sentry.io/platforms/javascript/guides/nestjs/');
    });
    // Sadly, NestInstrumentation has no requestHook, so we need to add the attributes here
    // We register this hook in this method, because if we register it in the integration `setup`,
    // it would always run even for users that are not even using Nest.js
    const client = core.getClient();
    if (client) {
        client.on('spanStart', (span)=>{
            addNestSpanAttributes(span);
        });
    }
    app.useGlobalInterceptors({
        intercept (context, next) {
            if (core.getIsolationScope() === core.getDefaultIsolationScope()) {
                core.logger.warn('Isolation scope is still the default isolation scope, skipping setting transactionName.');
                return next.handle();
            }
            if (context.getType() === 'http') {
                // getRequest() returns either a FastifyRequest or ExpressRequest, depending on the used adapter
                const req = context.switchToHttp().getRequest();
                if ('routeOptions' in req && req.routeOptions && req.routeOptions.url) {
                    // fastify case
                    core.getIsolationScope().setTransactionName(`${_optionalChain([
                        req,
                        'access',
                        (_)=>_.routeOptions,
                        'access',
                        (_2)=>_2.method,
                        'optionalAccess',
                        (_3)=>_3.toUpperCase,
                        'call',
                        (_4)=>_4()
                    ]) || 'GET'} ${req.routeOptions.url}`);
                } else if ('route' in req && req.route && req.route.path) {
                    // express case
                    core.getIsolationScope().setTransactionName(`${_optionalChain([
                        req,
                        'access',
                        (_5)=>_5.method,
                        'optionalAccess',
                        (_6)=>_6.toUpperCase,
                        'call',
                        (_7)=>_7()
                    ]) || 'GET'} ${req.route.path}`);
                }
            }
            return next.handle();
        }
    });
    const wrappedFilter = new Proxy(baseFilter, {
        get (target, prop, receiver) {
            if (prop === 'catch') {
                const originalCatch = Reflect.get(target, prop, receiver);
                return (exception, host)=>{
                    const exceptionIsObject = typeof exception === 'object' && exception !== null;
                    const exceptionStatusCode = exceptionIsObject && 'status' in exception ? exception.status : null;
                    const exceptionErrorProperty = exceptionIsObject && 'error' in exception ? exception.error : null;
                    /*
          Don't report expected NestJS control flow errors
          - `HttpException` errors will have a `status` property
          - `RpcException` errors will have an `error` property
           */ if (exceptionStatusCode !== null || exceptionErrorProperty !== null) {
                        return originalCatch.apply(target, [
                            exception,
                            host
                        ]);
                    }
                    core.captureException(exception);
                    return originalCatch.apply(target, [
                        exception,
                        host
                    ]);
                };
            }
            return Reflect.get(target, prop, receiver);
        }
    });
    app.useGlobalFilters(wrappedFilter);
}
function addNestSpanAttributes(span) {
    const attributes = core.spanToJSON(span).data || {};
    // this is one of: app_creation, request_context, handler
    const type = attributes['nestjs.type'];
    // If this is already set, or we have no nest.js span, no need to process again...
    if (attributes[core.SEMANTIC_ATTRIBUTE_SENTRY_OP] || !type) {
        return;
    }
    span.setAttributes({
        [core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.http.otel.nestjs',
        [core.SEMANTIC_ATTRIBUTE_SENTRY_OP]: `${type}.nestjs`
    });
}
exports.instrumentNest = instrumentNest;
exports.nestIntegration = nestIntegration;
exports.setupNestErrorHandler = setupNestErrorHandler; //# sourceMappingURL=nest.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/postgres.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationPg = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-pg@0.49.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-pg/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const addOriginToSpan = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/addOriginToSpan.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Postgres';
const instrumentPostgres = instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>new instrumentationPg.PgInstrumentation({
        requireParentSpan: true,
        requestHook (span) {
            addOriginToSpan.addOriginToSpan(span, 'auto.db.otel.postgres');
        }
    }));
const _postgresIntegration = ()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentPostgres();
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for the [pg](https://www.npmjs.com/package/pg) library.
 *
 * For more information, see the [`postgresIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/postgres/).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *  integrations: [Sentry.postgresIntegration()],
 * });
 * ```
 */ const postgresIntegration = core.defineIntegration(_postgresIntegration);
exports.instrumentPostgres = instrumentPostgres;
exports.postgresIntegration = postgresIntegration; //# sourceMappingURL=postgres.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/prisma.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const prismaInstrumentation = __turbopack_context__.r("[project]/node_modules/.pnpm/@prisma+instrumentation@5.19.1/node_modules/@prisma/instrumentation/dist/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Prisma';
const instrumentPrisma = instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>{
    const EsmInteropPrismaInstrumentation = // @ts-expect-error We need to do the following for interop reasons
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    _optionalChain([
        prismaInstrumentation,
        'access',
        (_)=>_.default,
        'optionalAccess',
        (_2)=>_2.PrismaInstrumentation
    ]) || prismaInstrumentation.PrismaInstrumentation;
    return new EsmInteropPrismaInstrumentation({});
});
const _prismaIntegration = ()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentPrisma();
        },
        setup (client) {
            client.on('spanStart', (span)=>{
                const spanJSON = core.spanToJSON(span);
                if (_optionalChain([
                    spanJSON,
                    'access',
                    (_3)=>_3.description,
                    'optionalAccess',
                    (_4)=>_4.startsWith,
                    'call',
                    (_5)=>_5('prisma:')
                ])) {
                    span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, 'auto.db.otel.prisma');
                }
                if (spanJSON.description === 'prisma:engine:db_query') {
                    span.setAttribute('db.system', 'prisma');
                }
            });
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for the [prisma](https://www.npmjs.com/package/prisma) library.
 *
 * For more information, see the [`prismaIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/prisma/).
 *
 * @example
 *
 * Make sure `previewFeatures = ["tracing"]` is set in the prisma client generator block. See the
 * [prisma docs](https://www.prisma.io/docs/concepts/components/prisma-client/opentelemetry-tracing) for more details.
 *
 * ```prisma
 * generator client {
 *  provider = "prisma-client-js"
 *  previewFeatures = ["tracing"]
 * }
 * ```
 *
 * Then you can use the integration like this:
 *
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *  integrations: [Sentry.prismaIntegration()],
 * });
 * ```
 */ const prismaIntegration = core.defineIntegration(_prismaIntegration);
exports.instrumentPrisma = instrumentPrisma;
exports.prismaIntegration = prismaIntegration; //# sourceMappingURL=prisma.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/hapi/index.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationHapi = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-hapi@0.44.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-hapi/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const debugBuild = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/debug-build.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const ensureIsWrapped = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/ensureIsWrapped.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Hapi';
const instrumentHapi = instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>new instrumentationHapi.HapiInstrumentation());
const _hapiIntegration = ()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentHapi();
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for [Hapi](https://hapi.dev/).
 *
 * If you also want to capture errors, you need to call `setupHapiErrorHandler(server)` after you set up your server.
 *
 * For more information, see the [hapi documentation](https://docs.sentry.io/platforms/javascript/guides/hapi/).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *   integrations: [Sentry.hapiIntegration()],
 * })
 * ```
 */ const hapiIntegration = core.defineIntegration(_hapiIntegration);
function isErrorEvent(event) {
    return event && event.error !== undefined;
}
function sendErrorToSentry(errorData) {
    core.captureException(errorData, {
        mechanism: {
            type: 'hapi',
            handled: false,
            data: {
                function: 'hapiErrorPlugin'
            }
        }
    });
}
const hapiErrorPlugin = {
    name: 'SentryHapiErrorPlugin',
    version: core.SDK_VERSION,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    register: async function(serverArg) {
        const server = serverArg;
        server.events.on({
            name: 'request',
            channels: [
                'error'
            ]
        }, (request, event)=>{
            if (core.getIsolationScope() !== core.getDefaultIsolationScope()) {
                const route = request.route;
                if (route && route.path) {
                    core.getIsolationScope().setTransactionName(`${_optionalChain([
                        route,
                        'access',
                        (_)=>_.method,
                        'optionalAccess',
                        (_2)=>_2.toUpperCase,
                        'call',
                        (_3)=>_3()
                    ]) || 'GET'} ${route.path}`);
                }
            } else {
                debugBuild.DEBUG_BUILD && core.logger.warn('Isolation scope is still the default isolation scope - skipping setting transactionName');
            }
            if (isErrorEvent(event)) {
                sendErrorToSentry(event.error);
            }
        });
    }
};
/**
 * Add a Hapi plugin to capture errors to Sentry.
 *
 * @param server The Hapi server to attach the error handler to
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 * const Hapi = require('@hapi/hapi');
 *
 * const init = async () => {
 *   const server = Hapi.server();
 *
 *   // all your routes here
 *
 *   await Sentry.setupHapiErrorHandler(server);
 *
 *   await server.start();
 * };
 * ```
 */ async function setupHapiErrorHandler(server) {
    await server.register(hapiErrorPlugin);
    // Sadly, middleware spans do not go through `requestHook`, so we handle those here
    // We register this hook in this method, because if we register it in the integration `setup`,
    // it would always run even for users that are not even using hapi
    const client = core.getClient();
    if (client) {
        client.on('spanStart', (span)=>{
            addHapiSpanAttributes(span);
        });
    }
    // eslint-disable-next-line @typescript-eslint/unbound-method
    ensureIsWrapped.ensureIsWrapped(server.register, 'hapi');
}
function addHapiSpanAttributes(span) {
    const attributes = core.spanToJSON(span).data || {};
    // this is one of: router, plugin, server.ext
    const type = attributes['hapi.type'];
    // If this is already set, or we have no Hapi span, no need to process again...
    if (attributes[core.SEMANTIC_ATTRIBUTE_SENTRY_OP] || !type) {
        return;
    }
    span.setAttributes({
        [core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.http.otel.hapi',
        [core.SEMANTIC_ATTRIBUTE_SENTRY_OP]: `${type}.hapi`
    });
}
exports.hapiErrorPlugin = hapiErrorPlugin;
exports.hapiIntegration = hapiIntegration;
exports.instrumentHapi = instrumentHapi;
exports.setupHapiErrorHandler = setupHapiErrorHandler; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/koa.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationKoa = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-koa@0.46.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-koa/build/src/index.js [instrumentation] (ecmascript)");
const semanticConventions = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.36.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const debugBuild = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/debug-build.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const ensureIsWrapped = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/ensureIsWrapped.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Koa';
const instrumentKoa = instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>new instrumentationKoa.KoaInstrumentation({
        requestHook (span, info) {
            addKoaSpanAttributes(span);
            if (core.getIsolationScope() === core.getDefaultIsolationScope()) {
                debugBuild.DEBUG_BUILD && core.logger.warn('Isolation scope is default isolation scope - skipping setting transactionName');
                return;
            }
            const attributes = core.spanToJSON(span).data;
            const route = attributes && attributes[semanticConventions.ATTR_HTTP_ROUTE];
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            const method = _optionalChain([
                info,
                'optionalAccess',
                (_)=>_.context,
                'optionalAccess',
                (_2)=>_2.request,
                'optionalAccess',
                (_3)=>_3.method,
                'optionalAccess',
                (_4)=>_4.toUpperCase,
                'call',
                (_5)=>_5()
            ]) || 'GET';
            if (route) {
                core.getIsolationScope().setTransactionName(`${method} ${route}`);
            }
        }
    }));
const _koaIntegration = ()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentKoa();
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for [Koa](https://koajs.com/).
 *
 * If you also want to capture errors, you need to call `setupKoaErrorHandler(app)` after you set up your Koa server.
 *
 * For more information, see the [koa documentation](https://docs.sentry.io/platforms/javascript/guides/koa/).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *   integrations: [Sentry.koaIntegration()],
 * })
 * ```
 */ const koaIntegration = core.defineIntegration(_koaIntegration);
/**
 * Add an Koa error handler to capture errors to Sentry.
 *
 * The error handler must be before any other middleware and after all controllers.
 *
 * @param app The Express instances
 * @param options {ExpressHandlerOptions} Configuration options for the handler
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 * const Koa = require("koa");
 *
 * const app = new Koa();
 *
 * Sentry.setupKoaErrorHandler(app);
 *
 * // Add your routes, etc.
 *
 * app.listen(3000);
 * ```
 */ // eslint-disable-next-line @typescript-eslint/no-explicit-any
const setupKoaErrorHandler = (app)=>{
    app.use(async (ctx, next)=>{
        try {
            await next();
        } catch (error) {
            core.captureException(error);
            throw error;
        }
    });
    ensureIsWrapped.ensureIsWrapped(app.use, 'koa');
};
function addKoaSpanAttributes(span) {
    span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, 'auto.http.otel.koa');
    const attributes = core.spanToJSON(span).data || {};
    // this is one of: middleware, router
    const type = attributes['koa.type'];
    if (type) {
        span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_OP, `${type}.koa`);
    }
    // Also update the name
    const name = attributes['koa.name'];
    if (typeof name === 'string') {
        // Somehow, name is sometimes `''` for middleware spans
        // See: https://github.com/open-telemetry/opentelemetry-js-contrib/issues/2220
        span.updateName(name || '< unknown >');
    }
}
exports.instrumentKoa = instrumentKoa;
exports.koaIntegration = koaIntegration;
exports.setupKoaErrorHandler = setupKoaErrorHandler; //# sourceMappingURL=koa.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/connect.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationConnect = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-connect@0.42.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-connect/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const ensureIsWrapped = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/ensureIsWrapped.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Connect';
const instrumentConnect = instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>new instrumentationConnect.ConnectInstrumentation());
const _connectIntegration = ()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentConnect();
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for [Connect](https://github.com/senchalabs/connect/).
 *
 * If you also want to capture errors, you need to call `setupConnectErrorHandler(app)` after you initialize your connect app.
 *
 * For more information, see the [connect documentation](https://docs.sentry.io/platforms/javascript/guides/connect/).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *   integrations: [Sentry.connectIntegration()],
 * })
 * ```
 */ const connectIntegration = core.defineIntegration(_connectIntegration);
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function connectErrorMiddleware(err, req, res, next) {
    core.captureException(err);
    next(err);
}
/**
 * Add a Connect middleware to capture errors to Sentry.
 *
 * @param app The Connect app to attach the error handler to
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 * const connect = require("connect");
 *
 * const app = connect();
 *
 * Sentry.setupConnectErrorHandler(app);
 *
 * // Add you connect routes here
 *
 * app.listen(3000);
 * ```
 */ const setupConnectErrorHandler = (app)=>{
    app.use(connectErrorMiddleware);
    // Sadly, ConnectInstrumentation has no requestHook, so we need to add the attributes here
    // We register this hook in this method, because if we register it in the integration `setup`,
    // it would always run even for users that are not even using connect
    const client = core.getClient();
    if (client) {
        client.on('spanStart', (span)=>{
            addConnectSpanAttributes(span);
        });
    }
    ensureIsWrapped.ensureIsWrapped(app.use, 'connect');
};
function addConnectSpanAttributes(span) {
    const attributes = core.spanToJSON(span).data || {};
    // this is one of: middleware, request_handler
    const type = attributes['connect.type'];
    // If this is already set, or we have no connect span, no need to process again...
    if (attributes[core.SEMANTIC_ATTRIBUTE_SENTRY_OP] || !type) {
        return;
    }
    span.setAttributes({
        [core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.http.otel.connect',
        [core.SEMANTIC_ATTRIBUTE_SENTRY_OP]: `${type}.connect`
    });
    // Also update the name, we don't need to "middleware - " prefix
    const name = attributes['connect.name'];
    if (typeof name === 'string') {
        span.updateName(name);
    }
}
exports.connectIntegration = connectIntegration;
exports.instrumentConnect = instrumentConnect;
exports.setupConnectErrorHandler = setupConnectErrorHandler; //# sourceMappingURL=connect.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/spotlight.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const http = __turbopack_context__.r("[externals]/node:http [external] (node:http, cjs)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Spotlight';
const _spotlightIntegration = (options = {})=>{
    const _options = {
        sidecarUrl: options.sidecarUrl || 'http://localhost:8969/stream'
    };
    return {
        name: INTEGRATION_NAME,
        setup (client) {
            if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
            ;
            connectToSpotlight(client, _options);
        }
    };
};
/**
 * Use this integration to send errors and transactions to Spotlight.
 *
 * Learn more about spotlight at https://spotlightjs.com
 *
 * Important: This integration only works with Node 18 or newer.
 */ const spotlightIntegration = core.defineIntegration(_spotlightIntegration);
function connectToSpotlight(client, options) {
    const spotlightUrl = parseSidecarUrl(options.sidecarUrl);
    if (!spotlightUrl) {
        return;
    }
    let failedRequests = 0;
    client.on('beforeEnvelope', (envelope)=>{
        if (failedRequests > 3) {
            core.logger.warn('[Spotlight] Disabled Sentry -> Spotlight integration due to too many failed requests');
            return;
        }
        const serializedEnvelope = core.serializeEnvelope(envelope);
        const request = getNativeHttpRequest();
        const req = request({
            method: 'POST',
            path: spotlightUrl.pathname,
            hostname: spotlightUrl.hostname,
            port: spotlightUrl.port,
            headers: {
                'Content-Type': 'application/x-sentry-envelope'
            }
        }, (res)=>{
            if (res.statusCode && res.statusCode >= 200 && res.statusCode < 400) {
                // Reset failed requests counter on success
                failedRequests = 0;
            }
            res.on('data', ()=>{
            // Drain socket
            });
            res.on('end', ()=>{
            // Drain socket
            });
            res.setEncoding('utf8');
        });
        req.on('error', ()=>{
            failedRequests++;
            core.logger.warn('[Spotlight] Failed to send envelope to Spotlight Sidecar');
        });
        req.write(serializedEnvelope);
        req.end();
    });
}
function parseSidecarUrl(url) {
    try {
        return new URL(`${url}`);
    } catch (e) {
        core.logger.warn(`[Spotlight] Invalid sidecar URL: ${url}`);
        return undefined;
    }
}
/**
 * We want to get an unpatched http request implementation to avoid capturing our own calls.
 */ function getNativeHttpRequest() {
    const { request } = http;
    if (isWrapped(request)) {
        return request.__sentry_original__;
    }
    return request;
}
function isWrapped(impl) {
    return '__sentry_original__' in impl;
}
exports.INTEGRATION_NAME = INTEGRATION_NAME;
exports.getNativeHttpRequest = getNativeHttpRequest;
exports.spotlightIntegration = spotlightIntegration; //# sourceMappingURL=spotlight.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/knex.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationKnex = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-knex@0.43.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-knex/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Knex';
const instrumentKnex = instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>new instrumentationKnex.KnexInstrumentation({
        requireParentSpan: true
    }));
const _knexIntegration = ()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentKnex();
        },
        setup (client) {
            client.on('spanStart', (span)=>{
                const { data } = core.spanToJSON(span);
                // knex.version is always set in the span data
                // https://github.com/open-telemetry/opentelemetry-js-contrib/blob/0309caeafc44ac9cb13a3345b790b01b76d0497d/plugins/node/opentelemetry-instrumentation-knex/src/instrumentation.ts#L138
                if (data && 'knex.version' in data) {
                    span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, 'auto.db.otel.knex');
                }
            });
        }
    };
};
/**
 * Knex integration
 *
 * Capture tracing data for [Knex](https://knexjs.org/).
 *
 * @example
 * ```javascript
 * import * as Sentry from '@sentry/node';
 *
 * Sentry.init({
 *  integrations: [Sentry.knexIntegration()],
 * });
 * ```
 */ const knexIntegration = core.defineIntegration(_knexIntegration);
exports.instrumentKnex = instrumentKnex;
exports.knexIntegration = knexIntegration; //# sourceMappingURL=knex.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/tedious.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationTedious = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-tedious@0.17.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-tedious/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const TEDIUS_INSTRUMENTED_METHODS = new Set([
    'callProcedure',
    'execSql',
    'execSqlBatch',
    'execBulkLoad',
    'prepare',
    'execute'
]);
const INTEGRATION_NAME = 'Tedious';
const instrumentTedious = instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>new instrumentationTedious.TediousInstrumentation({}));
const _tediousIntegration = ()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentTedious();
        },
        setup (client) {
            client.on('spanStart', (span)=>{
                const { description, data } = core.spanToJSON(span);
                // Tedius integration always set a span name and `db.system` attribute to `mssql`.
                if (!description || _optionalChain([
                    data,
                    'optionalAccess',
                    (_)=>_['db.system']
                ]) !== 'mssql') {
                    return;
                }
                const operation = _optionalChain([
                    description,
                    'optionalAccess',
                    (_2)=>_2.split,
                    'call',
                    (_3)=>_3(' '),
                    'access',
                    (_4)=>_4[0]
                ]) || '';
                if (TEDIUS_INSTRUMENTED_METHODS.has(operation)) {
                    span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, 'auto.db.otel.tedious');
                }
            });
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for the [tedious](https://www.npmjs.com/package/tedious) library.
 *
 * For more information, see the [`tediousIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/tedious/).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *  integrations: [Sentry.tediousIntegration()],
 * });
 * ```
 */ const tediousIntegration = core.defineIntegration(_tediousIntegration);
exports.instrumentTedious = instrumentTedious;
exports.tediousIntegration = tediousIntegration; //# sourceMappingURL=tedious.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/genericPool.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationGenericPool = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-generic-pool@0.42.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-generic-pool/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'GenericPool';
const instrumentGenericPool = instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>new instrumentationGenericPool.GenericPoolInstrumentation({}));
const _genericPoolIntegration = ()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentGenericPool();
        },
        setup (client) {
            client.on('spanStart', (span)=>{
                const spanJSON = core.spanToJSON(span);
                const spanDescription = spanJSON.description;
                // typo in emitted span for version <= 0.38.0 of @opentelemetry/instrumentation-generic-pool
                const isGenericPoolSpan = spanDescription === 'generic-pool.aquire' || spanDescription === 'generic-pool.acquire';
                if (isGenericPoolSpan) {
                    span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, 'auto.db.otel.generic_pool');
                }
            });
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for the [generic-pool](https://www.npmjs.com/package/generic-pool) library.
 *
 * For more information, see the [`genericPoolIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/genericpool/).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *  integrations: [Sentry.genericPoolIntegration()],
 * });
 * ```
 */ const genericPoolIntegration = core.defineIntegration(_genericPoolIntegration);
exports.genericPoolIntegration = genericPoolIntegration;
exports.instrumentGenericPool = instrumentGenericPool; //# sourceMappingURL=genericPool.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/dataloader.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationDataloader = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-dataloader@0.15.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-dataloader/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Dataloader';
const instrumentDataloader = instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>new instrumentationDataloader.DataloaderInstrumentation({
        requireParentSpan: true
    }));
const _dataloaderIntegration = ()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentDataloader();
        },
        setup (client) {
            client.on('spanStart', (span)=>{
                const spanJSON = core.spanToJSON(span);
                if (_optionalChain([
                    spanJSON,
                    'access',
                    (_)=>_.description,
                    'optionalAccess',
                    (_2)=>_2.startsWith,
                    'call',
                    (_3)=>_3('dataloader')
                ])) {
                    span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, 'auto.db.otel.dataloader');
                }
                // These are all possible dataloader span descriptions
                // Still checking for the future versions
                // in case they add support for `clear` and `prime`
                if (spanJSON.description === 'dataloader.load' || spanJSON.description === 'dataloader.loadMany' || spanJSON.description === 'dataloader.batch') {
                    span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_OP, 'cache.get');
                // TODO: We can try adding `key` to the `data` attribute upstream.
                // Or alternatively, we can add `requestHook` to the dataloader instrumentation.
                }
            });
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for the [dataloader](https://www.npmjs.com/package/dataloader) library.
 *
 * For more information, see the [`dataloaderIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/dataloader/).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *  integrations: [Sentry.dataloaderIntegration()],
 * });
 * ```
 */ const dataloaderIntegration = core.defineIntegration(_dataloaderIntegration);
exports.dataloaderIntegration = dataloaderIntegration;
exports.instrumentDataloader = instrumentDataloader; //# sourceMappingURL=dataloader.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/amqplib.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentationAmqplib = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation-amqplib@0.45.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation-amqplib/build/src/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const addOriginToSpan = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/addOriginToSpan.js [instrumentation] (ecmascript)");
const INTEGRATION_NAME = 'Amqplib';
const config = {
    consumeEndHook: (span)=>{
        addOriginToSpan.addOriginToSpan(span, 'auto.amqplib.otel.consumer');
    },
    publishHook: (span)=>{
        addOriginToSpan.addOriginToSpan(span, 'auto.amqplib.otel.publisher');
    }
};
const instrumentAmqplib = instrument.generateInstrumentOnce(INTEGRATION_NAME, ()=>new instrumentationAmqplib.AmqplibInstrumentation(config));
const _amqplibIntegration = ()=>{
    return {
        name: INTEGRATION_NAME,
        setupOnce () {
            instrumentAmqplib();
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for the [amqplib](https://www.npmjs.com/package/amqplib) library.
 *
 * For more information, see the [`amqplibIntegration` documentation](https://docs.sentry.io/platforms/javascript/guides/node/configuration/integrations/amqplib/).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *  integrations: [Sentry.amqplibIntegration()],
 * });
 * ```
 */ const amqplibIntegration = core.defineIntegration(_amqplibIntegration);
exports.amqplibIntegration = amqplibIntegration;
exports.instrumentAmqplib = instrumentAmqplib; //# sourceMappingURL=amqplib.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/childProcess.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const diagnosticsChannel = __turbopack_context__.r("[externals]/node:diagnostics_channel [external] (node:diagnostics_channel, cjs)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
// TODO(v9): Update this name and mention in migration docs.
const INTEGRATION_NAME = 'ProcessAndThreadBreadcrumbs';
/**
 * Capture breadcrumbs for child processes and worker threads.
 */ const childProcessIntegration = core.defineIntegration((options = {})=>{
    return {
        name: INTEGRATION_NAME,
        setup (_client) {
            // eslint-disable-next-line deprecation/deprecation
            diagnosticsChannel.channel('child_process').subscribe((event)=>{
                if (event && typeof event === 'object' && 'process' in event) {
                    captureChildProcessEvents(event.process, options);
                }
            });
            // eslint-disable-next-line deprecation/deprecation
            diagnosticsChannel.channel('worker_threads').subscribe((event)=>{
                if (event && typeof event === 'object' && 'worker' in event) {
                    captureWorkerThreadEvents(event.worker);
                }
            });
        }
    };
});
/**
 * Capture breadcrumbs for child processes and worker threads.
 *
 * @deprecated Use `childProcessIntegration` integration instead. Functionally they are the same. `processThreadBreadcrumbIntegration` will be removed in the next major version.
 */ const processThreadBreadcrumbIntegration = childProcessIntegration;
function captureChildProcessEvents(child, options) {
    let hasExited = false;
    let data;
    child.on('spawn', ()=>{
        // This is Sentry getting macOS OS context
        if (child.spawnfile === '/usr/bin/sw_vers') {
            hasExited = true;
            return;
        }
        data = {
            spawnfile: child.spawnfile
        };
        if (options.includeChildProcessArgs) {
            data.spawnargs = child.spawnargs;
        }
    }).on('exit', (code)=>{
        if (!hasExited) {
            hasExited = true;
            // Only log for non-zero exit codes
            if (code !== null && code !== 0) {
                core.addBreadcrumb({
                    category: 'child_process',
                    message: `Child process exited with code '${code}'`,
                    level: 'warning',
                    data
                });
            }
        }
    }).on('error', (error)=>{
        if (!hasExited) {
            hasExited = true;
            core.addBreadcrumb({
                category: 'child_process',
                message: `Child process errored with '${error.message}'`,
                level: 'error',
                data
            });
        }
    });
}
function captureWorkerThreadEvents(worker) {
    let threadId;
    worker.on('online', ()=>{
        threadId = worker.threadId;
    }).on('error', (error)=>{
        core.addBreadcrumb({
            category: 'worker_thread',
            message: `Worker thread errored with '${error.message}'`,
            level: 'error',
            data: {
                threadId
            }
        });
    });
}
exports.childProcessIntegration = childProcessIntegration;
exports.processThreadBreadcrumbIntegration = processThreadBreadcrumbIntegration; //# sourceMappingURL=childProcess.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/contextManager.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const contextAsyncHooks = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+context-async-hooks@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/context-async-hooks/build/src/index.js [instrumentation] (ecmascript)");
const opentelemetry = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+opentelemetry@8.46.0_@opentelemetry+api@1.9.0_@opentelemetry+core@1.30.1_@opent_9f3efdcc19a3583a187ce7ebb26bfca9/node_modules/@sentry/opentelemetry/build/cjs/index.js [instrumentation] (ecmascript)");
/**
 * This is a custom ContextManager for OpenTelemetry, which extends the default AsyncLocalStorageContextManager.
 * It ensures that we create a new hub per context, so that the OTEL Context & the Sentry Hub are always in sync.
 *
 * Note that we currently only support AsyncHooks with this,
 * but since this should work for Node 14+ anyhow that should be good enough.
 */ const SentryContextManager = opentelemetry.wrapContextManagerClass(contextAsyncHooks.AsyncLocalStorageContextManager);
exports.SentryContextManager = SentryContextManager; //# sourceMappingURL=contextManager.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/vercelai/instrumentation.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const instrumentation = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation@0.56.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation/build/esm/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
// List of patched methods
// From: https://sdk.vercel.ai/docs/ai-sdk-core/telemetry#collected-data
const INSTRUMENTED_METHODS = [
    'generateText',
    'streamText',
    'generateObject',
    'streamObject',
    'embed',
    'embedMany'
];
exports.sentryVercelAiPatched = false;
/**
 * This detects is added by the Sentry Vercel AI Integration to detect if the integration should
 * be enabled.
 *
 * It also patches the `ai` module to enable Vercel AI telemetry automatically for all methods.
 */ class SentryVercelAiInstrumentation extends instrumentation.InstrumentationBase {
    constructor(config = {}){
        super('@sentry/instrumentation-vercel-ai', core.SDK_VERSION, config);
    }
    /**
   * Initializes the instrumentation by defining the modules to be patched.
   */ init() {
        const module = new instrumentation.InstrumentationNodeModuleDefinition('ai', [
            '>=3.0.0 <5'
        ], this._patch.bind(this));
        return module;
    }
    /**
   * Patches module exports to enable Vercel AI telemetry.
   */ _patch(moduleExports) {
        exports.sentryVercelAiPatched = true;
        function generatePatch(name) {
            return (...args)=>{
                const existingExperimentalTelemetry = args[0].experimental_telemetry || {};
                const isEnabled = existingExperimentalTelemetry.isEnabled;
                // if `isEnabled` is not explicitly set to `true` or `false`, enable telemetry
                // but disable capturing inputs and outputs by default
                if (isEnabled === undefined) {
                    args[0].experimental_telemetry = {
                        isEnabled: true,
                        recordInputs: false,
                        recordOutputs: false,
                        ...existingExperimentalTelemetry
                    };
                }
                // @ts-expect-error we know that the method exists
                return moduleExports[name].apply(this, args);
            };
        }
        const patchedModuleExports = INSTRUMENTED_METHODS.reduce((acc, curr)=>{
            acc[curr] = generatePatch(curr);
            return acc;
        }, {});
        return {
            ...moduleExports,
            ...patchedModuleExports
        };
    }
}
exports.SentryVercelAiInstrumentation = SentryVercelAiInstrumentation; //# sourceMappingURL=instrumentation.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/vercelai/index.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const addOriginToSpan = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/addOriginToSpan.js [instrumentation] (ecmascript)");
const instrumentation = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/vercelai/instrumentation.js [instrumentation] (ecmascript)");
/* eslint-disable complexity */ const instrumentVercelAi = instrument.generateInstrumentOnce('vercelAI', ()=>new instrumentation.SentryVercelAiInstrumentation({}));
const _vercelAIIntegration = ()=>{
    return {
        name: 'vercelAI',
        setupOnce () {
            instrumentVercelAi();
        },
        processEvent (event) {
            if (event.type === 'transaction' && _optionalChain([
                event,
                'access',
                (_)=>_.spans,
                'optionalAccess',
                (_2)=>_2.length
            ])) {
                for (const span of event.spans){
                    const { data: attributes, description: name } = span;
                    if (!attributes || !name || span.origin !== 'auto.vercelai.otel') {
                        continue;
                    }
                    // attributes around token usage can only be set on span finish
                    span.data = span.data || {};
                    if (attributes['ai.usage.completionTokens'] != undefined) {
                        span.data['ai.completion_tokens.used'] = attributes['ai.usage.completionTokens'];
                    }
                    if (attributes['ai.usage.promptTokens'] != undefined) {
                        span.data['ai.prompt_tokens.used'] = attributes['ai.usage.promptTokens'];
                    }
                    if (attributes['ai.usage.completionTokens'] != undefined && attributes['ai.usage.promptTokens'] != undefined) {
                        span.data['ai.total_tokens.used'] = attributes['ai.usage.completionTokens'] + attributes['ai.usage.promptTokens'];
                    }
                }
            }
            return event;
        },
        setup (client) {
            client.on('spanStart', (span)=>{
                if (!instrumentation.sentryVercelAiPatched) {
                    return;
                }
                const { data: attributes, description: name } = core.spanToJSON(span);
                if (!attributes || !name) {
                    return;
                }
                // The id of the model
                const aiModelId = attributes['ai.model.id'];
                // the provider of the model
                const aiModelProvider = attributes['ai.model.provider'];
                // both of these must be defined for the integration to work
                if (!aiModelId || !aiModelProvider) {
                    return;
                }
                let isPipelineSpan = false;
                switch(name){
                    case 'ai.generateText':
                        {
                            span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_OP, 'ai.pipeline.generateText');
                            isPipelineSpan = true;
                            break;
                        }
                    case 'ai.generateText.doGenerate':
                        {
                            span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_OP, 'ai.run.doGenerate');
                            break;
                        }
                    case 'ai.streamText':
                        {
                            span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_OP, 'ai.pipeline.streamText');
                            isPipelineSpan = true;
                            break;
                        }
                    case 'ai.streamText.doStream':
                        {
                            span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_OP, 'ai.run.doStream');
                            break;
                        }
                    case 'ai.generateObject':
                        {
                            span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_OP, 'ai.pipeline.generateObject');
                            isPipelineSpan = true;
                            break;
                        }
                    case 'ai.generateObject.doGenerate':
                        {
                            span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_OP, 'ai.run.doGenerate');
                            break;
                        }
                    case 'ai.streamObject':
                        {
                            span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_OP, 'ai.pipeline.streamObject');
                            isPipelineSpan = true;
                            break;
                        }
                    case 'ai.streamObject.doStream':
                        {
                            span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_OP, 'ai.run.doStream');
                            break;
                        }
                    case 'ai.embed':
                        {
                            span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_OP, 'ai.pipeline.embed');
                            isPipelineSpan = true;
                            break;
                        }
                    case 'ai.embed.doEmbed':
                        {
                            span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_OP, 'ai.embeddings');
                            break;
                        }
                    case 'ai.embedMany':
                        {
                            span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_OP, 'ai.pipeline.embedMany');
                            isPipelineSpan = true;
                            break;
                        }
                    case 'ai.embedMany.doEmbed':
                        {
                            span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_OP, 'ai.embeddings');
                            break;
                        }
                    case 'ai.toolCall':
                    case 'ai.stream.firstChunk':
                    case 'ai.stream.finish':
                        span.setAttribute(core.SEMANTIC_ATTRIBUTE_SENTRY_OP, 'ai.run');
                        break;
                }
                addOriginToSpan.addOriginToSpan(span, 'auto.vercelai.otel');
                const nameWthoutAi = name.replace('ai.', '');
                span.setAttribute('ai.pipeline.name', nameWthoutAi);
                span.updateName(nameWthoutAi);
                // If a Telemetry name is set and it is a pipeline span, use that as the operation name
                if (attributes['ai.telemetry.functionId'] && isPipelineSpan) {
                    span.updateName(attributes['ai.telemetry.functionId']);
                    span.setAttribute('ai.pipeline.name', attributes['ai.telemetry.functionId']);
                }
                if (attributes['ai.prompt']) {
                    span.setAttribute('ai.input_messages', attributes['ai.prompt']);
                }
                if (attributes['ai.model.id']) {
                    span.setAttribute('ai.model_id', attributes['ai.model.id']);
                }
                span.setAttribute('ai.streaming', name.includes('stream'));
            });
        }
    };
};
/**
 * Adds Sentry tracing instrumentation for the [ai](https://www.npmjs.com/package/ai) library.
 *
 * For more information, see the [`ai` documentation](https://sdk.vercel.ai/docs/ai-sdk-core/telemetry).
 *
 * @example
 * ```javascript
 * const Sentry = require('@sentry/node');
 *
 * Sentry.init({
 *  integrations: [Sentry.vercelAIIntegration()],
 * });
 * ```
 *
 * By default this integration adds tracing support to all `ai` function calls. If you need to disable
 * collecting spans for a specific call, you can do so by setting `experimental_telemetry.isEnabled` to
 * `false` in the first argument of the function call.
 *
 * ```javascript
 * const result = await generateText({
 *   model: openai('gpt-4-turbo'),
 *   experimental_telemetry: { isEnabled: false },
 * });
 * ```
 *
 * If you want to collect inputs and outputs for a specific call, you must specifically opt-in to each
 * function call by setting `experimental_telemetry.recordInputs` and `experimental_telemetry.recordOutputs`
 * to `true`.
 *
 * ```javascript
 * const result = await generateText({
 *  model: openai('gpt-4-turbo'),
 *  experimental_telemetry: { isEnabled: true, recordInputs: true, recordOutputs: true },
 * });
 */ const vercelAIIntegration = core.defineIntegration(_vercelAIIntegration);
exports.instrumentVercelAi = instrumentVercelAi;
exports.vercelAIIntegration = vercelAIIntegration; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/index.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const index$2 = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/http/index.js [instrumentation] (ecmascript)");
const amqplib = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/amqplib.js [instrumentation] (ecmascript)");
const connect = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/connect.js [instrumentation] (ecmascript)");
const express = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/express.js [instrumentation] (ecmascript)");
const fastify = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/fastify.js [instrumentation] (ecmascript)");
const genericPool = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/genericPool.js [instrumentation] (ecmascript)");
const graphql = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/graphql.js [instrumentation] (ecmascript)");
const index = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/hapi/index.js [instrumentation] (ecmascript)");
const kafka = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/kafka.js [instrumentation] (ecmascript)");
const koa = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/koa.js [instrumentation] (ecmascript)");
const lrumemoizer = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/lrumemoizer.js [instrumentation] (ecmascript)");
const mongo = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/mongo.js [instrumentation] (ecmascript)");
const mongoose = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/mongoose.js [instrumentation] (ecmascript)");
const mysql = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/mysql.js [instrumentation] (ecmascript)");
const mysql2 = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/mysql2.js [instrumentation] (ecmascript)");
const nest = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/nest/nest.js [instrumentation] (ecmascript)");
const postgres = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/postgres.js [instrumentation] (ecmascript)");
const redis = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/redis.js [instrumentation] (ecmascript)");
const tedious = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/tedious.js [instrumentation] (ecmascript)");
const index$1 = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/vercelai/index.js [instrumentation] (ecmascript)");
/**
 * With OTEL, all performance integrations will be added, as OTEL only initializes them when the patched package is actually required.
 */ function getAutoPerformanceIntegrations() {
    return [
        express.expressIntegration(),
        fastify.fastifyIntegration(),
        graphql.graphqlIntegration(),
        mongo.mongoIntegration(),
        mongoose.mongooseIntegration(),
        mysql.mysqlIntegration(),
        mysql2.mysql2Integration(),
        redis.redisIntegration(),
        postgres.postgresIntegration(),
        // For now, we do not include prisma by default because it has ESM issues
        // See https://github.com/prisma/prisma/issues/23410
        // TODO v8: Figure out a better solution for this, maybe only disable in ESM mode?
        // prismaIntegration(),
        // eslint-disable-next-line deprecation/deprecation
        nest.nestIntegration(),
        index.hapiIntegration(),
        koa.koaIntegration(),
        connect.connectIntegration(),
        tedious.tediousIntegration(),
        genericPool.genericPoolIntegration(),
        kafka.kafkaIntegration(),
        amqplib.amqplibIntegration(),
        lrumemoizer.lruMemoizerIntegration(),
        index$1.vercelAIIntegration()
    ];
}
/**
 * Get a list of methods to instrument OTEL, when preload instrumentation.
 */ // eslint-disable-next-line @typescript-eslint/no-explicit-any
function getOpenTelemetryInstrumentationToPreload() {
    return [
        index$2.instrumentOtelHttp,
        express.instrumentExpress,
        connect.instrumentConnect,
        fastify.instrumentFastify,
        index.instrumentHapi,
        kafka.instrumentKafka,
        koa.instrumentKoa,
        lrumemoizer.instrumentLruMemoizer,
        // eslint-disable-next-line deprecation/deprecation
        nest.instrumentNest,
        mongo.instrumentMongo,
        mongoose.instrumentMongoose,
        mysql.instrumentMysql,
        mysql2.instrumentMysql2,
        postgres.instrumentPostgres,
        index.instrumentHapi,
        graphql.instrumentGraphql,
        redis.instrumentRedis,
        tedious.instrumentTedious,
        genericPool.instrumentGenericPool,
        amqplib.instrumentAmqplib,
        index$1.instrumentVercelAi
    ];
}
exports.getAutoPerformanceIntegrations = getAutoPerformanceIntegrations;
exports.getOpenTelemetryInstrumentationToPreload = getOpenTelemetryInstrumentationToPreload; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/proxy/base.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _nullishCoalesce } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const http = __turbopack_context__.r("[externals]/node:http [external] (node:http, cjs)");
__turbopack_context__.r("[externals]/node:https [external] (node:https, cjs)");
/**
* This code was originally forked from https://github.com/TooTallNate/proxy-agents/tree/b133295fd16f6475578b6b15bd9b4e33ecb0d0b7
* With the following LICENSE:
*
* (The MIT License)
*
* Copyright (c) 2013 Nathan Rajlich <<EMAIL>>*
*
* Permission is hereby granted, free of charge, to any person obtaining
* a copy of this software and associated documentation files (the
* 'Software'), to deal in the Software without restriction, including
* without limitation the rights to use, copy, modify, merge, publish,
* distribute, sublicense, and/or sell copies of the Software, and to
* permit persons to whom the Software is furnished to do so, subject to
* the following conditions:*
*
* The above copyright notice and this permission notice shall be
* included in all copies or substantial portions of the Software.*
*
* THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,
* EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
* MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
* IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
* CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
* TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
* SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/ const INTERNAL = Symbol('AgentBaseInternalState');
class Agent extends http.Agent {
    // Set by `http.Agent` - missing from `@types/node`
    constructor(opts){
        super(opts);
        this[INTERNAL] = {};
    }
    /**
   * Determine whether this is an `http` or `https` request.
   */ isSecureEndpoint(options) {
        if (options) {
            // First check the `secureEndpoint` property explicitly, since this
            // means that a parent `Agent` is "passing through" to this instance.
            if (typeof options.secureEndpoint === 'boolean') {
                return options.secureEndpoint;
            }
            // If no explicit `secure` endpoint, check if `protocol` property is
            // set. This will usually be the case since using a full string URL
            // or `URL` instance should be the most common usage.
            if (typeof options.protocol === 'string') {
                return options.protocol === 'https:';
            }
        }
        // Finally, if no `protocol` property was set, then fall back to
        // checking the stack trace of the current call stack, and try to
        // detect the "https" module.
        const { stack } = new Error();
        if (typeof stack !== 'string') return false;
        return stack.split('\n').some((l)=>l.indexOf('(https.js:') !== -1 || l.indexOf('node:https:') !== -1);
    }
    createSocket(req, options, cb) {
        const connectOpts = {
            ...options,
            secureEndpoint: this.isSecureEndpoint(options)
        };
        Promise.resolve().then(()=>this.connect(req, connectOpts)).then((socket)=>{
            if (socket instanceof http.Agent) {
                // @ts-expect-error `addRequest()` isn't defined in `@types/node`
                return socket.addRequest(req, connectOpts);
            }
            this[INTERNAL].currentSocket = socket;
            // @ts-expect-error `createSocket()` isn't defined in `@types/node`
            super.createSocket(req, options, cb);
        }, cb);
    }
    createConnection() {
        const socket = this[INTERNAL].currentSocket;
        this[INTERNAL].currentSocket = undefined;
        if (!socket) {
            throw new Error('No socket was returned in the `connect()` function');
        }
        return socket;
    }
    get defaultPort() {
        return _nullishCoalesce(this[INTERNAL].defaultPort, ()=>this.protocol === 'https:' ? 443 : 80);
    }
    set defaultPort(v) {
        if (this[INTERNAL]) {
            this[INTERNAL].defaultPort = v;
        }
    }
    get protocol() {
        return _nullishCoalesce(this[INTERNAL].protocol, ()=>this.isSecureEndpoint() ? 'https:' : 'http:');
    }
    set protocol(v) {
        if (this[INTERNAL]) {
            this[INTERNAL].protocol = v;
        }
    }
}
exports.Agent = Agent; //# sourceMappingURL=base.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/proxy/parse-proxy-response.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
function debug(...args) {
    core.logger.log('[https-proxy-agent:parse-proxy-response]', ...args);
}
function parseProxyResponse(socket) {
    return new Promise((resolve, reject)=>{
        // we need to buffer any HTTP traffic that happens with the proxy before we get
        // the CONNECT response, so that if the response is anything other than an "200"
        // response code, then we can re-play the "data" events on the socket once the
        // HTTP parser is hooked up...
        let buffersLength = 0;
        const buffers = [];
        function read() {
            const b = socket.read();
            if (b) ondata(b);
            else socket.once('readable', read);
        }
        function cleanup() {
            socket.removeListener('end', onend);
            socket.removeListener('error', onerror);
            socket.removeListener('readable', read);
        }
        function onend() {
            cleanup();
            debug('onend');
            reject(new Error('Proxy connection ended before receiving CONNECT response'));
        }
        function onerror(err) {
            cleanup();
            debug('onerror %o', err);
            reject(err);
        }
        function ondata(b) {
            buffers.push(b);
            buffersLength += b.length;
            const buffered = Buffer.concat(buffers, buffersLength);
            const endOfHeaders = buffered.indexOf('\r\n\r\n');
            if (endOfHeaders === -1) {
                // keep buffering
                debug('have not received end of HTTP headers yet...');
                read();
                return;
            }
            const headerParts = buffered.slice(0, endOfHeaders).toString('ascii').split('\r\n');
            const firstLine = headerParts.shift();
            if (!firstLine) {
                socket.destroy();
                return reject(new Error('No header received from proxy CONNECT response'));
            }
            const firstLineParts = firstLine.split(' ');
            const statusCode = +(firstLineParts[1] || 0);
            const statusText = firstLineParts.slice(2).join(' ');
            const headers = {};
            for (const header of headerParts){
                if (!header) continue;
                const firstColon = header.indexOf(':');
                if (firstColon === -1) {
                    socket.destroy();
                    return reject(new Error(`Invalid header from proxy CONNECT response: "${header}"`));
                }
                const key = header.slice(0, firstColon).toLowerCase();
                const value = header.slice(firstColon + 1).trimStart();
                const current = headers[key];
                if (typeof current === 'string') {
                    headers[key] = [
                        current,
                        value
                    ];
                } else if (Array.isArray(current)) {
                    current.push(value);
                } else {
                    headers[key] = value;
                }
            }
            debug('got proxy server response: %o %o', firstLine, headers);
            cleanup();
            resolve({
                connect: {
                    statusCode,
                    statusText,
                    headers
                },
                buffered
            });
        }
        socket.on('error', onerror);
        socket.on('end', onend);
        read();
    });
}
exports.parseProxyResponse = parseProxyResponse; //# sourceMappingURL=parse-proxy-response.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/proxy/index.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _nullishCoalesce, _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const net = __turbopack_context__.r("[externals]/node:net [external] (node:net, cjs)");
const tls = __turbopack_context__.r("[externals]/node:tls [external] (node:tls, cjs)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const base = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/proxy/base.js [instrumentation] (ecmascript)");
const parseProxyResponse = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/proxy/parse-proxy-response.js [instrumentation] (ecmascript)");
function debug(...args) {
    core.logger.log('[https-proxy-agent]', ...args);
}
/**
 * The `HttpsProxyAgent` implements an HTTP Agent subclass that connects to
 * the specified "HTTP(s) proxy server" in order to proxy HTTPS requests.
 *
 * Outgoing HTTP requests are first tunneled through the proxy server using the
 * `CONNECT` HTTP request method to establish a connection to the proxy server,
 * and then the proxy server connects to the destination target and issues the
 * HTTP request from the proxy server.
 *
 * `https:` requests have their socket connection upgraded to TLS once
 * the connection to the proxy server has been established.
 */ class HttpsProxyAgent extends base.Agent {
    static __initStatic() {
        this.protocols = [
            'http',
            'https'
        ];
    }
    constructor(proxy, opts){
        super(opts);
        this.options = {};
        this.proxy = typeof proxy === 'string' ? new URL(proxy) : proxy;
        this.proxyHeaders = _nullishCoalesce(_optionalChain([
            opts,
            'optionalAccess',
            (_2)=>_2.headers
        ]), ()=>({}));
        debug('Creating new HttpsProxyAgent instance: %o', this.proxy.href);
        // Trim off the brackets from IPv6 addresses
        const host = (this.proxy.hostname || this.proxy.host).replace(/^\[|\]$/g, '');
        const port = this.proxy.port ? parseInt(this.proxy.port, 10) : this.proxy.protocol === 'https:' ? 443 : 80;
        this.connectOpts = {
            // Attempt to negotiate http/1.1 for proxy servers that support http/2
            ALPNProtocols: [
                'http/1.1'
            ],
            ...opts ? omit(opts, 'headers') : null,
            host,
            port
        };
    }
    /**
   * Called when the node-core HTTP client library is creating a
   * new HTTP request.
   */ async connect(req, opts) {
        const { proxy } = this;
        if (!opts.host) {
            throw new TypeError('No "host" provided');
        }
        // Create a socket connection to the proxy server.
        let socket;
        if (proxy.protocol === 'https:') {
            debug('Creating `tls.Socket`: %o', this.connectOpts);
            const servername = this.connectOpts.servername || this.connectOpts.host;
            socket = tls.connect({
                ...this.connectOpts,
                servername: servername && net.isIP(servername) ? undefined : servername
            });
        } else {
            debug('Creating `net.Socket`: %o', this.connectOpts);
            socket = net.connect(this.connectOpts);
        }
        const headers = typeof this.proxyHeaders === 'function' ? this.proxyHeaders() : {
            ...this.proxyHeaders
        };
        const host = net.isIPv6(opts.host) ? `[${opts.host}]` : opts.host;
        let payload = `CONNECT ${host}:${opts.port} HTTP/1.1\r\n`;
        // Inject the `Proxy-Authorization` header if necessary.
        if (proxy.username || proxy.password) {
            const auth = `${decodeURIComponent(proxy.username)}:${decodeURIComponent(proxy.password)}`;
            headers['Proxy-Authorization'] = `Basic ${Buffer.from(auth).toString('base64')}`;
        }
        headers.Host = `${host}:${opts.port}`;
        if (!headers['Proxy-Connection']) {
            headers['Proxy-Connection'] = this.keepAlive ? 'Keep-Alive' : 'close';
        }
        for (const name of Object.keys(headers)){
            payload += `${name}: ${headers[name]}\r\n`;
        }
        const proxyResponsePromise = parseProxyResponse.parseProxyResponse(socket);
        socket.write(`${payload}\r\n`);
        const { connect, buffered } = await proxyResponsePromise;
        req.emit('proxyConnect', connect);
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore Not EventEmitter in Node types
        this.emit('proxyConnect', connect, req);
        if (connect.statusCode === 200) {
            req.once('socket', resume);
            if (opts.secureEndpoint) {
                // The proxy is connecting to a TLS server, so upgrade
                // this socket connection to a TLS connection.
                debug('Upgrading socket connection to TLS');
                const servername = opts.servername || opts.host;
                return tls.connect({
                    ...omit(opts, 'host', 'path', 'port'),
                    socket,
                    servername: net.isIP(servername) ? undefined : servername
                });
            }
            return socket;
        }
        // Some other status code that's not 200... need to re-play the HTTP
        // header "data" events onto the socket once the HTTP machinery is
        // attached so that the node core `http` can parse and handle the
        // error status code.
        // Close the original socket, and a new "fake" socket is returned
        // instead, so that the proxy doesn't get the HTTP request
        // written to it (which may contain `Authorization` headers or other
        // sensitive data).
        //
        // See: https://hackerone.com/reports/541502
        socket.destroy();
        const fakeSocket = new net.Socket({
            writable: false
        });
        fakeSocket.readable = true;
        // Need to wait for the "socket" event to re-play the "data" events.
        req.once('socket', (s)=>{
            debug('Replaying proxy buffer for failed request');
            // Replay the "buffered" Buffer onto the fake `socket`, since at
            // this point the HTTP module machinery has been hooked up for
            // the user.
            s.push(buffered);
            s.push(null);
        });
        return fakeSocket;
    }
}
HttpsProxyAgent.__initStatic();
function resume(socket) {
    socket.resume();
}
function omit(obj, ...keys) {
    const ret = {};
    let key;
    for(key in obj){
        if (!keys.includes(key)) {
            ret[key] = obj[key];
        }
    }
    return ret;
}
exports.HttpsProxyAgent = HttpsProxyAgent; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/transports/http.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _nullishCoalesce } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const http = __turbopack_context__.r("[externals]/node:http [external] (node:http, cjs)");
const https = __turbopack_context__.r("[externals]/node:https [external] (node:https, cjs)");
const node_stream = __turbopack_context__.r("[externals]/node:stream [external] (node:stream, cjs)");
const node_zlib = __turbopack_context__.r("[externals]/node:zlib [external] (node:zlib, cjs)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const index = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/proxy/index.js [instrumentation] (ecmascript)");
// Estimated maximum size for reasonable standalone event
const GZIP_THRESHOLD = 1024 * 32;
/**
 * Gets a stream from a Uint8Array or string
 * Readable.from is ideal but was added in node.js v12.3.0 and v10.17.0
 */ function streamFromBody(body) {
    return new node_stream.Readable({
        read () {
            this.push(body);
            this.push(null);
        }
    });
}
/**
 * Creates a Transport that uses native the native 'http' and 'https' modules to send events to Sentry.
 */ function makeNodeTransport(options) {
    let urlSegments;
    try {
        urlSegments = new URL(options.url);
    } catch (e) {
        core.consoleSandbox(()=>{
            // eslint-disable-next-line no-console
            console.warn('[@sentry/node]: Invalid dsn or tunnel option, will not send any events. The tunnel option must be a full URL when used.');
        });
        return core.createTransport(options, ()=>Promise.resolve({}));
    }
    const isHttps = urlSegments.protocol === 'https:';
    // Proxy prioritization: http => `options.proxy` | `process.env.http_proxy`
    // Proxy prioritization: https => `options.proxy` | `process.env.https_proxy` | `process.env.http_proxy`
    const proxy = applyNoProxyOption(urlSegments, options.proxy || (isHttps ? process.env.https_proxy : undefined) || process.env.http_proxy);
    const nativeHttpModule = isHttps ? https : http;
    const keepAlive = options.keepAlive === undefined ? false : options.keepAlive;
    // TODO(v9): Evaluate if we can set keepAlive to true. This would involve testing for memory leaks in older node
    // versions(>= 8) as they had memory leaks when using it: #2555
    const agent = proxy ? new index.HttpsProxyAgent(proxy) : new nativeHttpModule.Agent({
        keepAlive,
        maxSockets: 30,
        timeout: 2000
    });
    const requestExecutor = createRequestExecutor(options, _nullishCoalesce(options.httpModule, ()=>nativeHttpModule), agent);
    return core.createTransport(options, requestExecutor);
}
/**
 * Honors the `no_proxy` env variable with the highest priority to allow for hosts exclusion.
 *
 * @param transportUrl The URL the transport intends to send events to.
 * @param proxy The client configured proxy.
 * @returns A proxy the transport should use.
 */ function applyNoProxyOption(transportUrlSegments, proxy) {
    const { no_proxy } = process.env;
    const urlIsExemptFromProxy = no_proxy && no_proxy.split(',').some((exemption)=>transportUrlSegments.host.endsWith(exemption) || transportUrlSegments.hostname.endsWith(exemption));
    if (urlIsExemptFromProxy) {
        return undefined;
    } else {
        return proxy;
    }
}
/**
 * Creates a RequestExecutor to be used with `createTransport`.
 */ function createRequestExecutor(options, httpModule, agent) {
    const { hostname, pathname, port, protocol, search } = new URL(options.url);
    return function makeRequest(request) {
        return new Promise((resolve, reject)=>{
            // This ensures we do not generate any spans in OpenTelemetry for the transport
            core.suppressTracing(()=>{
                let body = streamFromBody(request.body);
                const headers = {
                    ...options.headers
                };
                if (request.body.length > GZIP_THRESHOLD) {
                    headers['content-encoding'] = 'gzip';
                    body = body.pipe(node_zlib.createGzip());
                }
                const req = httpModule.request({
                    method: 'POST',
                    agent,
                    headers,
                    hostname,
                    path: `${pathname}${search}`,
                    port,
                    protocol,
                    ca: options.caCerts
                }, (res)=>{
                    res.on('data', ()=>{
                    // Drain socket
                    });
                    res.on('end', ()=>{
                    // Drain socket
                    });
                    res.setEncoding('utf8');
                    // "Key-value pairs of header names and values. Header names are lower-cased."
                    // https://nodejs.org/api/http.html#http_message_headers
                    const retryAfterHeader = _nullishCoalesce(res.headers['retry-after'], ()=>null);
                    const rateLimitsHeader = _nullishCoalesce(res.headers['x-sentry-rate-limits'], ()=>null);
                    resolve({
                        statusCode: res.statusCode,
                        headers: {
                            'retry-after': retryAfterHeader,
                            'x-sentry-rate-limits': Array.isArray(rateLimitsHeader) ? rateLimitsHeader[0] || null : rateLimitsHeader
                        }
                    });
                });
                req.on('error', reject);
                body.pipe(req);
            });
        });
    };
}
exports.makeNodeTransport = makeNodeTransport; //# sourceMappingURL=http.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/envToBool.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const FALSY_ENV_VALUES = new Set([
    'false',
    'f',
    'n',
    'no',
    'off',
    '0'
]);
const TRUTHY_ENV_VALUES = new Set([
    'true',
    't',
    'y',
    'yes',
    'on',
    '1'
]);
/**
 * A helper function which casts an ENV variable value to `true` or `false` using the constants defined above.
 * In strict mode, it may return `null` if the value doesn't match any of the predefined values.
 *
 * @param value The value of the env variable
 * @param options -- Only has `strict` key for now, which requires a strict match for `true` in TRUTHY_ENV_VALUES
 * @returns true/false if the lowercase value matches the predefined values above. If not, null in strict mode,
 *          and Boolean(value) in loose mode.
 */ function envToBool(value, options) {
    const normalized = String(value).toLowerCase();
    if (FALSY_ENV_VALUES.has(normalized)) {
        return false;
    }
    if (TRUTHY_ENV_VALUES.has(normalized)) {
        return true;
    }
    return options && options.strict ? null : Boolean(value);
}
exports.FALSY_ENV_VALUES = FALSY_ENV_VALUES;
exports.TRUTHY_ENV_VALUES = TRUTHY_ENV_VALUES;
exports.envToBool = envToBool; //# sourceMappingURL=envToBool.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/module.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const node_path = __turbopack_context__.r("[externals]/node:path [external] (node:path, cjs)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
/** normalizes Windows paths */ function normalizeWindowsPath(path) {
    return path.replace(/^[A-Z]:/, '') // remove Windows-style prefix
    .replace(/\\/g, '/'); // replace all `\` instances with `/`
}
/** Creates a function that gets the module name from a filename */ function createGetModuleFromFilename(basePath = process.argv[1] ? core.dirname(process.argv[1]) : process.cwd(), isWindows = node_path.sep === '\\') {
    const normalizedBase = isWindows ? normalizeWindowsPath(basePath) : basePath;
    return (filename)=>{
        if (!filename) {
            return;
        }
        const normalizedFilename = isWindows ? normalizeWindowsPath(filename) : filename;
        // eslint-disable-next-line prefer-const
        let { dir, base: file, ext } = node_path.posix.parse(normalizedFilename);
        if (ext === '.js' || ext === '.mjs' || ext === '.cjs') {
            file = file.slice(0, ext.length * -1);
        }
        // The file name might be URI-encoded which we want to decode to
        // the original file name.
        const decodedFile = decodeURIComponent(file);
        if (!dir) {
            // No dirname whatsoever
            dir = '.';
        }
        const n = dir.lastIndexOf('/node_modules');
        if (n > -1) {
            return `${dir.slice(n + 14).replace(/\//g, '.')}:${decodedFile}`;
        }
        // Let's see if it's a part of the main module
        // To be a part of main module, it has to share the same base
        if (dir.startsWith(normalizedBase)) {
            const moduleName = dir.slice(normalizedBase.length + 1).replace(/\//g, '.');
            return moduleName ? `${moduleName}:${decodedFile}` : decodedFile;
        }
        return decodedFile;
    };
}
exports.createGetModuleFromFilename = createGetModuleFromFilename; //# sourceMappingURL=module.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/sdk/api.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const module$1 = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/module.js [instrumentation] (ecmascript)");
/**
 * Returns a release dynamically from environment variables.
 */ // eslint-disable-next-line complexity
function getSentryRelease(fallback) {
    // Always read first as Sentry takes this as precedence
    if (process.env.SENTRY_RELEASE) {
        return process.env.SENTRY_RELEASE;
    }
    // This supports the variable that sentry-webpack-plugin injects
    if (core.GLOBAL_OBJ.SENTRY_RELEASE && core.GLOBAL_OBJ.SENTRY_RELEASE.id) {
        return core.GLOBAL_OBJ.SENTRY_RELEASE.id;
    }
    // This list is in approximate alpha order, separated into 3 categories:
    // 1. Git providers
    // 2. CI providers with specific environment variables (has the provider name in the variable name)
    // 3. CI providers with generic environment variables (checked for last to prevent possible false positives)
    const possibleReleaseNameOfGitProvider = // GitHub Actions - https://help.github.com/en/actions/configuring-and-managing-workflows/using-environment-variables#default-environment-variables
    process.env['GITHUB_SHA'] || // GitLab CI - https://docs.gitlab.com/ee/ci/variables/predefined_variables.html
    process.env['CI_MERGE_REQUEST_SOURCE_BRANCH_SHA'] || process.env['CI_BUILD_REF'] || process.env['CI_COMMIT_SHA'] || // Bitbucket - https://support.atlassian.com/bitbucket-cloud/docs/variables-and-secrets/
    process.env['BITBUCKET_COMMIT'];
    const possibleReleaseNameOfCiProvidersWithSpecificEnvVar = // AppVeyor - https://www.appveyor.com/docs/environment-variables/
    process.env['APPVEYOR_PULL_REQUEST_HEAD_COMMIT'] || process.env['APPVEYOR_REPO_COMMIT'] || // AWS CodeBuild - https://docs.aws.amazon.com/codebuild/latest/userguide/build-env-ref-env-vars.html
    process.env['CODEBUILD_RESOLVED_SOURCE_VERSION'] || // AWS Amplify - https://docs.aws.amazon.com/amplify/latest/userguide/environment-variables.html
    process.env['AWS_COMMIT_ID'] || // Azure Pipelines - https://docs.microsoft.com/en-us/azure/devops/pipelines/build/variables?view=azure-devops&tabs=yaml
    process.env['BUILD_SOURCEVERSION'] || // Bitrise - https://devcenter.bitrise.io/builds/available-environment-variables/
    process.env['GIT_CLONE_COMMIT_HASH'] || // Buddy CI - https://buddy.works/docs/pipelines/environment-variables#default-environment-variables
    process.env['BUDDY_EXECUTION_REVISION'] || // Builtkite - https://buildkite.com/docs/pipelines/environment-variables
    process.env['BUILDKITE_COMMIT'] || // CircleCI - https://circleci.com/docs/variables/
    process.env['CIRCLE_SHA1'] || // Cirrus CI - https://cirrus-ci.org/guide/writing-tasks/#environment-variables
    process.env['CIRRUS_CHANGE_IN_REPO'] || // Codefresh - https://codefresh.io/docs/docs/codefresh-yaml/variables/
    process.env['CF_REVISION'] || // Codemagic - https://docs.codemagic.io/yaml-basic-configuration/environment-variables/
    process.env['CM_COMMIT'] || // Cloudflare Pages - https://developers.cloudflare.com/pages/platform/build-configuration/#environment-variables
    process.env['CF_PAGES_COMMIT_SHA'] || // Drone - https://docs.drone.io/pipeline/environment/reference/
    process.env['DRONE_COMMIT_SHA'] || // Flightcontrol - https://www.flightcontrol.dev/docs/guides/flightcontrol/environment-variables#built-in-environment-variables
    process.env['FC_GIT_COMMIT_SHA'] || // Heroku #1 https://devcenter.heroku.com/articles/heroku-ci
    process.env['HEROKU_TEST_RUN_COMMIT_VERSION'] || // Heroku #2 https://docs.sentry.io/product/integrations/deployment/heroku/#configure-releases
    process.env['HEROKU_SLUG_COMMIT'] || // Railway - https://docs.railway.app/reference/variables#git-variables
    process.env['RAILWAY_GIT_COMMIT_SHA'] || // Render - https://render.com/docs/environment-variables
    process.env['RENDER_GIT_COMMIT'] || // Semaphore CI - https://docs.semaphoreci.com/ci-cd-environment/environment-variables
    process.env['SEMAPHORE_GIT_SHA'] || // TravisCI - https://docs.travis-ci.com/user/environment-variables/#default-environment-variables
    process.env['TRAVIS_PULL_REQUEST_SHA'] || // Vercel - https://vercel.com/docs/v2/build-step#system-environment-variables
    process.env['VERCEL_GIT_COMMIT_SHA'] || process.env['VERCEL_GITHUB_COMMIT_SHA'] || process.env['VERCEL_GITLAB_COMMIT_SHA'] || process.env['VERCEL_BITBUCKET_COMMIT_SHA'] || // Zeit (now known as Vercel)
    process.env['ZEIT_GITHUB_COMMIT_SHA'] || process.env['ZEIT_GITLAB_COMMIT_SHA'] || process.env['ZEIT_BITBUCKET_COMMIT_SHA'];
    const possibleReleaseNameOfCiProvidersWithGenericEnvVar = // CloudBees CodeShip - https://docs.cloudbees.com/docs/cloudbees-codeship/latest/pro-builds-and-configuration/environment-variables
    process.env['CI_COMMIT_ID'] || // Coolify - https://coolify.io/docs/knowledge-base/environment-variables
    process.env['SOURCE_COMMIT'] || // Heroku #3 https://devcenter.heroku.com/changelog-items/630
    process.env['SOURCE_VERSION'] || // Jenkins - https://plugins.jenkins.io/git/#environment-variables
    process.env['GIT_COMMIT'] || // Netlify - https://docs.netlify.com/configure-builds/environment-variables/#build-metadata
    process.env['COMMIT_REF'] || // TeamCity - https://www.jetbrains.com/help/teamcity/predefined-build-parameters.html
    process.env['BUILD_VCS_NUMBER'] || // Woodpecker CI - https://woodpecker-ci.org/docs/usage/environment
    process.env['CI_COMMIT_SHA'];
    return possibleReleaseNameOfGitProvider || possibleReleaseNameOfCiProvidersWithSpecificEnvVar || possibleReleaseNameOfCiProvidersWithGenericEnvVar || fallback;
}
/** Node.js stack parser */ const defaultStackParser = core.createStackParser(core.nodeStackLineParser(module$1.createGetModuleFromFilename()));
exports.defaultStackParser = defaultStackParser;
exports.getSentryRelease = getSentryRelease; //# sourceMappingURL=api.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/sdk/client.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _nullishCoalesce, _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const os = __turbopack_context__.r("[externals]/node:os [external] (node:os, cjs)");
const api = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js [instrumentation] (ecmascript)");
const instrumentation = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+instrumentation@0.56.0_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/instrumentation/build/esm/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const opentelemetry = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+opentelemetry@8.46.0_@opentelemetry+api@1.9.0_@opentelemetry+core@1.30.1_@opent_9f3efdcc19a3583a187ce7ebb26bfca9/node_modules/@sentry/opentelemetry/build/cjs/index.js [instrumentation] (ecmascript)");
const worker_threads = __turbopack_context__.r("[externals]/worker_threads [external] (worker_threads, cjs)");
const debugBuild = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/debug-build.js [instrumentation] (ecmascript)");
const DEFAULT_CLIENT_REPORT_FLUSH_INTERVAL_MS = 60000; // 60s was chosen arbitrarily
/** A client for using Sentry with Node & OpenTelemetry. */ class NodeClient extends core.ServerRuntimeClient {
    constructor(options){
        const clientOptions = {
            ...options,
            platform: 'node',
            runtime: {
                name: 'node',
                version: ("TURBOPACK ident replacement", globalThis).process.version
            },
            serverName: options.serverName || ("TURBOPACK ident replacement", globalThis).process.env.SENTRY_NAME || os.hostname()
        };
        if (options.openTelemetryInstrumentations) {
            instrumentation.registerInstrumentations({
                instrumentations: options.openTelemetryInstrumentations
            });
        }
        core.applySdkMetadata(clientOptions, 'node');
        core.logger.log(`Initializing Sentry: process: ${process.pid}, thread: ${worker_threads.isMainThread ? 'main' : `worker-${worker_threads.threadId}`}.`);
        super(clientOptions);
    }
    /** Get the OTEL tracer. */ get tracer() {
        if (this._tracer) {
            return this._tracer;
        }
        const name = '@sentry/node';
        const version = core.SDK_VERSION;
        const tracer = api.trace.getTracer(name, version);
        this._tracer = tracer;
        return tracer;
    }
    // Eslint ignore explanation: This is already documented in super.
    // eslint-disable-next-line jsdoc/require-jsdoc
    async flush(timeout) {
        const provider = this.traceProvider;
        const spanProcessor = _optionalChain([
            provider,
            'optionalAccess',
            (_)=>_.activeSpanProcessor
        ]);
        if (spanProcessor) {
            await spanProcessor.forceFlush();
        }
        if (this.getOptions().sendClientReports) {
            this._flushOutcomes();
        }
        return super.flush(timeout);
    }
    // Eslint ignore explanation: This is already documented in super.
    // eslint-disable-next-line jsdoc/require-jsdoc
    close(timeout) {
        if (this._clientReportInterval) {
            clearInterval(this._clientReportInterval);
        }
        if (this._clientReportOnExitFlushListener) {
            process.off('beforeExit', this._clientReportOnExitFlushListener);
        }
        return super.close(timeout);
    }
    /**
   * Will start tracking client reports for this client.
   *
   * NOTICE: This method will create an interval that is periodically called and attach a `process.on('beforeExit')`
   * hook. To clean up these resources, call `.close()` when you no longer intend to use the client. Not doing so will
   * result in a memory leak.
   */ // The reason client reports need to be manually activated with this method instead of just enabling them in a
    // constructor, is that if users periodically and unboundedly create new clients, we will create more and more
    // intervals and beforeExit listeners, thus leaking memory. In these situations, users are required to call
    // `client.close()` in order to dispose of the acquired resources.
    // We assume that calling this method in Sentry.init() is a sensible default, because calling Sentry.init() over and
    // over again would also result in memory leaks.
    // Note: We have experimented with using `FinalizationRegisty` to clear the interval when the client is garbage
    // collected, but it did not work, because the cleanup function never got called.
    startClientReportTracking() {
        const clientOptions = this.getOptions();
        if (clientOptions.sendClientReports) {
            this._clientReportOnExitFlushListener = ()=>{
                this._flushOutcomes();
            };
            this._clientReportInterval = setInterval(()=>{
                debugBuild.DEBUG_BUILD && core.logger.log('Flushing client reports based on interval.');
                this._flushOutcomes();
            }, _nullishCoalesce(clientOptions.clientReportFlushInterval, ()=>DEFAULT_CLIENT_REPORT_FLUSH_INTERVAL_MS))// Unref is critical for not preventing the process from exiting because the interval is active.
            .unref();
            process.on('beforeExit', this._clientReportOnExitFlushListener);
        }
    }
    /** Custom implementation for OTEL, so we can handle scope-span linking. */ _getTraceInfoFromScope(scope) {
        if (!scope) {
            return [
                undefined,
                undefined
            ];
        }
        return opentelemetry.getTraceContextForScope(this, scope);
    }
}
exports.NodeClient = NodeClient; //# sourceMappingURL=client.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/sdk/initOtel.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const moduleModule = __turbopack_context__.r("[externals]/module [external] (module, cjs)");
const api = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+api@1.9.0/node_modules/@opentelemetry/api/build/esm/index.js [instrumentation] (ecmascript)");
const resources = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+resources@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/resources/build/esm/index.js [instrumentation] (ecmascript)");
const sdkTraceBase = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+sdk-trace-base@1.30.1_@opentelemetry+api@1.9.0/node_modules/@opentelemetry/sdk-trace-base/build/esm/index.js [instrumentation] (ecmascript)");
const semanticConventions = __turbopack_context__.r("[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.36.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const opentelemetry = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+opentelemetry@8.46.0_@opentelemetry+api@1.9.0_@opentelemetry+core@1.30.1_@opent_9f3efdcc19a3583a187ce7ebb26bfca9/node_modules/@sentry/opentelemetry/build/cjs/index.js [instrumentation] (ecmascript)");
const importInTheMiddle = __turbopack_context__.r("[project]/node_modules/.pnpm/import-in-the-middle@1.14.2/node_modules/import-in-the-middle/index.js [instrumentation] (ecmascript)");
const debugBuild = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/debug-build.js [instrumentation] (ecmascript)");
const index = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/index.js [instrumentation] (ecmascript)");
const contextManager = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/contextManager.js [instrumentation] (ecmascript)");
const commonjs = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/commonjs.js [instrumentation] (ecmascript)");
var _documentCurrentScript = typeof document !== 'undefined' ? document.currentScript : null;
// About 277h - this must fit into new Array(len)!
const MAX_MAX_SPAN_WAIT_DURATION = 1000000;
/**
 * Initialize OpenTelemetry for Node.
 */ function initOpenTelemetry(client) {
    if (client.getOptions().debug) {
        setupOpenTelemetryLogger();
    }
    const provider = setupOtel(client);
    client.traceProvider = provider;
}
function getRegisterOptions(esmHookConfig) {
    // TODO(v9): Make onlyIncludeInstrumentedModules: true the default behavior.
    if (_optionalChain([
        esmHookConfig,
        'optionalAccess',
        (_)=>_.onlyIncludeInstrumentedModules
    ])) {
        const { addHookMessagePort } = importInTheMiddle.createAddHookMessageChannel();
        // If the user supplied include, we need to use that as a starting point or use an empty array to ensure no modules
        // are wrapped if they are not hooked
        // eslint-disable-next-line deprecation/deprecation
        return {
            data: {
                addHookMessagePort,
                include: esmHookConfig.include || []
            },
            transferList: [
                addHookMessagePort
            ]
        };
    }
    return {
        data: esmHookConfig
    };
}
/** Initialize the ESM loader. */ function maybeInitializeEsmLoader(esmHookConfig) {
    const [nodeMajor = 0, nodeMinor = 0] = process.versions.node.split('.').map(Number);
    // Register hook was added in v20.6.0 and v18.19.0
    if (nodeMajor >= 22 || nodeMajor === 20 && nodeMinor >= 6 || nodeMajor === 18 && nodeMinor >= 19) {
        // We need to work around using import.meta.url directly because jest complains about it.
        const importMetaUrl = typeof (typeof document === 'undefined' ? __turbopack_context__.r("[externals]/url [external] (url, cjs)").pathToFileURL(("TURBOPACK compile-time value", "/ROOT/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/sdk/initOtel.js")).href : _documentCurrentScript && _documentCurrentScript.tagName.toUpperCase() === 'SCRIPT' && _documentCurrentScript.src || new URL('sdk/initOtel.js', document.baseURI).href) !== 'undefined' ? typeof document === 'undefined' ? __turbopack_context__.r("[externals]/url [external] (url, cjs)").pathToFileURL(("TURBOPACK compile-time value", "/ROOT/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/sdk/initOtel.js")).href : _documentCurrentScript && _documentCurrentScript.tagName.toUpperCase() === 'SCRIPT' && _documentCurrentScript.src || new URL('sdk/initOtel.js', document.baseURI).href : undefined;
        if (!core.GLOBAL_OBJ._sentryEsmLoaderHookRegistered && importMetaUrl) {
            try {
                // @ts-expect-error register is available in these versions
                moduleModule.default.register('import-in-the-middle/hook.mjs', importMetaUrl, getRegisterOptions(esmHookConfig));
                core.GLOBAL_OBJ._sentryEsmLoaderHookRegistered = true;
            } catch (error) {
                core.logger.warn('Failed to register ESM hook', error);
            }
        }
    } else {
        core.consoleSandbox(()=>{
            // eslint-disable-next-line no-console
            console.warn('[Sentry] You are using Node.js in ESM mode ("import syntax"). The Sentry Node.js SDK is not compatible with ESM in Node.js versions before 18.19.0 or before 20.6.0. Please either build your application with CommonJS ("require() syntax"), or upgrade your Node.js version.');
        });
    }
}
/**
 * Preload OpenTelemetry for Node.
 * This can be used to preload instrumentation early, but set up Sentry later.
 * By preloading the OTEL instrumentation wrapping still happens early enough that everything works.
 */ function preloadOpenTelemetry(options = {}) {
    const { debug } = options;
    if (debug) {
        core.logger.enable();
        setupOpenTelemetryLogger();
    }
    if (!commonjs.isCjs()) {
        maybeInitializeEsmLoader(options.registerEsmLoaderHooks);
    }
    // These are all integrations that we need to pre-load to ensure they are set up before any other code runs
    getPreloadMethods(options.integrations).forEach((fn)=>{
        fn();
        if (debug) {
            core.logger.log(`[Sentry] Preloaded ${fn.id} instrumentation`);
        }
    });
}
function getPreloadMethods(integrationNames) {
    const instruments = index.getOpenTelemetryInstrumentationToPreload();
    if (!integrationNames) {
        return instruments;
    }
    return instruments.filter((instrumentation)=>integrationNames.includes(instrumentation.id));
}
/** Just exported for tests. */ function setupOtel(client) {
    // Create and configure NodeTracerProvider
    const provider = new sdkTraceBase.BasicTracerProvider({
        sampler: new opentelemetry.SentrySampler(client),
        resource: new resources.Resource({
            [semanticConventions.ATTR_SERVICE_NAME]: 'node',
            // eslint-disable-next-line deprecation/deprecation
            [semanticConventions.SEMRESATTRS_SERVICE_NAMESPACE]: 'sentry',
            [semanticConventions.ATTR_SERVICE_VERSION]: core.SDK_VERSION
        }),
        forceFlushTimeoutMillis: 500,
        spanProcessors: [
            new opentelemetry.SentrySpanProcessor({
                timeout: _clampSpanProcessorTimeout(client.getOptions().maxSpanWaitDuration)
            })
        ]
    });
    // Initialize the provider
    provider.register({
        propagator: new opentelemetry.SentryPropagator(),
        contextManager: new contextManager.SentryContextManager()
    });
    return provider;
}
/** Just exported for tests. */ function _clampSpanProcessorTimeout(maxSpanWaitDuration) {
    if (maxSpanWaitDuration == null) {
        return undefined;
    }
    // We guard for a max. value here, because we create an array with this length
    // So if this value is too large, this would fail
    if (maxSpanWaitDuration > MAX_MAX_SPAN_WAIT_DURATION) {
        debugBuild.DEBUG_BUILD && core.logger.warn(`\`maxSpanWaitDuration\` is too high, using the maximum value of ${MAX_MAX_SPAN_WAIT_DURATION}`);
        return MAX_MAX_SPAN_WAIT_DURATION;
    } else if (maxSpanWaitDuration <= 0 || Number.isNaN(maxSpanWaitDuration)) {
        debugBuild.DEBUG_BUILD && core.logger.warn('`maxSpanWaitDuration` must be a positive number, using default value instead.');
        return undefined;
    }
    return maxSpanWaitDuration;
}
/**
 * Setup the OTEL logger to use our own logger.
 */ function setupOpenTelemetryLogger() {
    const otelLogger = new Proxy(core.logger, {
        get (target, prop, receiver) {
            const actualProp = prop === 'verbose' ? 'debug' : prop;
            return Reflect.get(target, actualProp, receiver);
        }
    });
    // Disable diag, to ensure this works even if called multiple times
    api.diag.disable();
    api.diag.setLogger(otelLogger, api.DiagLogLevel.DEBUG);
}
exports._clampSpanProcessorTimeout = _clampSpanProcessorTimeout;
exports.initOpenTelemetry = initOpenTelemetry;
exports.maybeInitializeEsmLoader = maybeInitializeEsmLoader;
exports.preloadOpenTelemetry = preloadOpenTelemetry;
exports.setupOtel = setupOtel; //# sourceMappingURL=initOtel.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/sdk/index.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const opentelemetry = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+opentelemetry@8.46.0_@opentelemetry+api@1.9.0_@opentelemetry+core@1.30.1_@opent_9f3efdcc19a3583a187ce7ebb26bfca9/node_modules/@sentry/opentelemetry/build/cjs/index.js [instrumentation] (ecmascript)");
const debugBuild = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/debug-build.js [instrumentation] (ecmascript)");
const childProcess = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/childProcess.js [instrumentation] (ecmascript)");
const console$1 = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/console.js [instrumentation] (ecmascript)");
const context = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/context.js [instrumentation] (ecmascript)");
const contextlines = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/contextlines.js [instrumentation] (ecmascript)");
const index = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/http/index.js [instrumentation] (ecmascript)");
const index$1 = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/local-variables/index.js [instrumentation] (ecmascript)");
const modules = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/modules.js [instrumentation] (ecmascript)");
const nodeFetch = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/node-fetch.js [instrumentation] (ecmascript)");
const onuncaughtexception = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/onuncaughtexception.js [instrumentation] (ecmascript)");
const onunhandledrejection = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/onunhandledrejection.js [instrumentation] (ecmascript)");
const spotlight = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/spotlight.js [instrumentation] (ecmascript)");
const index$2 = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/index.js [instrumentation] (ecmascript)");
const http = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/transports/http.js [instrumentation] (ecmascript)");
const commonjs = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/commonjs.js [instrumentation] (ecmascript)");
const envToBool = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/envToBool.js [instrumentation] (ecmascript)");
const api = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/sdk/api.js [instrumentation] (ecmascript)");
const client = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/sdk/client.js [instrumentation] (ecmascript)");
const initOtel = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/sdk/initOtel.js [instrumentation] (ecmascript)");
function getCjsOnlyIntegrations() {
    return commonjs.isCjs() ? [
        modules.modulesIntegration()
    ] : [];
}
/**
 * Get default integrations, excluding performance.
 */ function getDefaultIntegrationsWithoutPerformance() {
    return [
        // Common
        core.inboundFiltersIntegration(),
        core.functionToStringIntegration(),
        core.linkedErrorsIntegration(),
        core.requestDataIntegration(),
        // Native Wrappers
        console$1.consoleIntegration(),
        index.httpIntegration(),
        nodeFetch.nativeNodeFetchIntegration(),
        // Global Handlers
        onuncaughtexception.onUncaughtExceptionIntegration(),
        onunhandledrejection.onUnhandledRejectionIntegration(),
        // Event Info
        contextlines.contextLinesIntegration(),
        index$1.localVariablesIntegration(),
        context.nodeContextIntegration(),
        childProcess.childProcessIntegration(),
        ...getCjsOnlyIntegrations()
    ];
}
/** Get the default integrations for the Node SDK. */ function getDefaultIntegrations(options) {
    return [
        ...getDefaultIntegrationsWithoutPerformance(),
        // We only add performance integrations if tracing is enabled
        // Note that this means that without tracing enabled, e.g. `expressIntegration()` will not be added
        // This means that generally request isolation will work (because that is done by httpIntegration)
        // But `transactionName` will not be set automatically
        ...shouldAddPerformanceIntegrations(options) ? index$2.getAutoPerformanceIntegrations() : []
    ];
}
function shouldAddPerformanceIntegrations(options) {
    if (!core.hasTracingEnabled(options)) {
        return false;
    }
    // We want to ensure `tracesSampleRate` is not just undefined/null here
    // eslint-disable-next-line deprecation/deprecation
    return options.enableTracing || options.tracesSampleRate != null || 'tracesSampler' in options;
}
/**
 * Initialize Sentry for Node.
 */ function init(options = {}) {
    return _init(options, getDefaultIntegrations);
}
/**
 * Initialize Sentry for Node, without any integrations added by default.
 */ function initWithoutDefaultIntegrations(options = {}) {
    return _init(options, ()=>[]);
}
/**
 * Initialize Sentry for Node, without performance instrumentation.
 */ function _init(_options = {}, getDefaultIntegrationsImpl) {
    const options = getClientOptions(_options, getDefaultIntegrationsImpl);
    if (options.debug === true) {
        if (debugBuild.DEBUG_BUILD) {
            core.logger.enable();
        } else {
            // use `console.warn` rather than `logger.warn` since by non-debug bundles have all `logger.x` statements stripped
            core.consoleSandbox(()=>{
                // eslint-disable-next-line no-console
                console.warn('[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.');
            });
        }
    }
    if (!commonjs.isCjs() && options.registerEsmLoaderHooks !== false) {
        initOtel.maybeInitializeEsmLoader(options.registerEsmLoaderHooks === true ? undefined : options.registerEsmLoaderHooks);
    }
    opentelemetry.setOpenTelemetryContextAsyncContextStrategy();
    const scope = core.getCurrentScope();
    scope.update(options.initialScope);
    if (options.spotlight && !options.integrations.some(({ name })=>name === spotlight.INTEGRATION_NAME)) {
        options.integrations.push(spotlight.spotlightIntegration({
            sidecarUrl: typeof options.spotlight === 'string' ? options.spotlight : undefined
        }));
    }
    const client$1 = new client.NodeClient(options);
    // The client is on the current scope, from where it generally is inherited
    core.getCurrentScope().setClient(client$1);
    client$1.init();
    core.logger.log(`Running in ${commonjs.isCjs() ? 'CommonJS' : 'ESM'} mode.`);
    // TODO(V9): Remove this code since all of the logic should be in an integration
    // eslint-disable-next-line deprecation/deprecation
    if (options.autoSessionTracking) {
        startSessionTracking();
    }
    client$1.startClientReportTracking();
    updateScopeFromEnvVariables();
    // If users opt-out of this, they _have_ to set up OpenTelemetry themselves
    // There is no way to use this SDK without OpenTelemetry!
    if (!options.skipOpenTelemetrySetup) {
        initOtel.initOpenTelemetry(client$1);
        validateOpenTelemetrySetup();
    }
    opentelemetry.enhanceDscWithOpenTelemetryRootSpanName(client$1);
    opentelemetry.setupEventContextTrace(client$1);
    return client$1;
}
/**
 * Validate that your OpenTelemetry setup is correct.
 */ function validateOpenTelemetrySetup() {
    if (!debugBuild.DEBUG_BUILD) {
        return;
    }
    const setup = opentelemetry.openTelemetrySetupCheck();
    const required = [
        'SentryContextManager',
        'SentryPropagator'
    ];
    if (core.hasTracingEnabled()) {
        required.push('SentrySpanProcessor');
    }
    for (const k of required){
        if (!setup.includes(k)) {
            core.logger.error(`You have to set up the ${k}. Without this, the OpenTelemetry & Sentry integration will not work properly.`);
        }
    }
    if (!setup.includes('SentrySampler')) {
        core.logger.warn('You have to set up the SentrySampler. Without this, the OpenTelemetry & Sentry integration may still work, but sample rates set for the Sentry SDK will not be respected. If you use a custom sampler, make sure to use `wrapSamplingDecision`.');
    }
}
function getClientOptions(options, getDefaultIntegrationsImpl) {
    const release = getRelease(options.release);
    const autoSessionTracking = typeof release !== 'string' ? false : options.autoSessionTracking === undefined ? true : options.autoSessionTracking;
    if (options.spotlight == null) {
        const spotlightEnv = envToBool.envToBool(process.env.SENTRY_SPOTLIGHT, {
            strict: true
        });
        if (spotlightEnv == null) {
            options.spotlight = process.env.SENTRY_SPOTLIGHT;
        } else {
            options.spotlight = spotlightEnv;
        }
    }
    const tracesSampleRate = getTracesSampleRate(options.tracesSampleRate);
    const baseOptions = core.dropUndefinedKeys({
        transport: http.makeNodeTransport,
        dsn: process.env.SENTRY_DSN,
        environment: process.env.SENTRY_ENVIRONMENT,
        sendClientReports: true
    });
    const overwriteOptions = core.dropUndefinedKeys({
        release,
        autoSessionTracking,
        tracesSampleRate
    });
    const mergedOptions = {
        ...baseOptions,
        ...options,
        ...overwriteOptions
    };
    if (options.defaultIntegrations === undefined) {
        options.defaultIntegrations = getDefaultIntegrationsImpl(mergedOptions);
    }
    const clientOptions = {
        ...mergedOptions,
        stackParser: core.stackParserFromStackParserOptions(options.stackParser || api.defaultStackParser),
        integrations: core.getIntegrationsToSetup({
            defaultIntegrations: options.defaultIntegrations,
            integrations: options.integrations
        })
    };
    return clientOptions;
}
function getRelease(release) {
    if (release !== undefined) {
        return release;
    }
    const detectedRelease = api.getSentryRelease();
    if (detectedRelease !== undefined) {
        return detectedRelease;
    }
    return undefined;
}
function getTracesSampleRate(tracesSampleRate) {
    if (tracesSampleRate !== undefined) {
        return tracesSampleRate;
    }
    const sampleRateFromEnv = process.env.SENTRY_TRACES_SAMPLE_RATE;
    if (!sampleRateFromEnv) {
        return undefined;
    }
    const parsed = parseFloat(sampleRateFromEnv);
    return isFinite(parsed) ? parsed : undefined;
}
/**
 * Update scope and propagation context based on environmental variables.
 *
 * See https://github.com/getsentry/rfcs/blob/main/text/0071-continue-trace-over-process-boundaries.md
 * for more details.
 */ function updateScopeFromEnvVariables() {
    if (envToBool.envToBool(process.env.SENTRY_USE_ENVIRONMENT) !== false) {
        const sentryTraceEnv = process.env.SENTRY_TRACE;
        const baggageEnv = process.env.SENTRY_BAGGAGE;
        const propagationContext = core.propagationContextFromHeaders(sentryTraceEnv, baggageEnv);
        core.getCurrentScope().setPropagationContext(propagationContext);
    }
}
/**
 * Enable automatic Session Tracking for the node process.
 */ function startSessionTracking() {
    const client = core.getClient();
    // eslint-disable-next-line deprecation/deprecation
    if (client && client.getOptions().autoSessionTracking) {
        client.initSessionFlusher();
    }
    core.startSession();
    // Emitted in the case of healthy sessions, error of `mechanism.handled: true` and unhandledrejections because
    // The 'beforeExit' event is not emitted for conditions causing explicit termination,
    // such as calling process.exit() or uncaught exceptions.
    // Ref: https://nodejs.org/api/process.html#process_event_beforeexit
    process.on('beforeExit', ()=>{
        const session = core.getIsolationScope().getSession();
        // Only call endSession, if the Session exists on Scope and SessionStatus is not a
        // Terminal Status i.e. Exited or Crashed because
        // "When a session is moved away from ok it must not be updated anymore."
        // Ref: https://develop.sentry.dev/sdk/sessions/
        if (session && session.status !== 'ok') {
            core.endSession();
        }
    });
}
exports.getDefaultIntegrations = getDefaultIntegrations;
exports.getDefaultIntegrationsWithoutPerformance = getDefaultIntegrationsWithoutPerformance;
exports.init = init;
exports.initWithoutDefaultIntegrations = initWithoutDefaultIntegrations;
exports.validateOpenTelemetrySetup = validateOpenTelemetrySetup; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/cron/common.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const replacements = [
    [
        'january',
        '1'
    ],
    [
        'february',
        '2'
    ],
    [
        'march',
        '3'
    ],
    [
        'april',
        '4'
    ],
    [
        'may',
        '5'
    ],
    [
        'june',
        '6'
    ],
    [
        'july',
        '7'
    ],
    [
        'august',
        '8'
    ],
    [
        'september',
        '9'
    ],
    [
        'october',
        '10'
    ],
    [
        'november',
        '11'
    ],
    [
        'december',
        '12'
    ],
    [
        'jan',
        '1'
    ],
    [
        'feb',
        '2'
    ],
    [
        'mar',
        '3'
    ],
    [
        'apr',
        '4'
    ],
    [
        'may',
        '5'
    ],
    [
        'jun',
        '6'
    ],
    [
        'jul',
        '7'
    ],
    [
        'aug',
        '8'
    ],
    [
        'sep',
        '9'
    ],
    [
        'oct',
        '10'
    ],
    [
        'nov',
        '11'
    ],
    [
        'dec',
        '12'
    ],
    [
        'sunday',
        '0'
    ],
    [
        'monday',
        '1'
    ],
    [
        'tuesday',
        '2'
    ],
    [
        'wednesday',
        '3'
    ],
    [
        'thursday',
        '4'
    ],
    [
        'friday',
        '5'
    ],
    [
        'saturday',
        '6'
    ],
    [
        'sun',
        '0'
    ],
    [
        'mon',
        '1'
    ],
    [
        'tue',
        '2'
    ],
    [
        'wed',
        '3'
    ],
    [
        'thu',
        '4'
    ],
    [
        'fri',
        '5'
    ],
    [
        'sat',
        '6'
    ]
];
/**
 * Replaces names in cron expressions
 */ function replaceCronNames(cronExpression) {
    return replacements.reduce(// eslint-disable-next-line @sentry-internal/sdk/no-regexp-constructor
    (acc, [name, replacement])=>acc.replace(new RegExp(name, 'gi'), replacement), cronExpression);
}
exports.replaceCronNames = replaceCronNames; //# sourceMappingURL=common.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/cron/cron.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const common = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/cron/common.js [instrumentation] (ecmascript)");
const ERROR_TEXT = 'Automatic instrumentation of CronJob only supports crontab string';
/**
 * Instruments the `cron` library to send a check-in event to Sentry for each job execution.
 *
 * ```ts
 * import * as Sentry from '@sentry/node';
 * import { CronJob } from 'cron';
 *
 * const CronJobWithCheckIn = Sentry.cron.instrumentCron(CronJob, 'my-cron-job');
 *
 * // use the constructor
 * const job = new CronJobWithCheckIn('* * * * *', () => {
 *  console.log('You will see this message every minute');
 * });
 *
 * // or from
 * const job = CronJobWithCheckIn.from({ cronTime: '* * * * *', onTick: () => {
 *   console.log('You will see this message every minute');
 * });
 * ```
 */ function instrumentCron(lib, monitorSlug) {
    let jobScheduled = false;
    return new Proxy(lib, {
        construct (target, args) {
            const [cronTime, onTick, onComplete, start, timeZone, ...rest] = args;
            if (typeof cronTime !== 'string') {
                throw new Error(ERROR_TEXT);
            }
            if (jobScheduled) {
                throw new Error(`A job named '${monitorSlug}' has already been scheduled`);
            }
            jobScheduled = true;
            const cronString = common.replaceCronNames(cronTime);
            async function monitoredTick(context, onComplete) {
                return core.withMonitor(monitorSlug, async ()=>{
                    try {
                        await onTick(context, onComplete);
                    } catch (e) {
                        core.captureException(e);
                        throw e;
                    }
                }, {
                    schedule: {
                        type: 'crontab',
                        value: cronString
                    },
                    timezone: timeZone || undefined
                });
            }
            return new target(cronTime, monitoredTick, onComplete, start, timeZone, ...rest);
        },
        get (target, prop) {
            if (prop === 'from') {
                return (param)=>{
                    const { cronTime, onTick, timeZone } = param;
                    if (typeof cronTime !== 'string') {
                        throw new Error(ERROR_TEXT);
                    }
                    if (jobScheduled) {
                        throw new Error(`A job named '${monitorSlug}' has already been scheduled`);
                    }
                    jobScheduled = true;
                    const cronString = common.replaceCronNames(cronTime);
                    param.onTick = async (context, onComplete)=>{
                        return core.withMonitor(monitorSlug, async ()=>{
                            try {
                                await onTick(context, onComplete);
                            } catch (e) {
                                core.captureException(e);
                                throw e;
                            }
                        }, {
                            schedule: {
                                type: 'crontab',
                                value: cronString
                            },
                            timezone: timeZone || undefined
                        });
                    };
                    return target.from(param);
                };
            } else {
                return target[prop];
            }
        }
    });
}
exports.instrumentCron = instrumentCron; //# sourceMappingURL=cron.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/cron/node-cron.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const common = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/cron/common.js [instrumentation] (ecmascript)");
/**
 * Wraps the `node-cron` library with check-in monitoring.
 *
 * ```ts
 * import * as Sentry from "@sentry/node";
 * import * as cron from "node-cron";
 *
 * const cronWithCheckIn = Sentry.cron.instrumentNodeCron(cron);
 *
 * cronWithCheckIn.schedule(
 *   "* * * * *",
 *   () => {
 *     console.log("running a task every minute");
 *   },
 *   { name: "my-cron-job" },
 * );
 * ```
 */ function instrumentNodeCron(lib) {
    return new Proxy(lib, {
        get (target, prop) {
            if (prop === 'schedule' && target.schedule) {
                // When 'get' is called for schedule, return a proxied version of the schedule function
                return new Proxy(target.schedule, {
                    apply (target, thisArg, argArray) {
                        const [expression, callback, options] = argArray;
                        if (!_optionalChain([
                            options,
                            'optionalAccess',
                            (_)=>_.name
                        ])) {
                            throw new Error('Missing "name" for scheduled job. A name is required for Sentry check-in monitoring.');
                        }
                        async function monitoredCallback() {
                            return core.withMonitor(options.name, async ()=>{
                                // We have to manually catch here and capture the exception because node-cron swallows errors
                                // https://github.com/node-cron/node-cron/issues/399
                                try {
                                    return await callback();
                                } catch (e) {
                                    core.captureException(e);
                                    throw e;
                                }
                            }, {
                                schedule: {
                                    type: 'crontab',
                                    value: common.replaceCronNames(expression)
                                },
                                timezone: _optionalChain([
                                    options,
                                    'optionalAccess',
                                    (_2)=>_2.timezone
                                ])
                            });
                        }
                        return target.apply(thisArg, [
                            expression,
                            monitoredCallback,
                            options
                        ]);
                    }
                });
            } else {
                return target[prop];
            }
        }
    });
}
exports.instrumentNodeCron = instrumentNodeCron; //# sourceMappingURL=node-cron.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/cron/node-schedule.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
var { _optionalChain } = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
Object.defineProperty(exports, '__esModule', {
    value: true
});
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const common = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/cron/common.js [instrumentation] (ecmascript)");
/**
 * Instruments the `node-schedule` library to send a check-in event to Sentry for each job execution.
 *
 * ```ts
 * import * as Sentry from '@sentry/node';
 * import * as schedule from 'node-schedule';
 *
 * const scheduleWithCheckIn = Sentry.cron.instrumentNodeSchedule(schedule);
 *
 * const job = scheduleWithCheckIn.scheduleJob('my-cron-job', '* * * * *', () => {
 *  console.log('You will see this message every minute');
 * });
 * ```
 */ function instrumentNodeSchedule(lib) {
    return new Proxy(lib, {
        get (target, prop) {
            if (prop === 'scheduleJob') {
                // eslint-disable-next-line @typescript-eslint/unbound-method
                return new Proxy(target.scheduleJob, {
                    apply (target, thisArg, argArray) {
                        const [nameOrExpression, expressionOrCallback, callback] = argArray;
                        if (typeof nameOrExpression !== 'string' || typeof expressionOrCallback !== 'string' || typeof callback !== 'function') {
                            throw new Error("Automatic instrumentation of 'node-schedule' requires the first parameter of 'scheduleJob' to be a job name string and the second parameter to be a crontab string");
                        }
                        const monitorSlug = nameOrExpression;
                        const expression = expressionOrCallback;
                        async function monitoredCallback() {
                            return core.withMonitor(monitorSlug, async ()=>{
                                await _optionalChain([
                                    callback,
                                    'optionalCall',
                                    (_)=>_()
                                ]);
                            }, {
                                schedule: {
                                    type: 'crontab',
                                    value: common.replaceCronNames(expression)
                                }
                            });
                        }
                        return target.apply(thisArg, [
                            monitorSlug,
                            expression,
                            monitoredCallback
                        ]);
                    }
                });
            }
            return target[prop];
        }
    });
}
exports.instrumentNodeSchedule = instrumentNodeSchedule; //# sourceMappingURL=node-schedule.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/cron/index.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const cron$1 = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/cron/cron.js [instrumentation] (ecmascript)");
const nodeCron = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/cron/node-cron.js [instrumentation] (ecmascript)");
const nodeSchedule = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/cron/node-schedule.js [instrumentation] (ecmascript)");
/** Methods to instrument cron libraries for Sentry check-ins */ const cron = {
    instrumentCron: cron$1.instrumentCron,
    instrumentNodeCron: nodeCron.instrumentNodeCron,
    instrumentNodeSchedule: nodeSchedule.instrumentNodeSchedule
};
exports.cron = cron; //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/index.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
Object.defineProperty(exports, '__esModule', {
    value: true
});
const index = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/http/index.js [instrumentation] (ecmascript)");
const nodeFetch = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/node-fetch.js [instrumentation] (ecmascript)");
const fs = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/fs.js [instrumentation] (ecmascript)");
const console = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/console.js [instrumentation] (ecmascript)");
const context = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/context.js [instrumentation] (ecmascript)");
const contextlines = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/contextlines.js [instrumentation] (ecmascript)");
const index$1 = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/local-variables/index.js [instrumentation] (ecmascript)");
const modules = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/modules.js [instrumentation] (ecmascript)");
const onuncaughtexception = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/onuncaughtexception.js [instrumentation] (ecmascript)");
const onunhandledrejection = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/onunhandledrejection.js [instrumentation] (ecmascript)");
const index$2 = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/anr/index.js [instrumentation] (ecmascript)");
const express = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/express.js [instrumentation] (ecmascript)");
const fastify = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/fastify.js [instrumentation] (ecmascript)");
const graphql = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/graphql.js [instrumentation] (ecmascript)");
const kafka = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/kafka.js [instrumentation] (ecmascript)");
const lrumemoizer = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/lrumemoizer.js [instrumentation] (ecmascript)");
const mongo = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/mongo.js [instrumentation] (ecmascript)");
const mongoose = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/mongoose.js [instrumentation] (ecmascript)");
const mysql = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/mysql.js [instrumentation] (ecmascript)");
const mysql2 = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/mysql2.js [instrumentation] (ecmascript)");
const redis = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/redis.js [instrumentation] (ecmascript)");
const nest = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/nest/nest.js [instrumentation] (ecmascript)");
const postgres = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/postgres.js [instrumentation] (ecmascript)");
const prisma = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/prisma.js [instrumentation] (ecmascript)");
const index$3 = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/hapi/index.js [instrumentation] (ecmascript)");
const koa = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/koa.js [instrumentation] (ecmascript)");
const connect = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/connect.js [instrumentation] (ecmascript)");
const spotlight = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/spotlight.js [instrumentation] (ecmascript)");
const knex = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/knex.js [instrumentation] (ecmascript)");
const tedious = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/tedious.js [instrumentation] (ecmascript)");
const genericPool = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/genericPool.js [instrumentation] (ecmascript)");
const dataloader = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/dataloader.js [instrumentation] (ecmascript)");
const amqplib = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/amqplib.js [instrumentation] (ecmascript)");
const childProcess = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/childProcess.js [instrumentation] (ecmascript)");
const contextManager = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/contextManager.js [instrumentation] (ecmascript)");
const instrument = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/otel/instrument.js [instrumentation] (ecmascript)");
const index$4 = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/sdk/index.js [instrumentation] (ecmascript)");
const initOtel = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/sdk/initOtel.js [instrumentation] (ecmascript)");
const index$5 = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/integrations/tracing/index.js [instrumentation] (ecmascript)");
const api = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/sdk/api.js [instrumentation] (ecmascript)");
const module$1 = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/utils/module.js [instrumentation] (ecmascript)");
const http = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/transports/http.js [instrumentation] (ecmascript)");
const client = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/sdk/client.js [instrumentation] (ecmascript)");
const index$6 = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+node@8.46.0/node_modules/@sentry/node/build/cjs/cron/index.js [instrumentation] (ecmascript)");
const core = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+core@8.46.0/node_modules/@sentry/core/build/cjs/index.js [instrumentation] (ecmascript)");
const opentelemetry = __turbopack_context__.r("[project]/node_modules/.pnpm/@sentry+opentelemetry@8.46.0_@opentelemetry+api@1.9.0_@opentelemetry+core@1.30.1_@opent_9f3efdcc19a3583a187ce7ebb26bfca9/node_modules/@sentry/opentelemetry/build/cjs/index.js [instrumentation] (ecmascript)");
exports.httpIntegration = index.httpIntegration;
exports.nativeNodeFetchIntegration = nodeFetch.nativeNodeFetchIntegration;
exports.fsIntegration = fs.fsIntegration;
exports.consoleIntegration = console.consoleIntegration;
exports.nodeContextIntegration = context.nodeContextIntegration;
exports.contextLinesIntegration = contextlines.contextLinesIntegration;
exports.localVariablesIntegration = index$1.localVariablesIntegration;
exports.modulesIntegration = modules.modulesIntegration;
exports.onUncaughtExceptionIntegration = onuncaughtexception.onUncaughtExceptionIntegration;
exports.onUnhandledRejectionIntegration = onunhandledrejection.onUnhandledRejectionIntegration;
exports.anrIntegration = index$2.anrIntegration;
exports.disableAnrDetectionForCallback = index$2.disableAnrDetectionForCallback;
exports.expressErrorHandler = express.expressErrorHandler;
exports.expressIntegration = express.expressIntegration;
exports.setupExpressErrorHandler = express.setupExpressErrorHandler;
exports.fastifyIntegration = fastify.fastifyIntegration;
exports.setupFastifyErrorHandler = fastify.setupFastifyErrorHandler;
exports.graphqlIntegration = graphql.graphqlIntegration;
exports.kafkaIntegration = kafka.kafkaIntegration;
exports.lruMemoizerIntegration = lrumemoizer.lruMemoizerIntegration;
exports.mongoIntegration = mongo.mongoIntegration;
exports.mongooseIntegration = mongoose.mongooseIntegration;
exports.mysqlIntegration = mysql.mysqlIntegration;
exports.mysql2Integration = mysql2.mysql2Integration;
exports.redisIntegration = redis.redisIntegration;
exports.nestIntegration = nest.nestIntegration;
exports.setupNestErrorHandler = nest.setupNestErrorHandler;
exports.postgresIntegration = postgres.postgresIntegration;
exports.prismaIntegration = prisma.prismaIntegration;
exports.hapiIntegration = index$3.hapiIntegration;
exports.setupHapiErrorHandler = index$3.setupHapiErrorHandler;
exports.koaIntegration = koa.koaIntegration;
exports.setupKoaErrorHandler = koa.setupKoaErrorHandler;
exports.connectIntegration = connect.connectIntegration;
exports.setupConnectErrorHandler = connect.setupConnectErrorHandler;
exports.spotlightIntegration = spotlight.spotlightIntegration;
exports.knexIntegration = knex.knexIntegration;
exports.tediousIntegration = tedious.tediousIntegration;
exports.genericPoolIntegration = genericPool.genericPoolIntegration;
exports.dataloaderIntegration = dataloader.dataloaderIntegration;
exports.amqplibIntegration = amqplib.amqplibIntegration;
exports.childProcessIntegration = childProcess.childProcessIntegration;
exports.processThreadBreadcrumbIntegration = childProcess.processThreadBreadcrumbIntegration;
exports.SentryContextManager = contextManager.SentryContextManager;
exports.generateInstrumentOnce = instrument.generateInstrumentOnce;
exports.getDefaultIntegrations = index$4.getDefaultIntegrations;
exports.getDefaultIntegrationsWithoutPerformance = index$4.getDefaultIntegrationsWithoutPerformance;
exports.init = index$4.init;
exports.initWithoutDefaultIntegrations = index$4.initWithoutDefaultIntegrations;
exports.validateOpenTelemetrySetup = index$4.validateOpenTelemetrySetup;
exports.initOpenTelemetry = initOtel.initOpenTelemetry;
exports.preloadOpenTelemetry = initOtel.preloadOpenTelemetry;
exports.getAutoPerformanceIntegrations = index$5.getAutoPerformanceIntegrations;
exports.defaultStackParser = api.defaultStackParser;
exports.getSentryRelease = api.getSentryRelease;
exports.createGetModuleFromFilename = module$1.createGetModuleFromFilename;
exports.makeNodeTransport = http.makeNodeTransport;
exports.NodeClient = client.NodeClient;
exports.cron = index$6.cron;
exports.DEFAULT_USER_INCLUDES = core.DEFAULT_USER_INCLUDES;
exports.SDK_VERSION = core.SDK_VERSION;
exports.SEMANTIC_ATTRIBUTE_SENTRY_OP = core.SEMANTIC_ATTRIBUTE_SENTRY_OP;
exports.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN = core.SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN;
exports.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE = core.SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE;
exports.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE = core.SEMANTIC_ATTRIBUTE_SENTRY_SOURCE;
exports.Scope = core.Scope;
exports.addBreadcrumb = core.addBreadcrumb;
exports.addEventProcessor = core.addEventProcessor;
exports.addIntegration = core.addIntegration;
exports.addRequestDataToEvent = core.addRequestDataToEvent;
exports.captureCheckIn = core.captureCheckIn;
exports.captureConsoleIntegration = core.captureConsoleIntegration;
exports.captureEvent = core.captureEvent;
exports.captureException = core.captureException;
exports.captureFeedback = core.captureFeedback;
exports.captureMessage = core.captureMessage;
exports.captureSession = core.captureSession;
exports.close = core.close;
exports.createTransport = core.createTransport;
exports.debugIntegration = core.debugIntegration;
exports.dedupeIntegration = core.dedupeIntegration;
exports.endSession = core.endSession;
exports.extraErrorDataIntegration = core.extraErrorDataIntegration;
exports.extractRequestData = core.extractRequestData;
exports.flush = core.flush;
exports.functionToStringIntegration = core.functionToStringIntegration;
exports.getActiveSpan = core.getActiveSpan;
exports.getClient = core.getClient;
exports.getCurrentHub = core.getCurrentHub;
exports.getCurrentScope = core.getCurrentScope;
exports.getGlobalScope = core.getGlobalScope;
exports.getIsolationScope = core.getIsolationScope;
exports.getRootSpan = core.getRootSpan;
exports.getSpanDescendants = core.getSpanDescendants;
exports.getSpanStatusFromHttpCode = core.getSpanStatusFromHttpCode;
exports.getTraceData = core.getTraceData;
exports.getTraceMetaTags = core.getTraceMetaTags;
exports.inboundFiltersIntegration = core.inboundFiltersIntegration;
exports.isInitialized = core.isInitialized;
exports.lastEventId = core.lastEventId;
exports.linkedErrorsIntegration = core.linkedErrorsIntegration;
exports.metrics = core.metricsDefault;
exports.parameterize = core.parameterize;
exports.profiler = core.profiler;
exports.requestDataIntegration = core.requestDataIntegration;
exports.rewriteFramesIntegration = core.rewriteFramesIntegration;
exports.sessionTimingIntegration = core.sessionTimingIntegration;
exports.setContext = core.setContext;
exports.setCurrentClient = core.setCurrentClient;
exports.setExtra = core.setExtra;
exports.setExtras = core.setExtras;
exports.setHttpStatus = core.setHttpStatus;
exports.setMeasurement = core.setMeasurement;
exports.setTag = core.setTag;
exports.setTags = core.setTags;
exports.setUser = core.setUser;
exports.spanToBaggageHeader = core.spanToBaggageHeader;
exports.spanToJSON = core.spanToJSON;
exports.spanToTraceHeader = core.spanToTraceHeader;
exports.startInactiveSpan = core.startInactiveSpan;
exports.startNewTrace = core.startNewTrace;
exports.startSession = core.startSession;
exports.startSpan = core.startSpan;
exports.startSpanManual = core.startSpanManual;
exports.suppressTracing = core.suppressTracing;
exports.trpcMiddleware = core.trpcMiddleware;
exports.withActiveSpan = core.withActiveSpan;
exports.withIsolationScope = core.withIsolationScope;
exports.withMonitor = core.withMonitor;
exports.withScope = core.withScope;
exports.zodErrorsIntegration = core.zodErrorsIntegration;
exports.addOpenTelemetryInstrumentation = opentelemetry.addOpenTelemetryInstrumentation;
exports.continueTrace = opentelemetry.continueTrace;
exports.setNodeAsyncContextStrategy = opentelemetry.setOpenTelemetryContextAsyncContextStrategy; //# sourceMappingURL=index.js.map
}}),

};

//# sourceMappingURL=5156f_%40sentry_node_build_cjs_52a9a52a._.js.map