module.exports = {

"[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index-incubating.js [instrumentation] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ANDROID_STATE_VALUE_BACKGROUND": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ANDROID_STATE_VALUE_BACKGROUND"],
    "ANDROID_STATE_VALUE_CREATED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ANDROID_STATE_VALUE_CREATED"],
    "ANDROID_STATE_VALUE_FOREGROUND": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ANDROID_STATE_VALUE_FOREGROUND"],
    "ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_ABORTED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_ABORTED"],
    "ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_HANDLED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_HANDLED"],
    "ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_SKIPPED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_SKIPPED"],
    "ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_UNHANDLED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_UNHANDLED"],
    "ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ACQUIRED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ACQUIRED"],
    "ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ENDPOINT_LIMITER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ENDPOINT_LIMITER"],
    "ASPNETCORE_RATE_LIMITING_RESULT_VALUE_GLOBAL_LIMITER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ASPNETCORE_RATE_LIMITING_RESULT_VALUE_GLOBAL_LIMITER"],
    "ASPNETCORE_RATE_LIMITING_RESULT_VALUE_REQUEST_CANCELED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ASPNETCORE_RATE_LIMITING_RESULT_VALUE_REQUEST_CANCELED"],
    "ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_FAILURE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_FAILURE"],
    "ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_SUCCESS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_SUCCESS"],
    "ATTR_ANDROID_OS_API_LEVEL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ANDROID_OS_API_LEVEL"],
    "ATTR_ANDROID_STATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ANDROID_STATE"],
    "ATTR_ARTIFACT_ATTESTATION_FILENAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ARTIFACT_ATTESTATION_FILENAME"],
    "ATTR_ARTIFACT_ATTESTATION_HASH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ARTIFACT_ATTESTATION_HASH"],
    "ATTR_ARTIFACT_ATTESTATION_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ARTIFACT_ATTESTATION_ID"],
    "ATTR_ARTIFACT_FILENAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ARTIFACT_FILENAME"],
    "ATTR_ARTIFACT_HASH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ARTIFACT_HASH"],
    "ATTR_ARTIFACT_PURL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ARTIFACT_PURL"],
    "ATTR_ARTIFACT_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ARTIFACT_VERSION"],
    "ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT"],
    "ATTR_ASPNETCORE_DIAGNOSTICS_HANDLER_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ASPNETCORE_DIAGNOSTICS_HANDLER_TYPE"],
    "ATTR_ASPNETCORE_RATE_LIMITING_POLICY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ASPNETCORE_RATE_LIMITING_POLICY"],
    "ATTR_ASPNETCORE_RATE_LIMITING_RESULT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ASPNETCORE_RATE_LIMITING_RESULT"],
    "ATTR_ASPNETCORE_REQUEST_IS_UNHANDLED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ASPNETCORE_REQUEST_IS_UNHANDLED"],
    "ATTR_ASPNETCORE_ROUTING_IS_FALLBACK": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ASPNETCORE_ROUTING_IS_FALLBACK"],
    "ATTR_ASPNETCORE_ROUTING_MATCH_STATUS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ASPNETCORE_ROUTING_MATCH_STATUS"],
    "ATTR_AWS_DYNAMODB_ATTRIBUTES_TO_GET": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_ATTRIBUTES_TO_GET"],
    "ATTR_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS"],
    "ATTR_AWS_DYNAMODB_CONSISTENT_READ": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_CONSISTENT_READ"],
    "ATTR_AWS_DYNAMODB_CONSUMED_CAPACITY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_CONSUMED_CAPACITY"],
    "ATTR_AWS_DYNAMODB_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_COUNT"],
    "ATTR_AWS_DYNAMODB_EXCLUSIVE_START_TABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_EXCLUSIVE_START_TABLE"],
    "ATTR_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES"],
    "ATTR_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES"],
    "ATTR_AWS_DYNAMODB_INDEX_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_INDEX_NAME"],
    "ATTR_AWS_DYNAMODB_ITEM_COLLECTION_METRICS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_ITEM_COLLECTION_METRICS"],
    "ATTR_AWS_DYNAMODB_LIMIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_LIMIT"],
    "ATTR_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES"],
    "ATTR_AWS_DYNAMODB_PROJECTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_PROJECTION"],
    "ATTR_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY"],
    "ATTR_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY"],
    "ATTR_AWS_DYNAMODB_SCANNED_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_SCANNED_COUNT"],
    "ATTR_AWS_DYNAMODB_SCAN_FORWARD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_SCAN_FORWARD"],
    "ATTR_AWS_DYNAMODB_SEGMENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_SEGMENT"],
    "ATTR_AWS_DYNAMODB_SELECT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_SELECT"],
    "ATTR_AWS_DYNAMODB_TABLE_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_TABLE_COUNT"],
    "ATTR_AWS_DYNAMODB_TABLE_NAMES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_TABLE_NAMES"],
    "ATTR_AWS_DYNAMODB_TOTAL_SEGMENTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_DYNAMODB_TOTAL_SEGMENTS"],
    "ATTR_AWS_ECS_CLUSTER_ARN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_ECS_CLUSTER_ARN"],
    "ATTR_AWS_ECS_CONTAINER_ARN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_ECS_CONTAINER_ARN"],
    "ATTR_AWS_ECS_LAUNCHTYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_ECS_LAUNCHTYPE"],
    "ATTR_AWS_ECS_TASK_ARN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_ECS_TASK_ARN"],
    "ATTR_AWS_ECS_TASK_FAMILY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_ECS_TASK_FAMILY"],
    "ATTR_AWS_ECS_TASK_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_ECS_TASK_ID"],
    "ATTR_AWS_ECS_TASK_REVISION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_ECS_TASK_REVISION"],
    "ATTR_AWS_EKS_CLUSTER_ARN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_EKS_CLUSTER_ARN"],
    "ATTR_AWS_LAMBDA_INVOKED_ARN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_LAMBDA_INVOKED_ARN"],
    "ATTR_AWS_LOG_GROUP_ARNS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_LOG_GROUP_ARNS"],
    "ATTR_AWS_LOG_GROUP_NAMES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_LOG_GROUP_NAMES"],
    "ATTR_AWS_LOG_STREAM_ARNS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_LOG_STREAM_ARNS"],
    "ATTR_AWS_LOG_STREAM_NAMES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_LOG_STREAM_NAMES"],
    "ATTR_AWS_REQUEST_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_REQUEST_ID"],
    "ATTR_AWS_S3_BUCKET": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_S3_BUCKET"],
    "ATTR_AWS_S3_COPY_SOURCE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_S3_COPY_SOURCE"],
    "ATTR_AWS_S3_DELETE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_S3_DELETE"],
    "ATTR_AWS_S3_KEY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_S3_KEY"],
    "ATTR_AWS_S3_PART_NUMBER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_S3_PART_NUMBER"],
    "ATTR_AWS_S3_UPLOAD_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AWS_S3_UPLOAD_ID"],
    "ATTR_AZ_SERVICE_REQUEST_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_AZ_SERVICE_REQUEST_ID"],
    "ATTR_BROWSER_BRANDS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_BROWSER_BRANDS"],
    "ATTR_BROWSER_LANGUAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_BROWSER_LANGUAGE"],
    "ATTR_BROWSER_MOBILE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_BROWSER_MOBILE"],
    "ATTR_BROWSER_PLATFORM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_BROWSER_PLATFORM"],
    "ATTR_CICD_PIPELINE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CICD_PIPELINE_NAME"],
    "ATTR_CICD_PIPELINE_RUN_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CICD_PIPELINE_RUN_ID"],
    "ATTR_CICD_PIPELINE_TASK_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CICD_PIPELINE_TASK_NAME"],
    "ATTR_CICD_PIPELINE_TASK_RUN_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CICD_PIPELINE_TASK_RUN_ID"],
    "ATTR_CICD_PIPELINE_TASK_RUN_URL_FULL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CICD_PIPELINE_TASK_RUN_URL_FULL"],
    "ATTR_CICD_PIPELINE_TASK_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CICD_PIPELINE_TASK_TYPE"],
    "ATTR_CLIENT_ADDRESS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CLIENT_ADDRESS"],
    "ATTR_CLIENT_PORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CLIENT_PORT"],
    "ATTR_CLOUDEVENTS_EVENT_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CLOUDEVENTS_EVENT_ID"],
    "ATTR_CLOUDEVENTS_EVENT_SOURCE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CLOUDEVENTS_EVENT_SOURCE"],
    "ATTR_CLOUDEVENTS_EVENT_SPEC_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CLOUDEVENTS_EVENT_SPEC_VERSION"],
    "ATTR_CLOUDEVENTS_EVENT_SUBJECT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CLOUDEVENTS_EVENT_SUBJECT"],
    "ATTR_CLOUDEVENTS_EVENT_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CLOUDEVENTS_EVENT_TYPE"],
    "ATTR_CLOUD_ACCOUNT_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CLOUD_ACCOUNT_ID"],
    "ATTR_CLOUD_AVAILABILITY_ZONE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CLOUD_AVAILABILITY_ZONE"],
    "ATTR_CLOUD_PLATFORM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CLOUD_PLATFORM"],
    "ATTR_CLOUD_PROVIDER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CLOUD_PROVIDER"],
    "ATTR_CLOUD_REGION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CLOUD_REGION"],
    "ATTR_CLOUD_RESOURCE_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CLOUD_RESOURCE_ID"],
    "ATTR_CODE_COLUMN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CODE_COLUMN"],
    "ATTR_CODE_FILEPATH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CODE_FILEPATH"],
    "ATTR_CODE_FUNCTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CODE_FUNCTION"],
    "ATTR_CODE_LINENO": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CODE_LINENO"],
    "ATTR_CODE_NAMESPACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CODE_NAMESPACE"],
    "ATTR_CODE_STACKTRACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CODE_STACKTRACE"],
    "ATTR_CONTAINER_COMMAND": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CONTAINER_COMMAND"],
    "ATTR_CONTAINER_COMMAND_ARGS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CONTAINER_COMMAND_ARGS"],
    "ATTR_CONTAINER_COMMAND_LINE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CONTAINER_COMMAND_LINE"],
    "ATTR_CONTAINER_CPU_STATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CONTAINER_CPU_STATE"],
    "ATTR_CONTAINER_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CONTAINER_ID"],
    "ATTR_CONTAINER_IMAGE_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CONTAINER_IMAGE_ID"],
    "ATTR_CONTAINER_IMAGE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CONTAINER_IMAGE_NAME"],
    "ATTR_CONTAINER_IMAGE_REPO_DIGESTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CONTAINER_IMAGE_REPO_DIGESTS"],
    "ATTR_CONTAINER_IMAGE_TAGS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CONTAINER_IMAGE_TAGS"],
    "ATTR_CONTAINER_LABEL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CONTAINER_LABEL"],
    "ATTR_CONTAINER_LABELS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CONTAINER_LABELS"],
    "ATTR_CONTAINER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CONTAINER_NAME"],
    "ATTR_CONTAINER_RUNTIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CONTAINER_RUNTIME"],
    "ATTR_CPU_MODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_CPU_MODE"],
    "ATTR_DB_CASSANDRA_CONSISTENCY_LEVEL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_CASSANDRA_CONSISTENCY_LEVEL"],
    "ATTR_DB_CASSANDRA_COORDINATOR_DC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_CASSANDRA_COORDINATOR_DC"],
    "ATTR_DB_CASSANDRA_COORDINATOR_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_CASSANDRA_COORDINATOR_ID"],
    "ATTR_DB_CASSANDRA_IDEMPOTENCE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_CASSANDRA_IDEMPOTENCE"],
    "ATTR_DB_CASSANDRA_PAGE_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_CASSANDRA_PAGE_SIZE"],
    "ATTR_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT"],
    "ATTR_DB_CASSANDRA_TABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_CASSANDRA_TABLE"],
    "ATTR_DB_CLIENT_CONNECTIONS_POOL_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_CLIENT_CONNECTIONS_POOL_NAME"],
    "ATTR_DB_CLIENT_CONNECTIONS_STATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_CLIENT_CONNECTIONS_STATE"],
    "ATTR_DB_CLIENT_CONNECTION_POOL_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_CLIENT_CONNECTION_POOL_NAME"],
    "ATTR_DB_CLIENT_CONNECTION_STATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_CLIENT_CONNECTION_STATE"],
    "ATTR_DB_COLLECTION_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_COLLECTION_NAME"],
    "ATTR_DB_CONNECTION_STRING": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_CONNECTION_STRING"],
    "ATTR_DB_COSMOSDB_CLIENT_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_COSMOSDB_CLIENT_ID"],
    "ATTR_DB_COSMOSDB_CONNECTION_MODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_COSMOSDB_CONNECTION_MODE"],
    "ATTR_DB_COSMOSDB_CONTAINER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_COSMOSDB_CONTAINER"],
    "ATTR_DB_COSMOSDB_OPERATION_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_COSMOSDB_OPERATION_TYPE"],
    "ATTR_DB_COSMOSDB_REQUEST_CHARGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_COSMOSDB_REQUEST_CHARGE"],
    "ATTR_DB_COSMOSDB_REQUEST_CONTENT_LENGTH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_COSMOSDB_REQUEST_CONTENT_LENGTH"],
    "ATTR_DB_COSMOSDB_STATUS_CODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_COSMOSDB_STATUS_CODE"],
    "ATTR_DB_COSMOSDB_SUB_STATUS_CODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_COSMOSDB_SUB_STATUS_CODE"],
    "ATTR_DB_ELASTICSEARCH_CLUSTER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_ELASTICSEARCH_CLUSTER_NAME"],
    "ATTR_DB_ELASTICSEARCH_NODE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_ELASTICSEARCH_NODE_NAME"],
    "ATTR_DB_ELASTICSEARCH_PATH_PARTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_ELASTICSEARCH_PATH_PARTS"],
    "ATTR_DB_INSTANCE_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_INSTANCE_ID"],
    "ATTR_DB_JDBC_DRIVER_CLASSNAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_JDBC_DRIVER_CLASSNAME"],
    "ATTR_DB_MONGODB_COLLECTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_MONGODB_COLLECTION"],
    "ATTR_DB_MSSQL_INSTANCE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_MSSQL_INSTANCE_NAME"],
    "ATTR_DB_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_NAME"],
    "ATTR_DB_NAMESPACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_NAMESPACE"],
    "ATTR_DB_OPERATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_OPERATION"],
    "ATTR_DB_OPERATION_BATCH_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_OPERATION_BATCH_SIZE"],
    "ATTR_DB_OPERATION_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_OPERATION_NAME"],
    "ATTR_DB_QUERY_PARAMETER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_QUERY_PARAMETER"],
    "ATTR_DB_QUERY_TEXT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_QUERY_TEXT"],
    "ATTR_DB_REDIS_DATABASE_INDEX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_REDIS_DATABASE_INDEX"],
    "ATTR_DB_SQL_TABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_SQL_TABLE"],
    "ATTR_DB_STATEMENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_STATEMENT"],
    "ATTR_DB_SYSTEM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_SYSTEM"],
    "ATTR_DB_USER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DB_USER"],
    "ATTR_DEPLOYMENT_ENVIRONMENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DEPLOYMENT_ENVIRONMENT"],
    "ATTR_DEPLOYMENT_ENVIRONMENT_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DEPLOYMENT_ENVIRONMENT_NAME"],
    "ATTR_DEPLOYMENT_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DEPLOYMENT_ID"],
    "ATTR_DEPLOYMENT_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DEPLOYMENT_NAME"],
    "ATTR_DEPLOYMENT_STATUS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DEPLOYMENT_STATUS"],
    "ATTR_DESTINATION_ADDRESS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DESTINATION_ADDRESS"],
    "ATTR_DESTINATION_PORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DESTINATION_PORT"],
    "ATTR_DEVICE_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DEVICE_ID"],
    "ATTR_DEVICE_MANUFACTURER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DEVICE_MANUFACTURER"],
    "ATTR_DEVICE_MODEL_IDENTIFIER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DEVICE_MODEL_IDENTIFIER"],
    "ATTR_DEVICE_MODEL_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DEVICE_MODEL_NAME"],
    "ATTR_DISK_IO_DIRECTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DISK_IO_DIRECTION"],
    "ATTR_DNS_QUESTION_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_DNS_QUESTION_NAME"],
    "ATTR_ENDUSER_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ENDUSER_ID"],
    "ATTR_ENDUSER_ROLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ENDUSER_ROLE"],
    "ATTR_ENDUSER_SCOPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ENDUSER_SCOPE"],
    "ATTR_ERROR_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_ERROR_TYPE"],
    "ATTR_EVENT_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_EVENT_NAME"],
    "ATTR_EXCEPTION_ESCAPED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_EXCEPTION_ESCAPED"],
    "ATTR_EXCEPTION_MESSAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_EXCEPTION_MESSAGE"],
    "ATTR_EXCEPTION_STACKTRACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_EXCEPTION_STACKTRACE"],
    "ATTR_EXCEPTION_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_EXCEPTION_TYPE"],
    "ATTR_FAAS_COLDSTART": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FAAS_COLDSTART"],
    "ATTR_FAAS_CRON": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FAAS_CRON"],
    "ATTR_FAAS_DOCUMENT_COLLECTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FAAS_DOCUMENT_COLLECTION"],
    "ATTR_FAAS_DOCUMENT_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FAAS_DOCUMENT_NAME"],
    "ATTR_FAAS_DOCUMENT_OPERATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FAAS_DOCUMENT_OPERATION"],
    "ATTR_FAAS_DOCUMENT_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FAAS_DOCUMENT_TIME"],
    "ATTR_FAAS_INSTANCE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FAAS_INSTANCE"],
    "ATTR_FAAS_INVOCATION_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FAAS_INVOCATION_ID"],
    "ATTR_FAAS_INVOKED_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FAAS_INVOKED_NAME"],
    "ATTR_FAAS_INVOKED_PROVIDER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FAAS_INVOKED_PROVIDER"],
    "ATTR_FAAS_INVOKED_REGION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FAAS_INVOKED_REGION"],
    "ATTR_FAAS_MAX_MEMORY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FAAS_MAX_MEMORY"],
    "ATTR_FAAS_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FAAS_NAME"],
    "ATTR_FAAS_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FAAS_TIME"],
    "ATTR_FAAS_TRIGGER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FAAS_TRIGGER"],
    "ATTR_FAAS_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FAAS_VERSION"],
    "ATTR_FEATURE_FLAG_KEY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FEATURE_FLAG_KEY"],
    "ATTR_FEATURE_FLAG_PROVIDER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FEATURE_FLAG_PROVIDER_NAME"],
    "ATTR_FEATURE_FLAG_VARIANT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FEATURE_FLAG_VARIANT"],
    "ATTR_FILE_DIRECTORY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FILE_DIRECTORY"],
    "ATTR_FILE_EXTENSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FILE_EXTENSION"],
    "ATTR_FILE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FILE_NAME"],
    "ATTR_FILE_PATH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FILE_PATH"],
    "ATTR_FILE_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_FILE_SIZE"],
    "ATTR_GCP_CLIENT_SERVICE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GCP_CLIENT_SERVICE"],
    "ATTR_GCP_CLOUD_RUN_JOB_EXECUTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GCP_CLOUD_RUN_JOB_EXECUTION"],
    "ATTR_GCP_CLOUD_RUN_JOB_TASK_INDEX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GCP_CLOUD_RUN_JOB_TASK_INDEX"],
    "ATTR_GCP_GCE_INSTANCE_HOSTNAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GCP_GCE_INSTANCE_HOSTNAME"],
    "ATTR_GCP_GCE_INSTANCE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GCP_GCE_INSTANCE_NAME"],
    "ATTR_GEN_AI_COMPLETION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_COMPLETION"],
    "ATTR_GEN_AI_OPERATION_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_OPERATION_NAME"],
    "ATTR_GEN_AI_PROMPT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_PROMPT"],
    "ATTR_GEN_AI_REQUEST_FREQUENCY_PENALTY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_REQUEST_FREQUENCY_PENALTY"],
    "ATTR_GEN_AI_REQUEST_MAX_TOKENS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_REQUEST_MAX_TOKENS"],
    "ATTR_GEN_AI_REQUEST_MODEL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_REQUEST_MODEL"],
    "ATTR_GEN_AI_REQUEST_PRESENCE_PENALTY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_REQUEST_PRESENCE_PENALTY"],
    "ATTR_GEN_AI_REQUEST_STOP_SEQUENCES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_REQUEST_STOP_SEQUENCES"],
    "ATTR_GEN_AI_REQUEST_TEMPERATURE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_REQUEST_TEMPERATURE"],
    "ATTR_GEN_AI_REQUEST_TOP_K": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_REQUEST_TOP_K"],
    "ATTR_GEN_AI_REQUEST_TOP_P": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_REQUEST_TOP_P"],
    "ATTR_GEN_AI_RESPONSE_FINISH_REASONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_RESPONSE_FINISH_REASONS"],
    "ATTR_GEN_AI_RESPONSE_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_RESPONSE_ID"],
    "ATTR_GEN_AI_RESPONSE_MODEL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_RESPONSE_MODEL"],
    "ATTR_GEN_AI_SYSTEM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_SYSTEM"],
    "ATTR_GEN_AI_TOKEN_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_TOKEN_TYPE"],
    "ATTR_GEN_AI_USAGE_COMPLETION_TOKENS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_USAGE_COMPLETION_TOKENS"],
    "ATTR_GEN_AI_USAGE_INPUT_TOKENS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_USAGE_INPUT_TOKENS"],
    "ATTR_GEN_AI_USAGE_OUTPUT_TOKENS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_USAGE_OUTPUT_TOKENS"],
    "ATTR_GEN_AI_USAGE_PROMPT_TOKENS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GEN_AI_USAGE_PROMPT_TOKENS"],
    "ATTR_GO_MEMORY_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GO_MEMORY_TYPE"],
    "ATTR_GRAPHQL_DOCUMENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GRAPHQL_DOCUMENT"],
    "ATTR_GRAPHQL_OPERATION_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GRAPHQL_OPERATION_NAME"],
    "ATTR_GRAPHQL_OPERATION_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_GRAPHQL_OPERATION_TYPE"],
    "ATTR_HEROKU_APP_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HEROKU_APP_ID"],
    "ATTR_HEROKU_RELEASE_COMMIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HEROKU_RELEASE_COMMIT"],
    "ATTR_HEROKU_RELEASE_CREATION_TIMESTAMP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HEROKU_RELEASE_CREATION_TIMESTAMP"],
    "ATTR_HOST_ARCH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HOST_ARCH"],
    "ATTR_HOST_CPU_CACHE_L2_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HOST_CPU_CACHE_L2_SIZE"],
    "ATTR_HOST_CPU_FAMILY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HOST_CPU_FAMILY"],
    "ATTR_HOST_CPU_MODEL_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HOST_CPU_MODEL_ID"],
    "ATTR_HOST_CPU_MODEL_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HOST_CPU_MODEL_NAME"],
    "ATTR_HOST_CPU_STEPPING": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HOST_CPU_STEPPING"],
    "ATTR_HOST_CPU_VENDOR_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HOST_CPU_VENDOR_ID"],
    "ATTR_HOST_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HOST_ID"],
    "ATTR_HOST_IMAGE_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HOST_IMAGE_ID"],
    "ATTR_HOST_IMAGE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HOST_IMAGE_NAME"],
    "ATTR_HOST_IMAGE_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HOST_IMAGE_VERSION"],
    "ATTR_HOST_IP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HOST_IP"],
    "ATTR_HOST_MAC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HOST_MAC"],
    "ATTR_HOST_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HOST_NAME"],
    "ATTR_HOST_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HOST_TYPE"],
    "ATTR_HTTP_CLIENT_IP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_CLIENT_IP"],
    "ATTR_HTTP_CONNECTION_STATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_CONNECTION_STATE"],
    "ATTR_HTTP_FLAVOR": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_FLAVOR"],
    "ATTR_HTTP_HOST": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_HOST"],
    "ATTR_HTTP_METHOD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_METHOD"],
    "ATTR_HTTP_REQUEST_BODY_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_REQUEST_BODY_SIZE"],
    "ATTR_HTTP_REQUEST_CONTENT_LENGTH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_REQUEST_CONTENT_LENGTH"],
    "ATTR_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED"],
    "ATTR_HTTP_REQUEST_HEADER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_REQUEST_HEADER"],
    "ATTR_HTTP_REQUEST_METHOD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_REQUEST_METHOD"],
    "ATTR_HTTP_REQUEST_METHOD_ORIGINAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_REQUEST_METHOD_ORIGINAL"],
    "ATTR_HTTP_REQUEST_RESEND_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_REQUEST_RESEND_COUNT"],
    "ATTR_HTTP_REQUEST_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_REQUEST_SIZE"],
    "ATTR_HTTP_RESPONSE_BODY_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_RESPONSE_BODY_SIZE"],
    "ATTR_HTTP_RESPONSE_CONTENT_LENGTH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_RESPONSE_CONTENT_LENGTH"],
    "ATTR_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED"],
    "ATTR_HTTP_RESPONSE_HEADER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_RESPONSE_HEADER"],
    "ATTR_HTTP_RESPONSE_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_RESPONSE_SIZE"],
    "ATTR_HTTP_RESPONSE_STATUS_CODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_RESPONSE_STATUS_CODE"],
    "ATTR_HTTP_ROUTE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_ROUTE"],
    "ATTR_HTTP_SCHEME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_SCHEME"],
    "ATTR_HTTP_SERVER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_SERVER_NAME"],
    "ATTR_HTTP_STATUS_CODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_STATUS_CODE"],
    "ATTR_HTTP_TARGET": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_TARGET"],
    "ATTR_HTTP_URL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_URL"],
    "ATTR_HTTP_USER_AGENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_HTTP_USER_AGENT"],
    "ATTR_IOS_STATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_IOS_STATE"],
    "ATTR_JVM_BUFFER_POOL_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_JVM_BUFFER_POOL_NAME"],
    "ATTR_JVM_GC_ACTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_JVM_GC_ACTION"],
    "ATTR_JVM_GC_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_JVM_GC_NAME"],
    "ATTR_JVM_MEMORY_POOL_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_JVM_MEMORY_POOL_NAME"],
    "ATTR_JVM_MEMORY_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_JVM_MEMORY_TYPE"],
    "ATTR_JVM_THREAD_DAEMON": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_JVM_THREAD_DAEMON"],
    "ATTR_JVM_THREAD_STATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_JVM_THREAD_STATE"],
    "ATTR_K8S_CLUSTER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_CLUSTER_NAME"],
    "ATTR_K8S_CLUSTER_UID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_CLUSTER_UID"],
    "ATTR_K8S_CONTAINER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_CONTAINER_NAME"],
    "ATTR_K8S_CONTAINER_RESTART_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_CONTAINER_RESTART_COUNT"],
    "ATTR_K8S_CONTAINER_STATUS_LAST_TERMINATED_REASON": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_CONTAINER_STATUS_LAST_TERMINATED_REASON"],
    "ATTR_K8S_CRONJOB_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_CRONJOB_NAME"],
    "ATTR_K8S_CRONJOB_UID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_CRONJOB_UID"],
    "ATTR_K8S_DAEMONSET_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_DAEMONSET_NAME"],
    "ATTR_K8S_DAEMONSET_UID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_DAEMONSET_UID"],
    "ATTR_K8S_DEPLOYMENT_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_DEPLOYMENT_NAME"],
    "ATTR_K8S_DEPLOYMENT_UID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_DEPLOYMENT_UID"],
    "ATTR_K8S_JOB_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_JOB_NAME"],
    "ATTR_K8S_JOB_UID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_JOB_UID"],
    "ATTR_K8S_NAMESPACE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_NAMESPACE_NAME"],
    "ATTR_K8S_NODE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_NODE_NAME"],
    "ATTR_K8S_NODE_UID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_NODE_UID"],
    "ATTR_K8S_POD_ANNOTATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_POD_ANNOTATION"],
    "ATTR_K8S_POD_LABEL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_POD_LABEL"],
    "ATTR_K8S_POD_LABELS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_POD_LABELS"],
    "ATTR_K8S_POD_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_POD_NAME"],
    "ATTR_K8S_POD_UID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_POD_UID"],
    "ATTR_K8S_REPLICASET_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_REPLICASET_NAME"],
    "ATTR_K8S_REPLICASET_UID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_REPLICASET_UID"],
    "ATTR_K8S_STATEFULSET_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_STATEFULSET_NAME"],
    "ATTR_K8S_STATEFULSET_UID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_K8S_STATEFULSET_UID"],
    "ATTR_LINUX_MEMORY_SLAB_STATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_LINUX_MEMORY_SLAB_STATE"],
    "ATTR_LOG_FILE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_LOG_FILE_NAME"],
    "ATTR_LOG_FILE_NAME_RESOLVED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_LOG_FILE_NAME_RESOLVED"],
    "ATTR_LOG_FILE_PATH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_LOG_FILE_PATH"],
    "ATTR_LOG_FILE_PATH_RESOLVED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_LOG_FILE_PATH_RESOLVED"],
    "ATTR_LOG_IOSTREAM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_LOG_IOSTREAM"],
    "ATTR_LOG_RECORD_ORIGINAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_LOG_RECORD_ORIGINAL"],
    "ATTR_LOG_RECORD_UID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_LOG_RECORD_UID"],
    "ATTR_MESSAGE_COMPRESSED_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGE_COMPRESSED_SIZE"],
    "ATTR_MESSAGE_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGE_ID"],
    "ATTR_MESSAGE_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGE_TYPE"],
    "ATTR_MESSAGE_UNCOMPRESSED_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGE_UNCOMPRESSED_SIZE"],
    "ATTR_MESSAGING_BATCH_MESSAGE_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_BATCH_MESSAGE_COUNT"],
    "ATTR_MESSAGING_CLIENT_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_CLIENT_ID"],
    "ATTR_MESSAGING_CONSUMER_GROUP_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_CONSUMER_GROUP_NAME"],
    "ATTR_MESSAGING_DESTINATION_ANONYMOUS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_DESTINATION_ANONYMOUS"],
    "ATTR_MESSAGING_DESTINATION_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_DESTINATION_NAME"],
    "ATTR_MESSAGING_DESTINATION_PARTITION_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_DESTINATION_PARTITION_ID"],
    "ATTR_MESSAGING_DESTINATION_PUBLISH_ANONYMOUS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_DESTINATION_PUBLISH_ANONYMOUS"],
    "ATTR_MESSAGING_DESTINATION_PUBLISH_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_DESTINATION_PUBLISH_NAME"],
    "ATTR_MESSAGING_DESTINATION_SUBSCRIPTION_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_DESTINATION_SUBSCRIPTION_NAME"],
    "ATTR_MESSAGING_DESTINATION_TEMPLATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_DESTINATION_TEMPLATE"],
    "ATTR_MESSAGING_DESTINATION_TEMPORARY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_DESTINATION_TEMPORARY"],
    "ATTR_MESSAGING_EVENTHUBS_CONSUMER_GROUP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_EVENTHUBS_CONSUMER_GROUP"],
    "ATTR_MESSAGING_EVENTHUBS_MESSAGE_ENQUEUED_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_EVENTHUBS_MESSAGE_ENQUEUED_TIME"],
    "ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_ACK_DEADLINE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_ACK_DEADLINE"],
    "ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_ACK_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_ACK_ID"],
    "ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_DELIVERY_ATTEMPT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_DELIVERY_ATTEMPT"],
    "ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_ORDERING_KEY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_ORDERING_KEY"],
    "ATTR_MESSAGING_KAFKA_CONSUMER_GROUP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_KAFKA_CONSUMER_GROUP"],
    "ATTR_MESSAGING_KAFKA_DESTINATION_PARTITION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_KAFKA_DESTINATION_PARTITION"],
    "ATTR_MESSAGING_KAFKA_MESSAGE_KEY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_KAFKA_MESSAGE_KEY"],
    "ATTR_MESSAGING_KAFKA_MESSAGE_OFFSET": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_KAFKA_MESSAGE_OFFSET"],
    "ATTR_MESSAGING_KAFKA_MESSAGE_TOMBSTONE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_KAFKA_MESSAGE_TOMBSTONE"],
    "ATTR_MESSAGING_KAFKA_OFFSET": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_KAFKA_OFFSET"],
    "ATTR_MESSAGING_MESSAGE_BODY_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_MESSAGE_BODY_SIZE"],
    "ATTR_MESSAGING_MESSAGE_CONVERSATION_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_MESSAGE_CONVERSATION_ID"],
    "ATTR_MESSAGING_MESSAGE_ENVELOPE_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_MESSAGE_ENVELOPE_SIZE"],
    "ATTR_MESSAGING_MESSAGE_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_MESSAGE_ID"],
    "ATTR_MESSAGING_OPERATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_OPERATION"],
    "ATTR_MESSAGING_OPERATION_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_OPERATION_NAME"],
    "ATTR_MESSAGING_OPERATION_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_OPERATION_TYPE"],
    "ATTR_MESSAGING_RABBITMQ_DESTINATION_ROUTING_KEY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_RABBITMQ_DESTINATION_ROUTING_KEY"],
    "ATTR_MESSAGING_RABBITMQ_MESSAGE_DELIVERY_TAG": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_RABBITMQ_MESSAGE_DELIVERY_TAG"],
    "ATTR_MESSAGING_ROCKETMQ_CLIENT_GROUP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_ROCKETMQ_CLIENT_GROUP"],
    "ATTR_MESSAGING_ROCKETMQ_CONSUMPTION_MODEL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_ROCKETMQ_CONSUMPTION_MODEL"],
    "ATTR_MESSAGING_ROCKETMQ_MESSAGE_DELAY_TIME_LEVEL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_ROCKETMQ_MESSAGE_DELAY_TIME_LEVEL"],
    "ATTR_MESSAGING_ROCKETMQ_MESSAGE_DELIVERY_TIMESTAMP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_ROCKETMQ_MESSAGE_DELIVERY_TIMESTAMP"],
    "ATTR_MESSAGING_ROCKETMQ_MESSAGE_GROUP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_ROCKETMQ_MESSAGE_GROUP"],
    "ATTR_MESSAGING_ROCKETMQ_MESSAGE_KEYS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_ROCKETMQ_MESSAGE_KEYS"],
    "ATTR_MESSAGING_ROCKETMQ_MESSAGE_TAG": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_ROCKETMQ_MESSAGE_TAG"],
    "ATTR_MESSAGING_ROCKETMQ_MESSAGE_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_ROCKETMQ_MESSAGE_TYPE"],
    "ATTR_MESSAGING_ROCKETMQ_NAMESPACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_ROCKETMQ_NAMESPACE"],
    "ATTR_MESSAGING_SERVICEBUS_DESTINATION_SUBSCRIPTION_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_SERVICEBUS_DESTINATION_SUBSCRIPTION_NAME"],
    "ATTR_MESSAGING_SERVICEBUS_DISPOSITION_STATUS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_SERVICEBUS_DISPOSITION_STATUS"],
    "ATTR_MESSAGING_SERVICEBUS_MESSAGE_DELIVERY_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_SERVICEBUS_MESSAGE_DELIVERY_COUNT"],
    "ATTR_MESSAGING_SERVICEBUS_MESSAGE_ENQUEUED_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_SERVICEBUS_MESSAGE_ENQUEUED_TIME"],
    "ATTR_MESSAGING_SYSTEM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_MESSAGING_SYSTEM"],
    "ATTR_NETWORK_CARRIER_ICC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NETWORK_CARRIER_ICC"],
    "ATTR_NETWORK_CARRIER_MCC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NETWORK_CARRIER_MCC"],
    "ATTR_NETWORK_CARRIER_MNC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NETWORK_CARRIER_MNC"],
    "ATTR_NETWORK_CARRIER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NETWORK_CARRIER_NAME"],
    "ATTR_NETWORK_CONNECTION_SUBTYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NETWORK_CONNECTION_SUBTYPE"],
    "ATTR_NETWORK_CONNECTION_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NETWORK_CONNECTION_TYPE"],
    "ATTR_NETWORK_IO_DIRECTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NETWORK_IO_DIRECTION"],
    "ATTR_NETWORK_LOCAL_ADDRESS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NETWORK_LOCAL_ADDRESS"],
    "ATTR_NETWORK_LOCAL_PORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NETWORK_LOCAL_PORT"],
    "ATTR_NETWORK_PEER_ADDRESS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NETWORK_PEER_ADDRESS"],
    "ATTR_NETWORK_PEER_PORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NETWORK_PEER_PORT"],
    "ATTR_NETWORK_PROTOCOL_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NETWORK_PROTOCOL_NAME"],
    "ATTR_NETWORK_PROTOCOL_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NETWORK_PROTOCOL_VERSION"],
    "ATTR_NETWORK_TRANSPORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NETWORK_TRANSPORT"],
    "ATTR_NETWORK_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NETWORK_TYPE"],
    "ATTR_NET_HOST_IP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NET_HOST_IP"],
    "ATTR_NET_HOST_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NET_HOST_NAME"],
    "ATTR_NET_HOST_PORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NET_HOST_PORT"],
    "ATTR_NET_PEER_IP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NET_PEER_IP"],
    "ATTR_NET_PEER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NET_PEER_NAME"],
    "ATTR_NET_PEER_PORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NET_PEER_PORT"],
    "ATTR_NET_PROTOCOL_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NET_PROTOCOL_NAME"],
    "ATTR_NET_PROTOCOL_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NET_PROTOCOL_VERSION"],
    "ATTR_NET_SOCK_FAMILY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NET_SOCK_FAMILY"],
    "ATTR_NET_SOCK_HOST_ADDR": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NET_SOCK_HOST_ADDR"],
    "ATTR_NET_SOCK_HOST_PORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NET_SOCK_HOST_PORT"],
    "ATTR_NET_SOCK_PEER_ADDR": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NET_SOCK_PEER_ADDR"],
    "ATTR_NET_SOCK_PEER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NET_SOCK_PEER_NAME"],
    "ATTR_NET_SOCK_PEER_PORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NET_SOCK_PEER_PORT"],
    "ATTR_NET_TRANSPORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_NET_TRANSPORT"],
    "ATTR_OCI_MANIFEST_DIGEST": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_OCI_MANIFEST_DIGEST"],
    "ATTR_OPENTRACING_REF_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_OPENTRACING_REF_TYPE"],
    "ATTR_OS_BUILD_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_OS_BUILD_ID"],
    "ATTR_OS_DESCRIPTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_OS_DESCRIPTION"],
    "ATTR_OS_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_OS_NAME"],
    "ATTR_OS_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_OS_TYPE"],
    "ATTR_OS_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_OS_VERSION"],
    "ATTR_OTEL_LIBRARY_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_OTEL_LIBRARY_NAME"],
    "ATTR_OTEL_LIBRARY_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_OTEL_LIBRARY_VERSION"],
    "ATTR_OTEL_SCOPE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_OTEL_SCOPE_NAME"],
    "ATTR_OTEL_SCOPE_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_OTEL_SCOPE_VERSION"],
    "ATTR_OTEL_STATUS_CODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_OTEL_STATUS_CODE"],
    "ATTR_OTEL_STATUS_DESCRIPTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_OTEL_STATUS_DESCRIPTION"],
    "ATTR_PEER_SERVICE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PEER_SERVICE"],
    "ATTR_POOL_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_POOL_NAME"],
    "ATTR_PROCESS_COMMAND": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_COMMAND"],
    "ATTR_PROCESS_COMMAND_ARGS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_COMMAND_ARGS"],
    "ATTR_PROCESS_COMMAND_LINE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_COMMAND_LINE"],
    "ATTR_PROCESS_CONTEXT_SWITCH_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_CONTEXT_SWITCH_TYPE"],
    "ATTR_PROCESS_CPU_STATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_CPU_STATE"],
    "ATTR_PROCESS_CREATION_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_CREATION_TIME"],
    "ATTR_PROCESS_EXECUTABLE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_EXECUTABLE_NAME"],
    "ATTR_PROCESS_EXECUTABLE_PATH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_EXECUTABLE_PATH"],
    "ATTR_PROCESS_EXIT_CODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_EXIT_CODE"],
    "ATTR_PROCESS_EXIT_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_EXIT_TIME"],
    "ATTR_PROCESS_GROUP_LEADER_PID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_GROUP_LEADER_PID"],
    "ATTR_PROCESS_INTERACTIVE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_INTERACTIVE"],
    "ATTR_PROCESS_OWNER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_OWNER"],
    "ATTR_PROCESS_PAGING_FAULT_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_PAGING_FAULT_TYPE"],
    "ATTR_PROCESS_PARENT_PID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_PARENT_PID"],
    "ATTR_PROCESS_PID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_PID"],
    "ATTR_PROCESS_REAL_USER_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_REAL_USER_ID"],
    "ATTR_PROCESS_REAL_USER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_REAL_USER_NAME"],
    "ATTR_PROCESS_RUNTIME_DESCRIPTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_RUNTIME_DESCRIPTION"],
    "ATTR_PROCESS_RUNTIME_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_RUNTIME_NAME"],
    "ATTR_PROCESS_RUNTIME_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_RUNTIME_VERSION"],
    "ATTR_PROCESS_SAVED_USER_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_SAVED_USER_ID"],
    "ATTR_PROCESS_SAVED_USER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_SAVED_USER_NAME"],
    "ATTR_PROCESS_SESSION_LEADER_PID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_SESSION_LEADER_PID"],
    "ATTR_PROCESS_USER_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_USER_ID"],
    "ATTR_PROCESS_USER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_USER_NAME"],
    "ATTR_PROCESS_VPID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_PROCESS_VPID"],
    "ATTR_RPC_CONNECT_RPC_ERROR_CODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_RPC_CONNECT_RPC_ERROR_CODE"],
    "ATTR_RPC_CONNECT_RPC_REQUEST_METADATA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_RPC_CONNECT_RPC_REQUEST_METADATA"],
    "ATTR_RPC_CONNECT_RPC_RESPONSE_METADATA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_RPC_CONNECT_RPC_RESPONSE_METADATA"],
    "ATTR_RPC_GRPC_REQUEST_METADATA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_RPC_GRPC_REQUEST_METADATA"],
    "ATTR_RPC_GRPC_RESPONSE_METADATA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_RPC_GRPC_RESPONSE_METADATA"],
    "ATTR_RPC_GRPC_STATUS_CODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_RPC_GRPC_STATUS_CODE"],
    "ATTR_RPC_JSONRPC_ERROR_CODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_RPC_JSONRPC_ERROR_CODE"],
    "ATTR_RPC_JSONRPC_ERROR_MESSAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_RPC_JSONRPC_ERROR_MESSAGE"],
    "ATTR_RPC_JSONRPC_REQUEST_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_RPC_JSONRPC_REQUEST_ID"],
    "ATTR_RPC_JSONRPC_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_RPC_JSONRPC_VERSION"],
    "ATTR_RPC_MESSAGE_COMPRESSED_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_RPC_MESSAGE_COMPRESSED_SIZE"],
    "ATTR_RPC_MESSAGE_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_RPC_MESSAGE_ID"],
    "ATTR_RPC_MESSAGE_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_RPC_MESSAGE_TYPE"],
    "ATTR_RPC_MESSAGE_UNCOMPRESSED_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_RPC_MESSAGE_UNCOMPRESSED_SIZE"],
    "ATTR_RPC_METHOD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_RPC_METHOD"],
    "ATTR_RPC_SERVICE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_RPC_SERVICE"],
    "ATTR_RPC_SYSTEM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_RPC_SYSTEM"],
    "ATTR_SERVER_ADDRESS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SERVER_ADDRESS"],
    "ATTR_SERVER_PORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SERVER_PORT"],
    "ATTR_SERVICE_INSTANCE_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SERVICE_INSTANCE_ID"],
    "ATTR_SERVICE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SERVICE_NAME"],
    "ATTR_SERVICE_NAMESPACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SERVICE_NAMESPACE"],
    "ATTR_SERVICE_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SERVICE_VERSION"],
    "ATTR_SESSION_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SESSION_ID"],
    "ATTR_SESSION_PREVIOUS_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SESSION_PREVIOUS_ID"],
    "ATTR_SIGNALR_CONNECTION_STATUS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SIGNALR_CONNECTION_STATUS"],
    "ATTR_SIGNALR_TRANSPORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SIGNALR_TRANSPORT"],
    "ATTR_SOURCE_ADDRESS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SOURCE_ADDRESS"],
    "ATTR_SOURCE_PORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SOURCE_PORT"],
    "ATTR_STATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_STATE"],
    "ATTR_SYSTEM_CPU_LOGICAL_NUMBER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SYSTEM_CPU_LOGICAL_NUMBER"],
    "ATTR_SYSTEM_CPU_STATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SYSTEM_CPU_STATE"],
    "ATTR_SYSTEM_DEVICE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SYSTEM_DEVICE"],
    "ATTR_SYSTEM_FILESYSTEM_MODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SYSTEM_FILESYSTEM_MODE"],
    "ATTR_SYSTEM_FILESYSTEM_MOUNTPOINT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SYSTEM_FILESYSTEM_MOUNTPOINT"],
    "ATTR_SYSTEM_FILESYSTEM_STATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SYSTEM_FILESYSTEM_STATE"],
    "ATTR_SYSTEM_FILESYSTEM_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SYSTEM_FILESYSTEM_TYPE"],
    "ATTR_SYSTEM_MEMORY_STATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SYSTEM_MEMORY_STATE"],
    "ATTR_SYSTEM_NETWORK_STATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SYSTEM_NETWORK_STATE"],
    "ATTR_SYSTEM_PAGING_DIRECTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SYSTEM_PAGING_DIRECTION"],
    "ATTR_SYSTEM_PAGING_STATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SYSTEM_PAGING_STATE"],
    "ATTR_SYSTEM_PAGING_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SYSTEM_PAGING_TYPE"],
    "ATTR_SYSTEM_PROCESSES_STATUS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SYSTEM_PROCESSES_STATUS"],
    "ATTR_SYSTEM_PROCESS_STATUS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_SYSTEM_PROCESS_STATUS"],
    "ATTR_TELEMETRY_DISTRO_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TELEMETRY_DISTRO_NAME"],
    "ATTR_TELEMETRY_DISTRO_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TELEMETRY_DISTRO_VERSION"],
    "ATTR_TELEMETRY_SDK_LANGUAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TELEMETRY_SDK_LANGUAGE"],
    "ATTR_TELEMETRY_SDK_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TELEMETRY_SDK_NAME"],
    "ATTR_TELEMETRY_SDK_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TELEMETRY_SDK_VERSION"],
    "ATTR_TEST_CASE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TEST_CASE_NAME"],
    "ATTR_TEST_CASE_RESULT_STATUS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TEST_CASE_RESULT_STATUS"],
    "ATTR_TEST_SUITE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TEST_SUITE_NAME"],
    "ATTR_TEST_SUITE_RUN_STATUS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TEST_SUITE_RUN_STATUS"],
    "ATTR_THREAD_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_THREAD_ID"],
    "ATTR_THREAD_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_THREAD_NAME"],
    "ATTR_TLS_CIPHER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_CIPHER"],
    "ATTR_TLS_CLIENT_CERTIFICATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_CLIENT_CERTIFICATE"],
    "ATTR_TLS_CLIENT_CERTIFICATE_CHAIN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_CLIENT_CERTIFICATE_CHAIN"],
    "ATTR_TLS_CLIENT_HASH_MD5": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_CLIENT_HASH_MD5"],
    "ATTR_TLS_CLIENT_HASH_SHA1": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_CLIENT_HASH_SHA1"],
    "ATTR_TLS_CLIENT_HASH_SHA256": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_CLIENT_HASH_SHA256"],
    "ATTR_TLS_CLIENT_ISSUER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_CLIENT_ISSUER"],
    "ATTR_TLS_CLIENT_JA3": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_CLIENT_JA3"],
    "ATTR_TLS_CLIENT_NOT_AFTER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_CLIENT_NOT_AFTER"],
    "ATTR_TLS_CLIENT_NOT_BEFORE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_CLIENT_NOT_BEFORE"],
    "ATTR_TLS_CLIENT_SERVER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_CLIENT_SERVER_NAME"],
    "ATTR_TLS_CLIENT_SUBJECT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_CLIENT_SUBJECT"],
    "ATTR_TLS_CLIENT_SUPPORTED_CIPHERS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_CLIENT_SUPPORTED_CIPHERS"],
    "ATTR_TLS_CURVE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_CURVE"],
    "ATTR_TLS_ESTABLISHED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_ESTABLISHED"],
    "ATTR_TLS_NEXT_PROTOCOL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_NEXT_PROTOCOL"],
    "ATTR_TLS_PROTOCOL_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_PROTOCOL_NAME"],
    "ATTR_TLS_PROTOCOL_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_PROTOCOL_VERSION"],
    "ATTR_TLS_RESUMED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_RESUMED"],
    "ATTR_TLS_SERVER_CERTIFICATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_SERVER_CERTIFICATE"],
    "ATTR_TLS_SERVER_CERTIFICATE_CHAIN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_SERVER_CERTIFICATE_CHAIN"],
    "ATTR_TLS_SERVER_HASH_MD5": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_SERVER_HASH_MD5"],
    "ATTR_TLS_SERVER_HASH_SHA1": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_SERVER_HASH_SHA1"],
    "ATTR_TLS_SERVER_HASH_SHA256": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_SERVER_HASH_SHA256"],
    "ATTR_TLS_SERVER_ISSUER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_SERVER_ISSUER"],
    "ATTR_TLS_SERVER_JA3S": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_SERVER_JA3S"],
    "ATTR_TLS_SERVER_NOT_AFTER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_SERVER_NOT_AFTER"],
    "ATTR_TLS_SERVER_NOT_BEFORE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_SERVER_NOT_BEFORE"],
    "ATTR_TLS_SERVER_SUBJECT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_TLS_SERVER_SUBJECT"],
    "ATTR_URL_DOMAIN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_URL_DOMAIN"],
    "ATTR_URL_EXTENSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_URL_EXTENSION"],
    "ATTR_URL_FRAGMENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_URL_FRAGMENT"],
    "ATTR_URL_FULL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_URL_FULL"],
    "ATTR_URL_ORIGINAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_URL_ORIGINAL"],
    "ATTR_URL_PATH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_URL_PATH"],
    "ATTR_URL_PORT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_URL_PORT"],
    "ATTR_URL_QUERY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_URL_QUERY"],
    "ATTR_URL_REGISTERED_DOMAIN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_URL_REGISTERED_DOMAIN"],
    "ATTR_URL_SCHEME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_URL_SCHEME"],
    "ATTR_URL_SUBDOMAIN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_URL_SUBDOMAIN"],
    "ATTR_URL_TEMPLATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_URL_TEMPLATE"],
    "ATTR_URL_TOP_LEVEL_DOMAIN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_URL_TOP_LEVEL_DOMAIN"],
    "ATTR_USER_AGENT_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_USER_AGENT_NAME"],
    "ATTR_USER_AGENT_ORIGINAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_USER_AGENT_ORIGINAL"],
    "ATTR_USER_AGENT_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_USER_AGENT_VERSION"],
    "ATTR_USER_EMAIL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_USER_EMAIL"],
    "ATTR_USER_FULL_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_USER_FULL_NAME"],
    "ATTR_USER_HASH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_USER_HASH"],
    "ATTR_USER_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_USER_ID"],
    "ATTR_USER_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_USER_NAME"],
    "ATTR_USER_ROLES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_USER_ROLES"],
    "ATTR_V8JS_GC_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_V8JS_GC_TYPE"],
    "ATTR_V8JS_HEAP_SPACE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_V8JS_HEAP_SPACE_NAME"],
    "ATTR_VCS_REPOSITORY_CHANGE_ID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_VCS_REPOSITORY_CHANGE_ID"],
    "ATTR_VCS_REPOSITORY_CHANGE_TITLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_VCS_REPOSITORY_CHANGE_TITLE"],
    "ATTR_VCS_REPOSITORY_REF_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_VCS_REPOSITORY_REF_NAME"],
    "ATTR_VCS_REPOSITORY_REF_REVISION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_VCS_REPOSITORY_REF_REVISION"],
    "ATTR_VCS_REPOSITORY_REF_TYPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_VCS_REPOSITORY_REF_TYPE"],
    "ATTR_VCS_REPOSITORY_URL_FULL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_VCS_REPOSITORY_URL_FULL"],
    "ATTR_WEBENGINE_DESCRIPTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_WEBENGINE_DESCRIPTION"],
    "ATTR_WEBENGINE_NAME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_WEBENGINE_NAME"],
    "ATTR_WEBENGINE_VERSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ATTR_WEBENGINE_VERSION"],
    "AWS_ECS_LAUNCHTYPE_VALUE_EC2": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["AWS_ECS_LAUNCHTYPE_VALUE_EC2"],
    "AWS_ECS_LAUNCHTYPE_VALUE_FARGATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["AWS_ECS_LAUNCHTYPE_VALUE_FARGATE"],
    "CICD_PIPELINE_TASK_TYPE_VALUE_BUILD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CICD_PIPELINE_TASK_TYPE_VALUE_BUILD"],
    "CICD_PIPELINE_TASK_TYPE_VALUE_DEPLOY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CICD_PIPELINE_TASK_TYPE_VALUE_DEPLOY"],
    "CICD_PIPELINE_TASK_TYPE_VALUE_TEST": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CICD_PIPELINE_TASK_TYPE_VALUE_TEST"],
    "CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_ECS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_ECS"],
    "CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_FC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_FC"],
    "CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_OPENSHIFT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_OPENSHIFT"],
    "CLOUD_PLATFORM_VALUE_AWS_APP_RUNNER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_AWS_APP_RUNNER"],
    "CLOUD_PLATFORM_VALUE_AWS_EC2": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_AWS_EC2"],
    "CLOUD_PLATFORM_VALUE_AWS_ECS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_AWS_ECS"],
    "CLOUD_PLATFORM_VALUE_AWS_EKS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_AWS_EKS"],
    "CLOUD_PLATFORM_VALUE_AWS_ELASTIC_BEANSTALK": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_AWS_ELASTIC_BEANSTALK"],
    "CLOUD_PLATFORM_VALUE_AWS_LAMBDA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_AWS_LAMBDA"],
    "CLOUD_PLATFORM_VALUE_AWS_OPENSHIFT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_AWS_OPENSHIFT"],
    "CLOUD_PLATFORM_VALUE_AZURE_AKS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_AZURE_AKS"],
    "CLOUD_PLATFORM_VALUE_AZURE_APP_SERVICE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_AZURE_APP_SERVICE"],
    "CLOUD_PLATFORM_VALUE_AZURE_CONTAINER_APPS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_AZURE_CONTAINER_APPS"],
    "CLOUD_PLATFORM_VALUE_AZURE_CONTAINER_INSTANCES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_AZURE_CONTAINER_INSTANCES"],
    "CLOUD_PLATFORM_VALUE_AZURE_FUNCTIONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_AZURE_FUNCTIONS"],
    "CLOUD_PLATFORM_VALUE_AZURE_OPENSHIFT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_AZURE_OPENSHIFT"],
    "CLOUD_PLATFORM_VALUE_AZURE_VM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_AZURE_VM"],
    "CLOUD_PLATFORM_VALUE_GCP_APP_ENGINE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_GCP_APP_ENGINE"],
    "CLOUD_PLATFORM_VALUE_GCP_BARE_METAL_SOLUTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_GCP_BARE_METAL_SOLUTION"],
    "CLOUD_PLATFORM_VALUE_GCP_CLOUD_FUNCTIONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_GCP_CLOUD_FUNCTIONS"],
    "CLOUD_PLATFORM_VALUE_GCP_CLOUD_RUN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_GCP_CLOUD_RUN"],
    "CLOUD_PLATFORM_VALUE_GCP_COMPUTE_ENGINE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_GCP_COMPUTE_ENGINE"],
    "CLOUD_PLATFORM_VALUE_GCP_KUBERNETES_ENGINE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_GCP_KUBERNETES_ENGINE"],
    "CLOUD_PLATFORM_VALUE_GCP_OPENSHIFT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_GCP_OPENSHIFT"],
    "CLOUD_PLATFORM_VALUE_IBM_CLOUD_OPENSHIFT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_IBM_CLOUD_OPENSHIFT"],
    "CLOUD_PLATFORM_VALUE_TENCENT_CLOUD_CVM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_TENCENT_CLOUD_CVM"],
    "CLOUD_PLATFORM_VALUE_TENCENT_CLOUD_EKS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_TENCENT_CLOUD_EKS"],
    "CLOUD_PLATFORM_VALUE_TENCENT_CLOUD_SCF": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PLATFORM_VALUE_TENCENT_CLOUD_SCF"],
    "CLOUD_PROVIDER_VALUE_ALIBABA_CLOUD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PROVIDER_VALUE_ALIBABA_CLOUD"],
    "CLOUD_PROVIDER_VALUE_AWS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PROVIDER_VALUE_AWS"],
    "CLOUD_PROVIDER_VALUE_AZURE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PROVIDER_VALUE_AZURE"],
    "CLOUD_PROVIDER_VALUE_GCP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PROVIDER_VALUE_GCP"],
    "CLOUD_PROVIDER_VALUE_HEROKU": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PROVIDER_VALUE_HEROKU"],
    "CLOUD_PROVIDER_VALUE_IBM_CLOUD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PROVIDER_VALUE_IBM_CLOUD"],
    "CLOUD_PROVIDER_VALUE_TENCENT_CLOUD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CLOUD_PROVIDER_VALUE_TENCENT_CLOUD"],
    "CONTAINER_CPU_STATE_VALUE_KERNEL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CONTAINER_CPU_STATE_VALUE_KERNEL"],
    "CONTAINER_CPU_STATE_VALUE_SYSTEM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CONTAINER_CPU_STATE_VALUE_SYSTEM"],
    "CONTAINER_CPU_STATE_VALUE_USER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CONTAINER_CPU_STATE_VALUE_USER"],
    "CPU_MODE_VALUE_IDLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CPU_MODE_VALUE_IDLE"],
    "CPU_MODE_VALUE_INTERRUPT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CPU_MODE_VALUE_INTERRUPT"],
    "CPU_MODE_VALUE_IOWAIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CPU_MODE_VALUE_IOWAIT"],
    "CPU_MODE_VALUE_KERNEL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CPU_MODE_VALUE_KERNEL"],
    "CPU_MODE_VALUE_NICE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CPU_MODE_VALUE_NICE"],
    "CPU_MODE_VALUE_STEAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CPU_MODE_VALUE_STEAL"],
    "CPU_MODE_VALUE_SYSTEM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CPU_MODE_VALUE_SYSTEM"],
    "CPU_MODE_VALUE_USER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["CPU_MODE_VALUE_USER"],
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ALL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ALL"],
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ANY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ANY"],
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_EACH_QUORUM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_EACH_QUORUM"],
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_ONE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_ONE"],
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_QUORUM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_QUORUM"],
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_SERIAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_SERIAL"],
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ONE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ONE"],
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_QUORUM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_QUORUM"],
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_SERIAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_SERIAL"],
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_THREE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_THREE"],
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_TWO": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_TWO"],
    "DB_CLIENT_CONNECTIONS_STATE_VALUE_IDLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_CLIENT_CONNECTIONS_STATE_VALUE_IDLE"],
    "DB_CLIENT_CONNECTIONS_STATE_VALUE_USED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_CLIENT_CONNECTIONS_STATE_VALUE_USED"],
    "DB_CLIENT_CONNECTION_STATE_VALUE_IDLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_CLIENT_CONNECTION_STATE_VALUE_IDLE"],
    "DB_CLIENT_CONNECTION_STATE_VALUE_USED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_CLIENT_CONNECTION_STATE_VALUE_USED"],
    "DB_COSMOSDB_CONNECTION_MODE_VALUE_DIRECT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_COSMOSDB_CONNECTION_MODE_VALUE_DIRECT"],
    "DB_COSMOSDB_CONNECTION_MODE_VALUE_GATEWAY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_COSMOSDB_CONNECTION_MODE_VALUE_GATEWAY"],
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_BATCH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_COSMOSDB_OPERATION_TYPE_VALUE_BATCH"],
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_CREATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_COSMOSDB_OPERATION_TYPE_VALUE_CREATE"],
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_DELETE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_COSMOSDB_OPERATION_TYPE_VALUE_DELETE"],
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_EXECUTE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_COSMOSDB_OPERATION_TYPE_VALUE_EXECUTE"],
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_EXECUTE_JAVASCRIPT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_COSMOSDB_OPERATION_TYPE_VALUE_EXECUTE_JAVASCRIPT"],
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_HEAD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_COSMOSDB_OPERATION_TYPE_VALUE_HEAD"],
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_HEAD_FEED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_COSMOSDB_OPERATION_TYPE_VALUE_HEAD_FEED"],
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_INVALID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_COSMOSDB_OPERATION_TYPE_VALUE_INVALID"],
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_PATCH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_COSMOSDB_OPERATION_TYPE_VALUE_PATCH"],
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_QUERY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_COSMOSDB_OPERATION_TYPE_VALUE_QUERY"],
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_QUERY_PLAN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_COSMOSDB_OPERATION_TYPE_VALUE_QUERY_PLAN"],
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_READ": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_COSMOSDB_OPERATION_TYPE_VALUE_READ"],
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_READ_FEED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_COSMOSDB_OPERATION_TYPE_VALUE_READ_FEED"],
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_REPLACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_COSMOSDB_OPERATION_TYPE_VALUE_REPLACE"],
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_UPSERT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_COSMOSDB_OPERATION_TYPE_VALUE_UPSERT"],
    "DB_SYSTEM_VALUE_ADABAS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_ADABAS"],
    "DB_SYSTEM_VALUE_CACHE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_CACHE"],
    "DB_SYSTEM_VALUE_CASSANDRA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_CASSANDRA"],
    "DB_SYSTEM_VALUE_CLICKHOUSE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_CLICKHOUSE"],
    "DB_SYSTEM_VALUE_CLOUDSCAPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_CLOUDSCAPE"],
    "DB_SYSTEM_VALUE_COCKROACHDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_COCKROACHDB"],
    "DB_SYSTEM_VALUE_COLDFUSION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_COLDFUSION"],
    "DB_SYSTEM_VALUE_COSMOSDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_COSMOSDB"],
    "DB_SYSTEM_VALUE_COUCHBASE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_COUCHBASE"],
    "DB_SYSTEM_VALUE_COUCHDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_COUCHDB"],
    "DB_SYSTEM_VALUE_DB2": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_DB2"],
    "DB_SYSTEM_VALUE_DERBY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_DERBY"],
    "DB_SYSTEM_VALUE_DYNAMODB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_DYNAMODB"],
    "DB_SYSTEM_VALUE_EDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_EDB"],
    "DB_SYSTEM_VALUE_ELASTICSEARCH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_ELASTICSEARCH"],
    "DB_SYSTEM_VALUE_FILEMAKER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_FILEMAKER"],
    "DB_SYSTEM_VALUE_FIREBIRD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_FIREBIRD"],
    "DB_SYSTEM_VALUE_FIRSTSQL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_FIRSTSQL"],
    "DB_SYSTEM_VALUE_GEODE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_GEODE"],
    "DB_SYSTEM_VALUE_H2": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_H2"],
    "DB_SYSTEM_VALUE_HANADB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_HANADB"],
    "DB_SYSTEM_VALUE_HBASE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_HBASE"],
    "DB_SYSTEM_VALUE_HIVE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_HIVE"],
    "DB_SYSTEM_VALUE_HSQLDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_HSQLDB"],
    "DB_SYSTEM_VALUE_INFLUXDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_INFLUXDB"],
    "DB_SYSTEM_VALUE_INFORMIX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_INFORMIX"],
    "DB_SYSTEM_VALUE_INGRES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_INGRES"],
    "DB_SYSTEM_VALUE_INSTANTDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_INSTANTDB"],
    "DB_SYSTEM_VALUE_INTERBASE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_INTERBASE"],
    "DB_SYSTEM_VALUE_INTERSYSTEMS_CACHE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_INTERSYSTEMS_CACHE"],
    "DB_SYSTEM_VALUE_MARIADB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_MARIADB"],
    "DB_SYSTEM_VALUE_MAXDB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_MAXDB"],
    "DB_SYSTEM_VALUE_MEMCACHED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_MEMCACHED"],
    "DB_SYSTEM_VALUE_MONGODB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_MONGODB"],
    "DB_SYSTEM_VALUE_MSSQL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_MSSQL"],
    "DB_SYSTEM_VALUE_MSSQLCOMPACT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_MSSQLCOMPACT"],
    "DB_SYSTEM_VALUE_MYSQL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_MYSQL"],
    "DB_SYSTEM_VALUE_NEO4J": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_NEO4J"],
    "DB_SYSTEM_VALUE_NETEZZA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_NETEZZA"],
    "DB_SYSTEM_VALUE_OPENSEARCH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_OPENSEARCH"],
    "DB_SYSTEM_VALUE_ORACLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_ORACLE"],
    "DB_SYSTEM_VALUE_OTHER_SQL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_OTHER_SQL"],
    "DB_SYSTEM_VALUE_PERVASIVE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_PERVASIVE"],
    "DB_SYSTEM_VALUE_POINTBASE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_POINTBASE"],
    "DB_SYSTEM_VALUE_POSTGRESQL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_POSTGRESQL"],
    "DB_SYSTEM_VALUE_PROGRESS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_PROGRESS"],
    "DB_SYSTEM_VALUE_REDIS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_REDIS"],
    "DB_SYSTEM_VALUE_REDSHIFT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_REDSHIFT"],
    "DB_SYSTEM_VALUE_SPANNER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_SPANNER"],
    "DB_SYSTEM_VALUE_SQLITE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_SQLITE"],
    "DB_SYSTEM_VALUE_SYBASE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_SYBASE"],
    "DB_SYSTEM_VALUE_TERADATA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_TERADATA"],
    "DB_SYSTEM_VALUE_TRINO": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_TRINO"],
    "DB_SYSTEM_VALUE_VERTICA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DB_SYSTEM_VALUE_VERTICA"],
    "DEPLOYMENT_STATUS_VALUE_FAILED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DEPLOYMENT_STATUS_VALUE_FAILED"],
    "DEPLOYMENT_STATUS_VALUE_SUCCEEDED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DEPLOYMENT_STATUS_VALUE_SUCCEEDED"],
    "DISK_IO_DIRECTION_VALUE_READ": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DISK_IO_DIRECTION_VALUE_READ"],
    "DISK_IO_DIRECTION_VALUE_WRITE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["DISK_IO_DIRECTION_VALUE_WRITE"],
    "ERROR_TYPE_VALUE_OTHER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["ERROR_TYPE_VALUE_OTHER"],
    "FAAS_DOCUMENT_OPERATION_VALUE_DELETE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAAS_DOCUMENT_OPERATION_VALUE_DELETE"],
    "FAAS_DOCUMENT_OPERATION_VALUE_EDIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAAS_DOCUMENT_OPERATION_VALUE_EDIT"],
    "FAAS_DOCUMENT_OPERATION_VALUE_INSERT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAAS_DOCUMENT_OPERATION_VALUE_INSERT"],
    "FAAS_INVOKED_PROVIDER_VALUE_ALIBABA_CLOUD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAAS_INVOKED_PROVIDER_VALUE_ALIBABA_CLOUD"],
    "FAAS_INVOKED_PROVIDER_VALUE_AWS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAAS_INVOKED_PROVIDER_VALUE_AWS"],
    "FAAS_INVOKED_PROVIDER_VALUE_AZURE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAAS_INVOKED_PROVIDER_VALUE_AZURE"],
    "FAAS_INVOKED_PROVIDER_VALUE_GCP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAAS_INVOKED_PROVIDER_VALUE_GCP"],
    "FAAS_INVOKED_PROVIDER_VALUE_TENCENT_CLOUD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAAS_INVOKED_PROVIDER_VALUE_TENCENT_CLOUD"],
    "FAAS_TRIGGER_VALUE_DATASOURCE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAAS_TRIGGER_VALUE_DATASOURCE"],
    "FAAS_TRIGGER_VALUE_HTTP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAAS_TRIGGER_VALUE_HTTP"],
    "FAAS_TRIGGER_VALUE_OTHER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAAS_TRIGGER_VALUE_OTHER"],
    "FAAS_TRIGGER_VALUE_PUBSUB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAAS_TRIGGER_VALUE_PUBSUB"],
    "FAAS_TRIGGER_VALUE_TIMER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["FAAS_TRIGGER_VALUE_TIMER"],
    "GEN_AI_OPERATION_NAME_VALUE_CHAT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["GEN_AI_OPERATION_NAME_VALUE_CHAT"],
    "GEN_AI_OPERATION_NAME_VALUE_TEXT_COMPLETION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["GEN_AI_OPERATION_NAME_VALUE_TEXT_COMPLETION"],
    "GEN_AI_SYSTEM_VALUE_ANTHROPIC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["GEN_AI_SYSTEM_VALUE_ANTHROPIC"],
    "GEN_AI_SYSTEM_VALUE_COHERE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["GEN_AI_SYSTEM_VALUE_COHERE"],
    "GEN_AI_SYSTEM_VALUE_OPENAI": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["GEN_AI_SYSTEM_VALUE_OPENAI"],
    "GEN_AI_SYSTEM_VALUE_VERTEX_AI": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["GEN_AI_SYSTEM_VALUE_VERTEX_AI"],
    "GEN_AI_TOKEN_TYPE_VALUE_COMPLETION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["GEN_AI_TOKEN_TYPE_VALUE_COMPLETION"],
    "GEN_AI_TOKEN_TYPE_VALUE_INPUT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["GEN_AI_TOKEN_TYPE_VALUE_INPUT"],
    "GO_MEMORY_TYPE_VALUE_OTHER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["GO_MEMORY_TYPE_VALUE_OTHER"],
    "GO_MEMORY_TYPE_VALUE_STACK": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["GO_MEMORY_TYPE_VALUE_STACK"],
    "GRAPHQL_OPERATION_TYPE_VALUE_MUTATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["GRAPHQL_OPERATION_TYPE_VALUE_MUTATION"],
    "GRAPHQL_OPERATION_TYPE_VALUE_QUERY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["GRAPHQL_OPERATION_TYPE_VALUE_QUERY"],
    "GRAPHQL_OPERATION_TYPE_VALUE_SUBSCRIPTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["GRAPHQL_OPERATION_TYPE_VALUE_SUBSCRIPTION"],
    "HOST_ARCH_VALUE_AMD64": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HOST_ARCH_VALUE_AMD64"],
    "HOST_ARCH_VALUE_ARM32": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HOST_ARCH_VALUE_ARM32"],
    "HOST_ARCH_VALUE_ARM64": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HOST_ARCH_VALUE_ARM64"],
    "HOST_ARCH_VALUE_IA64": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HOST_ARCH_VALUE_IA64"],
    "HOST_ARCH_VALUE_PPC32": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HOST_ARCH_VALUE_PPC32"],
    "HOST_ARCH_VALUE_PPC64": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HOST_ARCH_VALUE_PPC64"],
    "HOST_ARCH_VALUE_S390X": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HOST_ARCH_VALUE_S390X"],
    "HOST_ARCH_VALUE_X86": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HOST_ARCH_VALUE_X86"],
    "HTTP_CONNECTION_STATE_VALUE_ACTIVE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTP_CONNECTION_STATE_VALUE_ACTIVE"],
    "HTTP_CONNECTION_STATE_VALUE_IDLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTP_CONNECTION_STATE_VALUE_IDLE"],
    "HTTP_FLAVOR_VALUE_HTTP_1_0": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTP_FLAVOR_VALUE_HTTP_1_0"],
    "HTTP_FLAVOR_VALUE_HTTP_1_1": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTP_FLAVOR_VALUE_HTTP_1_1"],
    "HTTP_FLAVOR_VALUE_HTTP_2_0": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTP_FLAVOR_VALUE_HTTP_2_0"],
    "HTTP_FLAVOR_VALUE_HTTP_3_0": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTP_FLAVOR_VALUE_HTTP_3_0"],
    "HTTP_FLAVOR_VALUE_QUIC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTP_FLAVOR_VALUE_QUIC"],
    "HTTP_FLAVOR_VALUE_SPDY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTP_FLAVOR_VALUE_SPDY"],
    "HTTP_REQUEST_METHOD_VALUE_CONNECT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTP_REQUEST_METHOD_VALUE_CONNECT"],
    "HTTP_REQUEST_METHOD_VALUE_DELETE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTP_REQUEST_METHOD_VALUE_DELETE"],
    "HTTP_REQUEST_METHOD_VALUE_GET": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTP_REQUEST_METHOD_VALUE_GET"],
    "HTTP_REQUEST_METHOD_VALUE_HEAD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTP_REQUEST_METHOD_VALUE_HEAD"],
    "HTTP_REQUEST_METHOD_VALUE_OPTIONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTP_REQUEST_METHOD_VALUE_OPTIONS"],
    "HTTP_REQUEST_METHOD_VALUE_OTHER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTP_REQUEST_METHOD_VALUE_OTHER"],
    "HTTP_REQUEST_METHOD_VALUE_PATCH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTP_REQUEST_METHOD_VALUE_PATCH"],
    "HTTP_REQUEST_METHOD_VALUE_POST": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTP_REQUEST_METHOD_VALUE_POST"],
    "HTTP_REQUEST_METHOD_VALUE_PUT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTP_REQUEST_METHOD_VALUE_PUT"],
    "HTTP_REQUEST_METHOD_VALUE_TRACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["HTTP_REQUEST_METHOD_VALUE_TRACE"],
    "IOS_STATE_VALUE_ACTIVE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["IOS_STATE_VALUE_ACTIVE"],
    "IOS_STATE_VALUE_BACKGROUND": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["IOS_STATE_VALUE_BACKGROUND"],
    "IOS_STATE_VALUE_FOREGROUND": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["IOS_STATE_VALUE_FOREGROUND"],
    "IOS_STATE_VALUE_INACTIVE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["IOS_STATE_VALUE_INACTIVE"],
    "IOS_STATE_VALUE_TERMINATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["IOS_STATE_VALUE_TERMINATE"],
    "JVM_MEMORY_TYPE_VALUE_HEAP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["JVM_MEMORY_TYPE_VALUE_HEAP"],
    "JVM_MEMORY_TYPE_VALUE_NON_HEAP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["JVM_MEMORY_TYPE_VALUE_NON_HEAP"],
    "JVM_THREAD_STATE_VALUE_BLOCKED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["JVM_THREAD_STATE_VALUE_BLOCKED"],
    "JVM_THREAD_STATE_VALUE_NEW": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["JVM_THREAD_STATE_VALUE_NEW"],
    "JVM_THREAD_STATE_VALUE_RUNNABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["JVM_THREAD_STATE_VALUE_RUNNABLE"],
    "JVM_THREAD_STATE_VALUE_TERMINATED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["JVM_THREAD_STATE_VALUE_TERMINATED"],
    "JVM_THREAD_STATE_VALUE_TIMED_WAITING": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["JVM_THREAD_STATE_VALUE_TIMED_WAITING"],
    "JVM_THREAD_STATE_VALUE_WAITING": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["JVM_THREAD_STATE_VALUE_WAITING"],
    "LINUX_MEMORY_SLAB_STATE_VALUE_RECLAIMABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["LINUX_MEMORY_SLAB_STATE_VALUE_RECLAIMABLE"],
    "LINUX_MEMORY_SLAB_STATE_VALUE_UNRECLAIMABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["LINUX_MEMORY_SLAB_STATE_VALUE_UNRECLAIMABLE"],
    "LOG_IOSTREAM_VALUE_STDERR": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["LOG_IOSTREAM_VALUE_STDERR"],
    "LOG_IOSTREAM_VALUE_STDOUT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["LOG_IOSTREAM_VALUE_STDOUT"],
    "MESSAGE_TYPE_VALUE_RECEIVED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGE_TYPE_VALUE_RECEIVED"],
    "MESSAGE_TYPE_VALUE_SENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGE_TYPE_VALUE_SENT"],
    "MESSAGING_OPERATION_TYPE_VALUE_CREATE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_OPERATION_TYPE_VALUE_CREATE"],
    "MESSAGING_OPERATION_TYPE_VALUE_DELIVER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_OPERATION_TYPE_VALUE_DELIVER"],
    "MESSAGING_OPERATION_TYPE_VALUE_PROCESS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_OPERATION_TYPE_VALUE_PROCESS"],
    "MESSAGING_OPERATION_TYPE_VALUE_PUBLISH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_OPERATION_TYPE_VALUE_PUBLISH"],
    "MESSAGING_OPERATION_TYPE_VALUE_RECEIVE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_OPERATION_TYPE_VALUE_RECEIVE"],
    "MESSAGING_OPERATION_TYPE_VALUE_SETTLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_OPERATION_TYPE_VALUE_SETTLE"],
    "MESSAGING_ROCKETMQ_CONSUMPTION_MODEL_VALUE_BROADCASTING": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_ROCKETMQ_CONSUMPTION_MODEL_VALUE_BROADCASTING"],
    "MESSAGING_ROCKETMQ_CONSUMPTION_MODEL_VALUE_CLUSTERING": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_ROCKETMQ_CONSUMPTION_MODEL_VALUE_CLUSTERING"],
    "MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_DELAY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_DELAY"],
    "MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_FIFO": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_FIFO"],
    "MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_NORMAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_NORMAL"],
    "MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_TRANSACTION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_TRANSACTION"],
    "MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_ABANDON": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_ABANDON"],
    "MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_COMPLETE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_COMPLETE"],
    "MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_DEAD_LETTER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_DEAD_LETTER"],
    "MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_DEFER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_DEFER"],
    "MESSAGING_SYSTEM_VALUE_ACTIVEMQ": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_SYSTEM_VALUE_ACTIVEMQ"],
    "MESSAGING_SYSTEM_VALUE_AWS_SQS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_SYSTEM_VALUE_AWS_SQS"],
    "MESSAGING_SYSTEM_VALUE_EVENTGRID": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_SYSTEM_VALUE_EVENTGRID"],
    "MESSAGING_SYSTEM_VALUE_EVENTHUBS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_SYSTEM_VALUE_EVENTHUBS"],
    "MESSAGING_SYSTEM_VALUE_GCP_PUBSUB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_SYSTEM_VALUE_GCP_PUBSUB"],
    "MESSAGING_SYSTEM_VALUE_JMS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_SYSTEM_VALUE_JMS"],
    "MESSAGING_SYSTEM_VALUE_KAFKA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_SYSTEM_VALUE_KAFKA"],
    "MESSAGING_SYSTEM_VALUE_PULSAR": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_SYSTEM_VALUE_PULSAR"],
    "MESSAGING_SYSTEM_VALUE_RABBITMQ": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_SYSTEM_VALUE_RABBITMQ"],
    "MESSAGING_SYSTEM_VALUE_ROCKETMQ": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_SYSTEM_VALUE_ROCKETMQ"],
    "MESSAGING_SYSTEM_VALUE_SERVICEBUS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["MESSAGING_SYSTEM_VALUE_SERVICEBUS"],
    "METRIC_ASPNETCORE_DIAGNOSTICS_EXCEPTIONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_ASPNETCORE_DIAGNOSTICS_EXCEPTIONS"],
    "METRIC_ASPNETCORE_RATE_LIMITING_ACTIVE_REQUEST_LEASES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_ASPNETCORE_RATE_LIMITING_ACTIVE_REQUEST_LEASES"],
    "METRIC_ASPNETCORE_RATE_LIMITING_QUEUED_REQUESTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_ASPNETCORE_RATE_LIMITING_QUEUED_REQUESTS"],
    "METRIC_ASPNETCORE_RATE_LIMITING_REQUESTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_ASPNETCORE_RATE_LIMITING_REQUESTS"],
    "METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_LEASE_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_LEASE_DURATION"],
    "METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_TIME_IN_QUEUE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_TIME_IN_QUEUE"],
    "METRIC_ASPNETCORE_ROUTING_MATCH_ATTEMPTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_ASPNETCORE_ROUTING_MATCH_ATTEMPTS"],
    "METRIC_CONTAINER_CPU_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_CONTAINER_CPU_TIME"],
    "METRIC_CONTAINER_DISK_IO": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_CONTAINER_DISK_IO"],
    "METRIC_CONTAINER_MEMORY_USAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_CONTAINER_MEMORY_USAGE"],
    "METRIC_CONTAINER_NETWORK_IO": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_CONTAINER_NETWORK_IO"],
    "METRIC_DB_CLIENT_CONNECTIONS_CREATE_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_CONNECTIONS_CREATE_TIME"],
    "METRIC_DB_CLIENT_CONNECTIONS_IDLE_MAX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_CONNECTIONS_IDLE_MAX"],
    "METRIC_DB_CLIENT_CONNECTIONS_IDLE_MIN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_CONNECTIONS_IDLE_MIN"],
    "METRIC_DB_CLIENT_CONNECTIONS_MAX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_CONNECTIONS_MAX"],
    "METRIC_DB_CLIENT_CONNECTIONS_PENDING_REQUESTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_CONNECTIONS_PENDING_REQUESTS"],
    "METRIC_DB_CLIENT_CONNECTIONS_TIMEOUTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_CONNECTIONS_TIMEOUTS"],
    "METRIC_DB_CLIENT_CONNECTIONS_USAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_CONNECTIONS_USAGE"],
    "METRIC_DB_CLIENT_CONNECTIONS_USE_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_CONNECTIONS_USE_TIME"],
    "METRIC_DB_CLIENT_CONNECTIONS_WAIT_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_CONNECTIONS_WAIT_TIME"],
    "METRIC_DB_CLIENT_CONNECTION_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_CONNECTION_COUNT"],
    "METRIC_DB_CLIENT_CONNECTION_CREATE_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_CONNECTION_CREATE_TIME"],
    "METRIC_DB_CLIENT_CONNECTION_IDLE_MAX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_CONNECTION_IDLE_MAX"],
    "METRIC_DB_CLIENT_CONNECTION_IDLE_MIN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_CONNECTION_IDLE_MIN"],
    "METRIC_DB_CLIENT_CONNECTION_MAX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_CONNECTION_MAX"],
    "METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS"],
    "METRIC_DB_CLIENT_CONNECTION_TIMEOUTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_CONNECTION_TIMEOUTS"],
    "METRIC_DB_CLIENT_CONNECTION_USE_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_CONNECTION_USE_TIME"],
    "METRIC_DB_CLIENT_CONNECTION_WAIT_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_CONNECTION_WAIT_TIME"],
    "METRIC_DB_CLIENT_OPERATION_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DB_CLIENT_OPERATION_DURATION"],
    "METRIC_DNS_LOOKUP_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_DNS_LOOKUP_DURATION"],
    "METRIC_FAAS_COLDSTARTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_FAAS_COLDSTARTS"],
    "METRIC_FAAS_CPU_USAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_FAAS_CPU_USAGE"],
    "METRIC_FAAS_ERRORS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_FAAS_ERRORS"],
    "METRIC_FAAS_INIT_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_FAAS_INIT_DURATION"],
    "METRIC_FAAS_INVOCATIONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_FAAS_INVOCATIONS"],
    "METRIC_FAAS_INVOKE_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_FAAS_INVOKE_DURATION"],
    "METRIC_FAAS_MEM_USAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_FAAS_MEM_USAGE"],
    "METRIC_FAAS_NET_IO": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_FAAS_NET_IO"],
    "METRIC_FAAS_TIMEOUTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_FAAS_TIMEOUTS"],
    "METRIC_GEN_AI_CLIENT_OPERATION_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_GEN_AI_CLIENT_OPERATION_DURATION"],
    "METRIC_GEN_AI_CLIENT_TOKEN_USAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_GEN_AI_CLIENT_TOKEN_USAGE"],
    "METRIC_GEN_AI_SERVER_REQUEST_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_GEN_AI_SERVER_REQUEST_DURATION"],
    "METRIC_GEN_AI_SERVER_TIME_PER_OUTPUT_TOKEN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_GEN_AI_SERVER_TIME_PER_OUTPUT_TOKEN"],
    "METRIC_GEN_AI_SERVER_TIME_TO_FIRST_TOKEN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_GEN_AI_SERVER_TIME_TO_FIRST_TOKEN"],
    "METRIC_GO_CONFIG_GOGC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_GO_CONFIG_GOGC"],
    "METRIC_GO_GOROUTINE_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_GO_GOROUTINE_COUNT"],
    "METRIC_GO_MEMORY_ALLOCATED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_GO_MEMORY_ALLOCATED"],
    "METRIC_GO_MEMORY_ALLOCATIONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_GO_MEMORY_ALLOCATIONS"],
    "METRIC_GO_MEMORY_GC_GOAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_GO_MEMORY_GC_GOAL"],
    "METRIC_GO_MEMORY_LIMIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_GO_MEMORY_LIMIT"],
    "METRIC_GO_MEMORY_USED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_GO_MEMORY_USED"],
    "METRIC_GO_PROCESSOR_LIMIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_GO_PROCESSOR_LIMIT"],
    "METRIC_GO_SCHEDULE_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_GO_SCHEDULE_DURATION"],
    "METRIC_HTTP_CLIENT_ACTIVE_REQUESTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_HTTP_CLIENT_ACTIVE_REQUESTS"],
    "METRIC_HTTP_CLIENT_CONNECTION_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_HTTP_CLIENT_CONNECTION_DURATION"],
    "METRIC_HTTP_CLIENT_OPEN_CONNECTIONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_HTTP_CLIENT_OPEN_CONNECTIONS"],
    "METRIC_HTTP_CLIENT_REQUEST_BODY_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_HTTP_CLIENT_REQUEST_BODY_SIZE"],
    "METRIC_HTTP_CLIENT_REQUEST_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_HTTP_CLIENT_REQUEST_DURATION"],
    "METRIC_HTTP_CLIENT_RESPONSE_BODY_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_HTTP_CLIENT_RESPONSE_BODY_SIZE"],
    "METRIC_HTTP_SERVER_ACTIVE_REQUESTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_HTTP_SERVER_ACTIVE_REQUESTS"],
    "METRIC_HTTP_SERVER_REQUEST_BODY_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_HTTP_SERVER_REQUEST_BODY_SIZE"],
    "METRIC_HTTP_SERVER_REQUEST_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_HTTP_SERVER_REQUEST_DURATION"],
    "METRIC_HTTP_SERVER_RESPONSE_BODY_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_HTTP_SERVER_RESPONSE_BODY_SIZE"],
    "METRIC_JVM_BUFFER_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_BUFFER_COUNT"],
    "METRIC_JVM_BUFFER_MEMORY_LIMIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_BUFFER_MEMORY_LIMIT"],
    "METRIC_JVM_BUFFER_MEMORY_USAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_BUFFER_MEMORY_USAGE"],
    "METRIC_JVM_BUFFER_MEMORY_USED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_BUFFER_MEMORY_USED"],
    "METRIC_JVM_CLASS_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_CLASS_COUNT"],
    "METRIC_JVM_CLASS_LOADED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_CLASS_LOADED"],
    "METRIC_JVM_CLASS_UNLOADED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_CLASS_UNLOADED"],
    "METRIC_JVM_CPU_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_CPU_COUNT"],
    "METRIC_JVM_CPU_RECENT_UTILIZATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_CPU_RECENT_UTILIZATION"],
    "METRIC_JVM_CPU_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_CPU_TIME"],
    "METRIC_JVM_GC_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_GC_DURATION"],
    "METRIC_JVM_MEMORY_COMMITTED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_MEMORY_COMMITTED"],
    "METRIC_JVM_MEMORY_INIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_MEMORY_INIT"],
    "METRIC_JVM_MEMORY_LIMIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_MEMORY_LIMIT"],
    "METRIC_JVM_MEMORY_USED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_MEMORY_USED"],
    "METRIC_JVM_MEMORY_USED_AFTER_LAST_GC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_MEMORY_USED_AFTER_LAST_GC"],
    "METRIC_JVM_SYSTEM_CPU_LOAD_1M": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_SYSTEM_CPU_LOAD_1M"],
    "METRIC_JVM_SYSTEM_CPU_UTILIZATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_SYSTEM_CPU_UTILIZATION"],
    "METRIC_JVM_THREAD_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_JVM_THREAD_COUNT"],
    "METRIC_KESTREL_ACTIVE_CONNECTIONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_KESTREL_ACTIVE_CONNECTIONS"],
    "METRIC_KESTREL_ACTIVE_TLS_HANDSHAKES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_KESTREL_ACTIVE_TLS_HANDSHAKES"],
    "METRIC_KESTREL_CONNECTION_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_KESTREL_CONNECTION_DURATION"],
    "METRIC_KESTREL_QUEUED_CONNECTIONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_KESTREL_QUEUED_CONNECTIONS"],
    "METRIC_KESTREL_QUEUED_REQUESTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_KESTREL_QUEUED_REQUESTS"],
    "METRIC_KESTREL_REJECTED_CONNECTIONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_KESTREL_REJECTED_CONNECTIONS"],
    "METRIC_KESTREL_TLS_HANDSHAKE_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_KESTREL_TLS_HANDSHAKE_DURATION"],
    "METRIC_KESTREL_UPGRADED_CONNECTIONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_KESTREL_UPGRADED_CONNECTIONS"],
    "METRIC_MESSAGING_CLIENT_CONSUMED_MESSAGES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_MESSAGING_CLIENT_CONSUMED_MESSAGES"],
    "METRIC_MESSAGING_CLIENT_OPERATION_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_MESSAGING_CLIENT_OPERATION_DURATION"],
    "METRIC_MESSAGING_CLIENT_PUBLISHED_MESSAGES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_MESSAGING_CLIENT_PUBLISHED_MESSAGES"],
    "METRIC_MESSAGING_PROCESS_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_MESSAGING_PROCESS_DURATION"],
    "METRIC_MESSAGING_PROCESS_MESSAGES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_MESSAGING_PROCESS_MESSAGES"],
    "METRIC_MESSAGING_PUBLISH_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_MESSAGING_PUBLISH_DURATION"],
    "METRIC_MESSAGING_PUBLISH_MESSAGES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_MESSAGING_PUBLISH_MESSAGES"],
    "METRIC_MESSAGING_RECEIVE_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_MESSAGING_RECEIVE_DURATION"],
    "METRIC_MESSAGING_RECEIVE_MESSAGES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_MESSAGING_RECEIVE_MESSAGES"],
    "METRIC_NODEJS_EVENTLOOP_DELAY_MAX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_NODEJS_EVENTLOOP_DELAY_MAX"],
    "METRIC_NODEJS_EVENTLOOP_DELAY_MEAN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_NODEJS_EVENTLOOP_DELAY_MEAN"],
    "METRIC_NODEJS_EVENTLOOP_DELAY_MIN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_NODEJS_EVENTLOOP_DELAY_MIN"],
    "METRIC_NODEJS_EVENTLOOP_DELAY_P50": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_NODEJS_EVENTLOOP_DELAY_P50"],
    "METRIC_NODEJS_EVENTLOOP_DELAY_P90": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_NODEJS_EVENTLOOP_DELAY_P90"],
    "METRIC_NODEJS_EVENTLOOP_DELAY_P99": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_NODEJS_EVENTLOOP_DELAY_P99"],
    "METRIC_NODEJS_EVENTLOOP_DELAY_STDDEV": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_NODEJS_EVENTLOOP_DELAY_STDDEV"],
    "METRIC_NODEJS_EVENTLOOP_UTILIZATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_NODEJS_EVENTLOOP_UTILIZATION"],
    "METRIC_PROCESS_CONTEXT_SWITCHES": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_PROCESS_CONTEXT_SWITCHES"],
    "METRIC_PROCESS_CPU_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_PROCESS_CPU_TIME"],
    "METRIC_PROCESS_CPU_UTILIZATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_PROCESS_CPU_UTILIZATION"],
    "METRIC_PROCESS_DISK_IO": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_PROCESS_DISK_IO"],
    "METRIC_PROCESS_MEMORY_USAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_PROCESS_MEMORY_USAGE"],
    "METRIC_PROCESS_MEMORY_VIRTUAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_PROCESS_MEMORY_VIRTUAL"],
    "METRIC_PROCESS_NETWORK_IO": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_PROCESS_NETWORK_IO"],
    "METRIC_PROCESS_OPEN_FILE_DESCRIPTOR_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_PROCESS_OPEN_FILE_DESCRIPTOR_COUNT"],
    "METRIC_PROCESS_PAGING_FAULTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_PROCESS_PAGING_FAULTS"],
    "METRIC_PROCESS_THREAD_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_PROCESS_THREAD_COUNT"],
    "METRIC_RPC_CLIENT_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_RPC_CLIENT_DURATION"],
    "METRIC_RPC_CLIENT_REQUESTS_PER_RPC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_RPC_CLIENT_REQUESTS_PER_RPC"],
    "METRIC_RPC_CLIENT_REQUEST_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_RPC_CLIENT_REQUEST_SIZE"],
    "METRIC_RPC_CLIENT_RESPONSES_PER_RPC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_RPC_CLIENT_RESPONSES_PER_RPC"],
    "METRIC_RPC_CLIENT_RESPONSE_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_RPC_CLIENT_RESPONSE_SIZE"],
    "METRIC_RPC_SERVER_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_RPC_SERVER_DURATION"],
    "METRIC_RPC_SERVER_REQUESTS_PER_RPC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_RPC_SERVER_REQUESTS_PER_RPC"],
    "METRIC_RPC_SERVER_REQUEST_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_RPC_SERVER_REQUEST_SIZE"],
    "METRIC_RPC_SERVER_RESPONSES_PER_RPC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_RPC_SERVER_RESPONSES_PER_RPC"],
    "METRIC_RPC_SERVER_RESPONSE_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_RPC_SERVER_RESPONSE_SIZE"],
    "METRIC_SIGNALR_SERVER_ACTIVE_CONNECTIONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SIGNALR_SERVER_ACTIVE_CONNECTIONS"],
    "METRIC_SIGNALR_SERVER_CONNECTION_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SIGNALR_SERVER_CONNECTION_DURATION"],
    "METRIC_SYSTEM_CPU_FREQUENCY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_CPU_FREQUENCY"],
    "METRIC_SYSTEM_CPU_LOGICAL_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_CPU_LOGICAL_COUNT"],
    "METRIC_SYSTEM_CPU_PHYSICAL_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_CPU_PHYSICAL_COUNT"],
    "METRIC_SYSTEM_CPU_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_CPU_TIME"],
    "METRIC_SYSTEM_CPU_UTILIZATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_CPU_UTILIZATION"],
    "METRIC_SYSTEM_DISK_IO": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_DISK_IO"],
    "METRIC_SYSTEM_DISK_IO_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_DISK_IO_TIME"],
    "METRIC_SYSTEM_DISK_MERGED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_DISK_MERGED"],
    "METRIC_SYSTEM_DISK_OPERATIONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_DISK_OPERATIONS"],
    "METRIC_SYSTEM_DISK_OPERATION_TIME": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_DISK_OPERATION_TIME"],
    "METRIC_SYSTEM_FILESYSTEM_USAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_FILESYSTEM_USAGE"],
    "METRIC_SYSTEM_FILESYSTEM_UTILIZATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_FILESYSTEM_UTILIZATION"],
    "METRIC_SYSTEM_LINUX_MEMORY_AVAILABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_LINUX_MEMORY_AVAILABLE"],
    "METRIC_SYSTEM_LINUX_MEMORY_SLAB_USAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_LINUX_MEMORY_SLAB_USAGE"],
    "METRIC_SYSTEM_MEMORY_LIMIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_MEMORY_LIMIT"],
    "METRIC_SYSTEM_MEMORY_SHARED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_MEMORY_SHARED"],
    "METRIC_SYSTEM_MEMORY_USAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_MEMORY_USAGE"],
    "METRIC_SYSTEM_MEMORY_UTILIZATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_MEMORY_UTILIZATION"],
    "METRIC_SYSTEM_NETWORK_CONNECTIONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_NETWORK_CONNECTIONS"],
    "METRIC_SYSTEM_NETWORK_DROPPED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_NETWORK_DROPPED"],
    "METRIC_SYSTEM_NETWORK_ERRORS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_NETWORK_ERRORS"],
    "METRIC_SYSTEM_NETWORK_IO": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_NETWORK_IO"],
    "METRIC_SYSTEM_NETWORK_PACKETS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_NETWORK_PACKETS"],
    "METRIC_SYSTEM_PAGING_FAULTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_PAGING_FAULTS"],
    "METRIC_SYSTEM_PAGING_OPERATIONS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_PAGING_OPERATIONS"],
    "METRIC_SYSTEM_PAGING_USAGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_PAGING_USAGE"],
    "METRIC_SYSTEM_PAGING_UTILIZATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_PAGING_UTILIZATION"],
    "METRIC_SYSTEM_PROCESS_COUNT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_PROCESS_COUNT"],
    "METRIC_SYSTEM_PROCESS_CREATED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_SYSTEM_PROCESS_CREATED"],
    "METRIC_V8JS_GC_DURATION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_V8JS_GC_DURATION"],
    "METRIC_V8JS_HEAP_SPACE_AVAILABLE_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_V8JS_HEAP_SPACE_AVAILABLE_SIZE"],
    "METRIC_V8JS_HEAP_SPACE_PHYSICAL_SIZE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_V8JS_HEAP_SPACE_PHYSICAL_SIZE"],
    "METRIC_V8JS_MEMORY_HEAP_LIMIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_V8JS_MEMORY_HEAP_LIMIT"],
    "METRIC_V8JS_MEMORY_HEAP_USED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["METRIC_V8JS_MEMORY_HEAP_USED"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_CDMA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_CDMA"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_CDMA2000_1XRTT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_CDMA2000_1XRTT"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_EDGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_EDGE"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_EHRPD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_EHRPD"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_0": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_0"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_A": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_A"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_B": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_B"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_GPRS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_GPRS"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_GSM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_GSM"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_HSDPA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_HSDPA"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_HSPA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_HSPA"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_HSPAP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_HSPAP"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_HSUPA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_HSUPA"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_IDEN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_IDEN"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_IWLAN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_IWLAN"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_LTE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_LTE"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_LTE_CA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_LTE_CA"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_NR": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_NR"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_NRNSA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_NRNSA"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_TD_SCDMA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_TD_SCDMA"],
    "NETWORK_CONNECTION_SUBTYPE_VALUE_UMTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_SUBTYPE_VALUE_UMTS"],
    "NETWORK_CONNECTION_TYPE_VALUE_CELL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_TYPE_VALUE_CELL"],
    "NETWORK_CONNECTION_TYPE_VALUE_UNAVAILABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_TYPE_VALUE_UNAVAILABLE"],
    "NETWORK_CONNECTION_TYPE_VALUE_UNKNOWN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_TYPE_VALUE_UNKNOWN"],
    "NETWORK_CONNECTION_TYPE_VALUE_WIFI": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_TYPE_VALUE_WIFI"],
    "NETWORK_CONNECTION_TYPE_VALUE_WIRED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_CONNECTION_TYPE_VALUE_WIRED"],
    "NETWORK_IO_DIRECTION_VALUE_RECEIVE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_IO_DIRECTION_VALUE_RECEIVE"],
    "NETWORK_IO_DIRECTION_VALUE_TRANSMIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_IO_DIRECTION_VALUE_TRANSMIT"],
    "NETWORK_TRANSPORT_VALUE_PIPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_TRANSPORT_VALUE_PIPE"],
    "NETWORK_TRANSPORT_VALUE_QUIC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_TRANSPORT_VALUE_QUIC"],
    "NETWORK_TRANSPORT_VALUE_TCP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_TRANSPORT_VALUE_TCP"],
    "NETWORK_TRANSPORT_VALUE_UDP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_TRANSPORT_VALUE_UDP"],
    "NETWORK_TRANSPORT_VALUE_UNIX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_TRANSPORT_VALUE_UNIX"],
    "NETWORK_TYPE_VALUE_IPV4": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_TYPE_VALUE_IPV4"],
    "NETWORK_TYPE_VALUE_IPV6": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NETWORK_TYPE_VALUE_IPV6"],
    "NET_SOCK_FAMILY_VALUE_INET": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NET_SOCK_FAMILY_VALUE_INET"],
    "NET_SOCK_FAMILY_VALUE_INET6": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NET_SOCK_FAMILY_VALUE_INET6"],
    "NET_SOCK_FAMILY_VALUE_UNIX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NET_SOCK_FAMILY_VALUE_UNIX"],
    "NET_TRANSPORT_VALUE_INPROC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NET_TRANSPORT_VALUE_INPROC"],
    "NET_TRANSPORT_VALUE_IP_TCP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NET_TRANSPORT_VALUE_IP_TCP"],
    "NET_TRANSPORT_VALUE_IP_UDP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NET_TRANSPORT_VALUE_IP_UDP"],
    "NET_TRANSPORT_VALUE_OTHER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NET_TRANSPORT_VALUE_OTHER"],
    "NET_TRANSPORT_VALUE_PIPE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["NET_TRANSPORT_VALUE_PIPE"],
    "OPENTRACING_REF_TYPE_VALUE_CHILD_OF": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["OPENTRACING_REF_TYPE_VALUE_CHILD_OF"],
    "OPENTRACING_REF_TYPE_VALUE_FOLLOWS_FROM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["OPENTRACING_REF_TYPE_VALUE_FOLLOWS_FROM"],
    "OS_TYPE_VALUE_AIX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["OS_TYPE_VALUE_AIX"],
    "OS_TYPE_VALUE_DARWIN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["OS_TYPE_VALUE_DARWIN"],
    "OS_TYPE_VALUE_DRAGONFLYBSD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["OS_TYPE_VALUE_DRAGONFLYBSD"],
    "OS_TYPE_VALUE_FREEBSD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["OS_TYPE_VALUE_FREEBSD"],
    "OS_TYPE_VALUE_HPUX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["OS_TYPE_VALUE_HPUX"],
    "OS_TYPE_VALUE_LINUX": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["OS_TYPE_VALUE_LINUX"],
    "OS_TYPE_VALUE_NETBSD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["OS_TYPE_VALUE_NETBSD"],
    "OS_TYPE_VALUE_OPENBSD": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["OS_TYPE_VALUE_OPENBSD"],
    "OS_TYPE_VALUE_SOLARIS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["OS_TYPE_VALUE_SOLARIS"],
    "OS_TYPE_VALUE_WINDOWS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["OS_TYPE_VALUE_WINDOWS"],
    "OS_TYPE_VALUE_Z_OS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["OS_TYPE_VALUE_Z_OS"],
    "OTEL_STATUS_CODE_VALUE_ERROR": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["OTEL_STATUS_CODE_VALUE_ERROR"],
    "OTEL_STATUS_CODE_VALUE_OK": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["OTEL_STATUS_CODE_VALUE_OK"],
    "PROCESS_CONTEXT_SWITCH_TYPE_VALUE_INVOLUNTARY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["PROCESS_CONTEXT_SWITCH_TYPE_VALUE_INVOLUNTARY"],
    "PROCESS_CONTEXT_SWITCH_TYPE_VALUE_VOLUNTARY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["PROCESS_CONTEXT_SWITCH_TYPE_VALUE_VOLUNTARY"],
    "PROCESS_CPU_STATE_VALUE_SYSTEM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["PROCESS_CPU_STATE_VALUE_SYSTEM"],
    "PROCESS_CPU_STATE_VALUE_USER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["PROCESS_CPU_STATE_VALUE_USER"],
    "PROCESS_CPU_STATE_VALUE_WAIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["PROCESS_CPU_STATE_VALUE_WAIT"],
    "PROCESS_PAGING_FAULT_TYPE_VALUE_MAJOR": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["PROCESS_PAGING_FAULT_TYPE_VALUE_MAJOR"],
    "PROCESS_PAGING_FAULT_TYPE_VALUE_MINOR": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["PROCESS_PAGING_FAULT_TYPE_VALUE_MINOR"],
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_ABORTED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_CONNECT_RPC_ERROR_CODE_VALUE_ABORTED"],
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_ALREADY_EXISTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_CONNECT_RPC_ERROR_CODE_VALUE_ALREADY_EXISTS"],
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_CANCELLED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_CONNECT_RPC_ERROR_CODE_VALUE_CANCELLED"],
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_DATA_LOSS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_CONNECT_RPC_ERROR_CODE_VALUE_DATA_LOSS"],
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_DEADLINE_EXCEEDED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_CONNECT_RPC_ERROR_CODE_VALUE_DEADLINE_EXCEEDED"],
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_FAILED_PRECONDITION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_CONNECT_RPC_ERROR_CODE_VALUE_FAILED_PRECONDITION"],
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_INTERNAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_CONNECT_RPC_ERROR_CODE_VALUE_INTERNAL"],
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_INVALID_ARGUMENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_CONNECT_RPC_ERROR_CODE_VALUE_INVALID_ARGUMENT"],
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_NOT_FOUND": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_CONNECT_RPC_ERROR_CODE_VALUE_NOT_FOUND"],
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_OUT_OF_RANGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_CONNECT_RPC_ERROR_CODE_VALUE_OUT_OF_RANGE"],
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_PERMISSION_DENIED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_CONNECT_RPC_ERROR_CODE_VALUE_PERMISSION_DENIED"],
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_RESOURCE_EXHAUSTED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_CONNECT_RPC_ERROR_CODE_VALUE_RESOURCE_EXHAUSTED"],
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNAUTHENTICATED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNAUTHENTICATED"],
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNAVAILABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNAVAILABLE"],
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNIMPLEMENTED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNIMPLEMENTED"],
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNKNOWN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNKNOWN"],
    "RPC_GRPC_STATUS_CODE_VALUE_ABORTED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_GRPC_STATUS_CODE_VALUE_ABORTED"],
    "RPC_GRPC_STATUS_CODE_VALUE_ALREADY_EXISTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_GRPC_STATUS_CODE_VALUE_ALREADY_EXISTS"],
    "RPC_GRPC_STATUS_CODE_VALUE_CANCELLED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_GRPC_STATUS_CODE_VALUE_CANCELLED"],
    "RPC_GRPC_STATUS_CODE_VALUE_DATA_LOSS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_GRPC_STATUS_CODE_VALUE_DATA_LOSS"],
    "RPC_GRPC_STATUS_CODE_VALUE_DEADLINE_EXCEEDED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_GRPC_STATUS_CODE_VALUE_DEADLINE_EXCEEDED"],
    "RPC_GRPC_STATUS_CODE_VALUE_FAILED_PRECONDITION": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_GRPC_STATUS_CODE_VALUE_FAILED_PRECONDITION"],
    "RPC_GRPC_STATUS_CODE_VALUE_INTERNAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_GRPC_STATUS_CODE_VALUE_INTERNAL"],
    "RPC_GRPC_STATUS_CODE_VALUE_INVALID_ARGUMENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_GRPC_STATUS_CODE_VALUE_INVALID_ARGUMENT"],
    "RPC_GRPC_STATUS_CODE_VALUE_NOT_FOUND": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_GRPC_STATUS_CODE_VALUE_NOT_FOUND"],
    "RPC_GRPC_STATUS_CODE_VALUE_OK": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_GRPC_STATUS_CODE_VALUE_OK"],
    "RPC_GRPC_STATUS_CODE_VALUE_OUT_OF_RANGE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_GRPC_STATUS_CODE_VALUE_OUT_OF_RANGE"],
    "RPC_GRPC_STATUS_CODE_VALUE_PERMISSION_DENIED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_GRPC_STATUS_CODE_VALUE_PERMISSION_DENIED"],
    "RPC_GRPC_STATUS_CODE_VALUE_RESOURCE_EXHAUSTED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_GRPC_STATUS_CODE_VALUE_RESOURCE_EXHAUSTED"],
    "RPC_GRPC_STATUS_CODE_VALUE_UNAUTHENTICATED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_GRPC_STATUS_CODE_VALUE_UNAUTHENTICATED"],
    "RPC_GRPC_STATUS_CODE_VALUE_UNAVAILABLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_GRPC_STATUS_CODE_VALUE_UNAVAILABLE"],
    "RPC_GRPC_STATUS_CODE_VALUE_UNIMPLEMENTED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_GRPC_STATUS_CODE_VALUE_UNIMPLEMENTED"],
    "RPC_GRPC_STATUS_CODE_VALUE_UNKNOWN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_GRPC_STATUS_CODE_VALUE_UNKNOWN"],
    "RPC_MESSAGE_TYPE_VALUE_RECEIVED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_MESSAGE_TYPE_VALUE_RECEIVED"],
    "RPC_MESSAGE_TYPE_VALUE_SENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_MESSAGE_TYPE_VALUE_SENT"],
    "RPC_SYSTEM_VALUE_APACHE_DUBBO": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_SYSTEM_VALUE_APACHE_DUBBO"],
    "RPC_SYSTEM_VALUE_CONNECT_RPC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_SYSTEM_VALUE_CONNECT_RPC"],
    "RPC_SYSTEM_VALUE_DOTNET_WCF": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_SYSTEM_VALUE_DOTNET_WCF"],
    "RPC_SYSTEM_VALUE_GRPC": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_SYSTEM_VALUE_GRPC"],
    "RPC_SYSTEM_VALUE_JAVA_RMI": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["RPC_SYSTEM_VALUE_JAVA_RMI"],
    "SIGNALR_CONNECTION_STATUS_VALUE_APP_SHUTDOWN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SIGNALR_CONNECTION_STATUS_VALUE_APP_SHUTDOWN"],
    "SIGNALR_CONNECTION_STATUS_VALUE_NORMAL_CLOSURE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SIGNALR_CONNECTION_STATUS_VALUE_NORMAL_CLOSURE"],
    "SIGNALR_CONNECTION_STATUS_VALUE_TIMEOUT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SIGNALR_CONNECTION_STATUS_VALUE_TIMEOUT"],
    "SIGNALR_TRANSPORT_VALUE_LONG_POLLING": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SIGNALR_TRANSPORT_VALUE_LONG_POLLING"],
    "SIGNALR_TRANSPORT_VALUE_SERVER_SENT_EVENTS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SIGNALR_TRANSPORT_VALUE_SERVER_SENT_EVENTS"],
    "SIGNALR_TRANSPORT_VALUE_WEB_SOCKETS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SIGNALR_TRANSPORT_VALUE_WEB_SOCKETS"],
    "STATE_VALUE_IDLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["STATE_VALUE_IDLE"],
    "STATE_VALUE_USED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["STATE_VALUE_USED"],
    "SYSTEM_CPU_STATE_VALUE_IDLE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_CPU_STATE_VALUE_IDLE"],
    "SYSTEM_CPU_STATE_VALUE_INTERRUPT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_CPU_STATE_VALUE_INTERRUPT"],
    "SYSTEM_CPU_STATE_VALUE_IOWAIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_CPU_STATE_VALUE_IOWAIT"],
    "SYSTEM_CPU_STATE_VALUE_NICE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_CPU_STATE_VALUE_NICE"],
    "SYSTEM_CPU_STATE_VALUE_STEAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_CPU_STATE_VALUE_STEAL"],
    "SYSTEM_CPU_STATE_VALUE_SYSTEM": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_CPU_STATE_VALUE_SYSTEM"],
    "SYSTEM_CPU_STATE_VALUE_USER": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_CPU_STATE_VALUE_USER"],
    "SYSTEM_FILESYSTEM_STATE_VALUE_FREE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_FILESYSTEM_STATE_VALUE_FREE"],
    "SYSTEM_FILESYSTEM_STATE_VALUE_RESERVED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_FILESYSTEM_STATE_VALUE_RESERVED"],
    "SYSTEM_FILESYSTEM_STATE_VALUE_USED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_FILESYSTEM_STATE_VALUE_USED"],
    "SYSTEM_FILESYSTEM_TYPE_VALUE_EXFAT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_FILESYSTEM_TYPE_VALUE_EXFAT"],
    "SYSTEM_FILESYSTEM_TYPE_VALUE_EXT4": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_FILESYSTEM_TYPE_VALUE_EXT4"],
    "SYSTEM_FILESYSTEM_TYPE_VALUE_FAT32": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_FILESYSTEM_TYPE_VALUE_FAT32"],
    "SYSTEM_FILESYSTEM_TYPE_VALUE_HFSPLUS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_FILESYSTEM_TYPE_VALUE_HFSPLUS"],
    "SYSTEM_FILESYSTEM_TYPE_VALUE_NTFS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_FILESYSTEM_TYPE_VALUE_NTFS"],
    "SYSTEM_FILESYSTEM_TYPE_VALUE_REFS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_FILESYSTEM_TYPE_VALUE_REFS"],
    "SYSTEM_MEMORY_STATE_VALUE_BUFFERS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_MEMORY_STATE_VALUE_BUFFERS"],
    "SYSTEM_MEMORY_STATE_VALUE_CACHED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_MEMORY_STATE_VALUE_CACHED"],
    "SYSTEM_MEMORY_STATE_VALUE_FREE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_MEMORY_STATE_VALUE_FREE"],
    "SYSTEM_MEMORY_STATE_VALUE_SHARED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_MEMORY_STATE_VALUE_SHARED"],
    "SYSTEM_MEMORY_STATE_VALUE_USED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_MEMORY_STATE_VALUE_USED"],
    "SYSTEM_NETWORK_STATE_VALUE_CLOSE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_NETWORK_STATE_VALUE_CLOSE"],
    "SYSTEM_NETWORK_STATE_VALUE_CLOSE_WAIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_NETWORK_STATE_VALUE_CLOSE_WAIT"],
    "SYSTEM_NETWORK_STATE_VALUE_CLOSING": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_NETWORK_STATE_VALUE_CLOSING"],
    "SYSTEM_NETWORK_STATE_VALUE_DELETE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_NETWORK_STATE_VALUE_DELETE"],
    "SYSTEM_NETWORK_STATE_VALUE_ESTABLISHED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_NETWORK_STATE_VALUE_ESTABLISHED"],
    "SYSTEM_NETWORK_STATE_VALUE_FIN_WAIT_1": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_NETWORK_STATE_VALUE_FIN_WAIT_1"],
    "SYSTEM_NETWORK_STATE_VALUE_FIN_WAIT_2": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_NETWORK_STATE_VALUE_FIN_WAIT_2"],
    "SYSTEM_NETWORK_STATE_VALUE_LAST_ACK": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_NETWORK_STATE_VALUE_LAST_ACK"],
    "SYSTEM_NETWORK_STATE_VALUE_LISTEN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_NETWORK_STATE_VALUE_LISTEN"],
    "SYSTEM_NETWORK_STATE_VALUE_SYN_RECV": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_NETWORK_STATE_VALUE_SYN_RECV"],
    "SYSTEM_NETWORK_STATE_VALUE_SYN_SENT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_NETWORK_STATE_VALUE_SYN_SENT"],
    "SYSTEM_NETWORK_STATE_VALUE_TIME_WAIT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_NETWORK_STATE_VALUE_TIME_WAIT"],
    "SYSTEM_PAGING_DIRECTION_VALUE_IN": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_PAGING_DIRECTION_VALUE_IN"],
    "SYSTEM_PAGING_DIRECTION_VALUE_OUT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_PAGING_DIRECTION_VALUE_OUT"],
    "SYSTEM_PAGING_STATE_VALUE_FREE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_PAGING_STATE_VALUE_FREE"],
    "SYSTEM_PAGING_STATE_VALUE_USED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_PAGING_STATE_VALUE_USED"],
    "SYSTEM_PAGING_TYPE_VALUE_MAJOR": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_PAGING_TYPE_VALUE_MAJOR"],
    "SYSTEM_PAGING_TYPE_VALUE_MINOR": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_PAGING_TYPE_VALUE_MINOR"],
    "SYSTEM_PROCESSES_STATUS_VALUE_DEFUNCT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_PROCESSES_STATUS_VALUE_DEFUNCT"],
    "SYSTEM_PROCESSES_STATUS_VALUE_RUNNING": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_PROCESSES_STATUS_VALUE_RUNNING"],
    "SYSTEM_PROCESSES_STATUS_VALUE_SLEEPING": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_PROCESSES_STATUS_VALUE_SLEEPING"],
    "SYSTEM_PROCESSES_STATUS_VALUE_STOPPED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_PROCESSES_STATUS_VALUE_STOPPED"],
    "SYSTEM_PROCESS_STATUS_VALUE_DEFUNCT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_PROCESS_STATUS_VALUE_DEFUNCT"],
    "SYSTEM_PROCESS_STATUS_VALUE_RUNNING": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_PROCESS_STATUS_VALUE_RUNNING"],
    "SYSTEM_PROCESS_STATUS_VALUE_SLEEPING": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_PROCESS_STATUS_VALUE_SLEEPING"],
    "SYSTEM_PROCESS_STATUS_VALUE_STOPPED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["SYSTEM_PROCESS_STATUS_VALUE_STOPPED"],
    "TELEMETRY_SDK_LANGUAGE_VALUE_CPP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TELEMETRY_SDK_LANGUAGE_VALUE_CPP"],
    "TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET"],
    "TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG"],
    "TELEMETRY_SDK_LANGUAGE_VALUE_GO": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TELEMETRY_SDK_LANGUAGE_VALUE_GO"],
    "TELEMETRY_SDK_LANGUAGE_VALUE_JAVA": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TELEMETRY_SDK_LANGUAGE_VALUE_JAVA"],
    "TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS"],
    "TELEMETRY_SDK_LANGUAGE_VALUE_PHP": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TELEMETRY_SDK_LANGUAGE_VALUE_PHP"],
    "TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON"],
    "TELEMETRY_SDK_LANGUAGE_VALUE_RUBY": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TELEMETRY_SDK_LANGUAGE_VALUE_RUBY"],
    "TELEMETRY_SDK_LANGUAGE_VALUE_RUST": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TELEMETRY_SDK_LANGUAGE_VALUE_RUST"],
    "TELEMETRY_SDK_LANGUAGE_VALUE_SWIFT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TELEMETRY_SDK_LANGUAGE_VALUE_SWIFT"],
    "TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS"],
    "TEST_CASE_RESULT_STATUS_VALUE_FAIL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TEST_CASE_RESULT_STATUS_VALUE_FAIL"],
    "TEST_CASE_RESULT_STATUS_VALUE_PASS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TEST_CASE_RESULT_STATUS_VALUE_PASS"],
    "TEST_SUITE_RUN_STATUS_VALUE_ABORTED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TEST_SUITE_RUN_STATUS_VALUE_ABORTED"],
    "TEST_SUITE_RUN_STATUS_VALUE_FAILURE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TEST_SUITE_RUN_STATUS_VALUE_FAILURE"],
    "TEST_SUITE_RUN_STATUS_VALUE_IN_PROGRESS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TEST_SUITE_RUN_STATUS_VALUE_IN_PROGRESS"],
    "TEST_SUITE_RUN_STATUS_VALUE_SKIPPED": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TEST_SUITE_RUN_STATUS_VALUE_SKIPPED"],
    "TEST_SUITE_RUN_STATUS_VALUE_SUCCESS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TEST_SUITE_RUN_STATUS_VALUE_SUCCESS"],
    "TEST_SUITE_RUN_STATUS_VALUE_TIMED_OUT": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TEST_SUITE_RUN_STATUS_VALUE_TIMED_OUT"],
    "TLS_PROTOCOL_NAME_VALUE_SSL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TLS_PROTOCOL_NAME_VALUE_SSL"],
    "TLS_PROTOCOL_NAME_VALUE_TLS": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["TLS_PROTOCOL_NAME_VALUE_TLS"],
    "V8JS_GC_TYPE_VALUE_INCREMENTAL": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["V8JS_GC_TYPE_VALUE_INCREMENTAL"],
    "V8JS_GC_TYPE_VALUE_MAJOR": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["V8JS_GC_TYPE_VALUE_MAJOR"],
    "V8JS_GC_TYPE_VALUE_MINOR": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["V8JS_GC_TYPE_VALUE_MINOR"],
    "V8JS_GC_TYPE_VALUE_WEAKCB": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["V8JS_GC_TYPE_VALUE_WEAKCB"],
    "V8JS_HEAP_SPACE_NAME_VALUE_CODE_SPACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["V8JS_HEAP_SPACE_NAME_VALUE_CODE_SPACE"],
    "V8JS_HEAP_SPACE_NAME_VALUE_LARGE_OBJECT_SPACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["V8JS_HEAP_SPACE_NAME_VALUE_LARGE_OBJECT_SPACE"],
    "V8JS_HEAP_SPACE_NAME_VALUE_MAP_SPACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["V8JS_HEAP_SPACE_NAME_VALUE_MAP_SPACE"],
    "V8JS_HEAP_SPACE_NAME_VALUE_NEW_SPACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["V8JS_HEAP_SPACE_NAME_VALUE_NEW_SPACE"],
    "V8JS_HEAP_SPACE_NAME_VALUE_OLD_SPACE": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["V8JS_HEAP_SPACE_NAME_VALUE_OLD_SPACE"],
    "VCS_REPOSITORY_REF_TYPE_VALUE_BRANCH": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["VCS_REPOSITORY_REF_TYPE_VALUE_BRANCH"],
    "VCS_REPOSITORY_REF_TYPE_VALUE_TAG": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__["VCS_REPOSITORY_REF_TYPE_VALUE_TAG"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js [instrumentation] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$stable_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_metrics.js [instrumentation] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_attributes$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.js [instrumentation] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$experimental_metrics$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_metrics.js [instrumentation] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$index$2d$incubating$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index-incubating.js [instrumentation] (ecmascript) <locals>");
}),

};