module.exports = {

"[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js [instrumentation] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /* eslint-disable no-restricted-syntax --
 * These re-exports are only of constants, only two-levels deep, and
 * should not cause problems for tree-shakers.
 */ // Deprecated. These are kept around for compatibility purposes
__turbopack_context__.s({});
;
;
;
;
 //# sourceMappingURL=index.js.map
}),
"[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js [instrumentation] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$index$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js [instrumentation] (ecmascript) <locals>");
}),
"[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/internal/utils.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Creates a const map from the given values
 * @param values - An array of values to be used as keys and values in the map.
 * @returns A populated version of the map with the values and keys derived from the values.
 */ /*#__NO_SIDE_EFFECTS__*/ __turbopack_context__.s({
    "createConstMap": ()=>createConstMap
});
function createConstMap(values) {
    // eslint-disable-next-line prefer-const, @typescript-eslint/no-explicit-any
    var res = {};
    var len = values.length;
    for(var lp = 0; lp < len; lp++){
        var val = values[lp];
        if (val) {
            res[String(val).toUpperCase().replace(/[-.]/g, '_')] = val;
        }
    }
    return res;
} //# sourceMappingURL=utils.js.map
}),
"[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_attributes.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ //----------------------------------------------------------------------------------------------------------
// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates/registry/stable/attributes.ts.j2
//----------------------------------------------------------------------------------------------------------
/**
 * Rate-limiting result, shows whether the lease was acquired or contains a rejection reason
 *
 * @example acquired
 *
 * @example request_canceled
 */ __turbopack_context__.s({
    "ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_ABORTED": ()=>ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_ABORTED,
    "ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_HANDLED": ()=>ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_HANDLED,
    "ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_SKIPPED": ()=>ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_SKIPPED,
    "ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_UNHANDLED": ()=>ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_UNHANDLED,
    "ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ACQUIRED": ()=>ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ACQUIRED,
    "ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ENDPOINT_LIMITER": ()=>ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ENDPOINT_LIMITER,
    "ASPNETCORE_RATE_LIMITING_RESULT_VALUE_GLOBAL_LIMITER": ()=>ASPNETCORE_RATE_LIMITING_RESULT_VALUE_GLOBAL_LIMITER,
    "ASPNETCORE_RATE_LIMITING_RESULT_VALUE_REQUEST_CANCELED": ()=>ASPNETCORE_RATE_LIMITING_RESULT_VALUE_REQUEST_CANCELED,
    "ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_FAILURE": ()=>ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_FAILURE,
    "ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_SUCCESS": ()=>ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_SUCCESS,
    "ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT": ()=>ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT,
    "ATTR_ASPNETCORE_DIAGNOSTICS_HANDLER_TYPE": ()=>ATTR_ASPNETCORE_DIAGNOSTICS_HANDLER_TYPE,
    "ATTR_ASPNETCORE_RATE_LIMITING_POLICY": ()=>ATTR_ASPNETCORE_RATE_LIMITING_POLICY,
    "ATTR_ASPNETCORE_RATE_LIMITING_RESULT": ()=>ATTR_ASPNETCORE_RATE_LIMITING_RESULT,
    "ATTR_ASPNETCORE_REQUEST_IS_UNHANDLED": ()=>ATTR_ASPNETCORE_REQUEST_IS_UNHANDLED,
    "ATTR_ASPNETCORE_ROUTING_IS_FALLBACK": ()=>ATTR_ASPNETCORE_ROUTING_IS_FALLBACK,
    "ATTR_ASPNETCORE_ROUTING_MATCH_STATUS": ()=>ATTR_ASPNETCORE_ROUTING_MATCH_STATUS,
    "ATTR_CLIENT_ADDRESS": ()=>ATTR_CLIENT_ADDRESS,
    "ATTR_CLIENT_PORT": ()=>ATTR_CLIENT_PORT,
    "ATTR_ERROR_TYPE": ()=>ATTR_ERROR_TYPE,
    "ATTR_EXCEPTION_ESCAPED": ()=>ATTR_EXCEPTION_ESCAPED,
    "ATTR_EXCEPTION_MESSAGE": ()=>ATTR_EXCEPTION_MESSAGE,
    "ATTR_EXCEPTION_STACKTRACE": ()=>ATTR_EXCEPTION_STACKTRACE,
    "ATTR_EXCEPTION_TYPE": ()=>ATTR_EXCEPTION_TYPE,
    "ATTR_HTTP_REQUEST_HEADER": ()=>ATTR_HTTP_REQUEST_HEADER,
    "ATTR_HTTP_REQUEST_METHOD": ()=>ATTR_HTTP_REQUEST_METHOD,
    "ATTR_HTTP_REQUEST_METHOD_ORIGINAL": ()=>ATTR_HTTP_REQUEST_METHOD_ORIGINAL,
    "ATTR_HTTP_REQUEST_RESEND_COUNT": ()=>ATTR_HTTP_REQUEST_RESEND_COUNT,
    "ATTR_HTTP_RESPONSE_HEADER": ()=>ATTR_HTTP_RESPONSE_HEADER,
    "ATTR_HTTP_RESPONSE_STATUS_CODE": ()=>ATTR_HTTP_RESPONSE_STATUS_CODE,
    "ATTR_HTTP_ROUTE": ()=>ATTR_HTTP_ROUTE,
    "ATTR_JVM_GC_ACTION": ()=>ATTR_JVM_GC_ACTION,
    "ATTR_JVM_GC_NAME": ()=>ATTR_JVM_GC_NAME,
    "ATTR_JVM_MEMORY_POOL_NAME": ()=>ATTR_JVM_MEMORY_POOL_NAME,
    "ATTR_JVM_MEMORY_TYPE": ()=>ATTR_JVM_MEMORY_TYPE,
    "ATTR_JVM_THREAD_DAEMON": ()=>ATTR_JVM_THREAD_DAEMON,
    "ATTR_JVM_THREAD_STATE": ()=>ATTR_JVM_THREAD_STATE,
    "ATTR_NETWORK_LOCAL_ADDRESS": ()=>ATTR_NETWORK_LOCAL_ADDRESS,
    "ATTR_NETWORK_LOCAL_PORT": ()=>ATTR_NETWORK_LOCAL_PORT,
    "ATTR_NETWORK_PEER_ADDRESS": ()=>ATTR_NETWORK_PEER_ADDRESS,
    "ATTR_NETWORK_PEER_PORT": ()=>ATTR_NETWORK_PEER_PORT,
    "ATTR_NETWORK_PROTOCOL_NAME": ()=>ATTR_NETWORK_PROTOCOL_NAME,
    "ATTR_NETWORK_PROTOCOL_VERSION": ()=>ATTR_NETWORK_PROTOCOL_VERSION,
    "ATTR_NETWORK_TRANSPORT": ()=>ATTR_NETWORK_TRANSPORT,
    "ATTR_NETWORK_TYPE": ()=>ATTR_NETWORK_TYPE,
    "ATTR_OTEL_SCOPE_NAME": ()=>ATTR_OTEL_SCOPE_NAME,
    "ATTR_OTEL_SCOPE_VERSION": ()=>ATTR_OTEL_SCOPE_VERSION,
    "ATTR_OTEL_STATUS_CODE": ()=>ATTR_OTEL_STATUS_CODE,
    "ATTR_OTEL_STATUS_DESCRIPTION": ()=>ATTR_OTEL_STATUS_DESCRIPTION,
    "ATTR_SERVER_ADDRESS": ()=>ATTR_SERVER_ADDRESS,
    "ATTR_SERVER_PORT": ()=>ATTR_SERVER_PORT,
    "ATTR_SERVICE_NAME": ()=>ATTR_SERVICE_NAME,
    "ATTR_SERVICE_VERSION": ()=>ATTR_SERVICE_VERSION,
    "ATTR_SIGNALR_CONNECTION_STATUS": ()=>ATTR_SIGNALR_CONNECTION_STATUS,
    "ATTR_SIGNALR_TRANSPORT": ()=>ATTR_SIGNALR_TRANSPORT,
    "ATTR_TELEMETRY_SDK_LANGUAGE": ()=>ATTR_TELEMETRY_SDK_LANGUAGE,
    "ATTR_TELEMETRY_SDK_NAME": ()=>ATTR_TELEMETRY_SDK_NAME,
    "ATTR_TELEMETRY_SDK_VERSION": ()=>ATTR_TELEMETRY_SDK_VERSION,
    "ATTR_URL_FRAGMENT": ()=>ATTR_URL_FRAGMENT,
    "ATTR_URL_FULL": ()=>ATTR_URL_FULL,
    "ATTR_URL_PATH": ()=>ATTR_URL_PATH,
    "ATTR_URL_QUERY": ()=>ATTR_URL_QUERY,
    "ATTR_URL_SCHEME": ()=>ATTR_URL_SCHEME,
    "ATTR_USER_AGENT_ORIGINAL": ()=>ATTR_USER_AGENT_ORIGINAL,
    "ERROR_TYPE_VALUE_OTHER": ()=>ERROR_TYPE_VALUE_OTHER,
    "HTTP_REQUEST_METHOD_VALUE_CONNECT": ()=>HTTP_REQUEST_METHOD_VALUE_CONNECT,
    "HTTP_REQUEST_METHOD_VALUE_DELETE": ()=>HTTP_REQUEST_METHOD_VALUE_DELETE,
    "HTTP_REQUEST_METHOD_VALUE_GET": ()=>HTTP_REQUEST_METHOD_VALUE_GET,
    "HTTP_REQUEST_METHOD_VALUE_HEAD": ()=>HTTP_REQUEST_METHOD_VALUE_HEAD,
    "HTTP_REQUEST_METHOD_VALUE_OPTIONS": ()=>HTTP_REQUEST_METHOD_VALUE_OPTIONS,
    "HTTP_REQUEST_METHOD_VALUE_OTHER": ()=>HTTP_REQUEST_METHOD_VALUE_OTHER,
    "HTTP_REQUEST_METHOD_VALUE_PATCH": ()=>HTTP_REQUEST_METHOD_VALUE_PATCH,
    "HTTP_REQUEST_METHOD_VALUE_POST": ()=>HTTP_REQUEST_METHOD_VALUE_POST,
    "HTTP_REQUEST_METHOD_VALUE_PUT": ()=>HTTP_REQUEST_METHOD_VALUE_PUT,
    "HTTP_REQUEST_METHOD_VALUE_TRACE": ()=>HTTP_REQUEST_METHOD_VALUE_TRACE,
    "JVM_MEMORY_TYPE_VALUE_HEAP": ()=>JVM_MEMORY_TYPE_VALUE_HEAP,
    "JVM_MEMORY_TYPE_VALUE_NON_HEAP": ()=>JVM_MEMORY_TYPE_VALUE_NON_HEAP,
    "JVM_THREAD_STATE_VALUE_BLOCKED": ()=>JVM_THREAD_STATE_VALUE_BLOCKED,
    "JVM_THREAD_STATE_VALUE_NEW": ()=>JVM_THREAD_STATE_VALUE_NEW,
    "JVM_THREAD_STATE_VALUE_RUNNABLE": ()=>JVM_THREAD_STATE_VALUE_RUNNABLE,
    "JVM_THREAD_STATE_VALUE_TERMINATED": ()=>JVM_THREAD_STATE_VALUE_TERMINATED,
    "JVM_THREAD_STATE_VALUE_TIMED_WAITING": ()=>JVM_THREAD_STATE_VALUE_TIMED_WAITING,
    "JVM_THREAD_STATE_VALUE_WAITING": ()=>JVM_THREAD_STATE_VALUE_WAITING,
    "NETWORK_TRANSPORT_VALUE_PIPE": ()=>NETWORK_TRANSPORT_VALUE_PIPE,
    "NETWORK_TRANSPORT_VALUE_QUIC": ()=>NETWORK_TRANSPORT_VALUE_QUIC,
    "NETWORK_TRANSPORT_VALUE_TCP": ()=>NETWORK_TRANSPORT_VALUE_TCP,
    "NETWORK_TRANSPORT_VALUE_UDP": ()=>NETWORK_TRANSPORT_VALUE_UDP,
    "NETWORK_TRANSPORT_VALUE_UNIX": ()=>NETWORK_TRANSPORT_VALUE_UNIX,
    "NETWORK_TYPE_VALUE_IPV4": ()=>NETWORK_TYPE_VALUE_IPV4,
    "NETWORK_TYPE_VALUE_IPV6": ()=>NETWORK_TYPE_VALUE_IPV6,
    "OTEL_STATUS_CODE_VALUE_ERROR": ()=>OTEL_STATUS_CODE_VALUE_ERROR,
    "OTEL_STATUS_CODE_VALUE_OK": ()=>OTEL_STATUS_CODE_VALUE_OK,
    "SIGNALR_CONNECTION_STATUS_VALUE_APP_SHUTDOWN": ()=>SIGNALR_CONNECTION_STATUS_VALUE_APP_SHUTDOWN,
    "SIGNALR_CONNECTION_STATUS_VALUE_NORMAL_CLOSURE": ()=>SIGNALR_CONNECTION_STATUS_VALUE_NORMAL_CLOSURE,
    "SIGNALR_CONNECTION_STATUS_VALUE_TIMEOUT": ()=>SIGNALR_CONNECTION_STATUS_VALUE_TIMEOUT,
    "SIGNALR_TRANSPORT_VALUE_LONG_POLLING": ()=>SIGNALR_TRANSPORT_VALUE_LONG_POLLING,
    "SIGNALR_TRANSPORT_VALUE_SERVER_SENT_EVENTS": ()=>SIGNALR_TRANSPORT_VALUE_SERVER_SENT_EVENTS,
    "SIGNALR_TRANSPORT_VALUE_WEB_SOCKETS": ()=>SIGNALR_TRANSPORT_VALUE_WEB_SOCKETS,
    "TELEMETRY_SDK_LANGUAGE_VALUE_CPP": ()=>TELEMETRY_SDK_LANGUAGE_VALUE_CPP,
    "TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET": ()=>TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET,
    "TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG": ()=>TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG,
    "TELEMETRY_SDK_LANGUAGE_VALUE_GO": ()=>TELEMETRY_SDK_LANGUAGE_VALUE_GO,
    "TELEMETRY_SDK_LANGUAGE_VALUE_JAVA": ()=>TELEMETRY_SDK_LANGUAGE_VALUE_JAVA,
    "TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS": ()=>TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS,
    "TELEMETRY_SDK_LANGUAGE_VALUE_PHP": ()=>TELEMETRY_SDK_LANGUAGE_VALUE_PHP,
    "TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON": ()=>TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON,
    "TELEMETRY_SDK_LANGUAGE_VALUE_RUBY": ()=>TELEMETRY_SDK_LANGUAGE_VALUE_RUBY,
    "TELEMETRY_SDK_LANGUAGE_VALUE_RUST": ()=>TELEMETRY_SDK_LANGUAGE_VALUE_RUST,
    "TELEMETRY_SDK_LANGUAGE_VALUE_SWIFT": ()=>TELEMETRY_SDK_LANGUAGE_VALUE_SWIFT,
    "TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS": ()=>TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS
});
var ATTR_ASPNETCORE_RATE_LIMITING_RESULT = 'aspnetcore.rate_limiting.result';
var ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ACQUIRED = "acquired";
var ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ENDPOINT_LIMITER = "endpoint_limiter";
var ASPNETCORE_RATE_LIMITING_RESULT_VALUE_GLOBAL_LIMITER = "global_limiter";
var ASPNETCORE_RATE_LIMITING_RESULT_VALUE_REQUEST_CANCELED = "request_canceled";
var ATTR_TELEMETRY_SDK_LANGUAGE = 'telemetry.sdk.language';
var TELEMETRY_SDK_LANGUAGE_VALUE_CPP = "cpp";
var TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET = "dotnet";
var TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG = "erlang";
var TELEMETRY_SDK_LANGUAGE_VALUE_GO = "go";
var TELEMETRY_SDK_LANGUAGE_VALUE_JAVA = "java";
var TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS = "nodejs";
var TELEMETRY_SDK_LANGUAGE_VALUE_PHP = "php";
var TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON = "python";
var TELEMETRY_SDK_LANGUAGE_VALUE_RUBY = "ruby";
var TELEMETRY_SDK_LANGUAGE_VALUE_RUST = "rust";
var TELEMETRY_SDK_LANGUAGE_VALUE_SWIFT = "swift";
var TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS = "webjs";
var ATTR_TELEMETRY_SDK_NAME = 'telemetry.sdk.name';
var ATTR_TELEMETRY_SDK_VERSION = 'telemetry.sdk.version';
var ATTR_ASPNETCORE_DIAGNOSTICS_HANDLER_TYPE = 'aspnetcore.diagnostics.handler.type';
var ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT = 'aspnetcore.diagnostics.exception.result';
var ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_ABORTED = "aborted";
var ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_HANDLED = "handled";
var ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_SKIPPED = "skipped";
var ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_UNHANDLED = "unhandled";
var ATTR_ASPNETCORE_RATE_LIMITING_POLICY = 'aspnetcore.rate_limiting.policy';
var ATTR_ASPNETCORE_REQUEST_IS_UNHANDLED = 'aspnetcore.request.is_unhandled';
var ATTR_ASPNETCORE_ROUTING_IS_FALLBACK = 'aspnetcore.routing.is_fallback';
var ATTR_ASPNETCORE_ROUTING_MATCH_STATUS = 'aspnetcore.routing.match_status';
var ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_FAILURE = "failure";
var ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_SUCCESS = "success";
var ATTR_CLIENT_ADDRESS = 'client.address';
var ATTR_CLIENT_PORT = 'client.port';
var ATTR_ERROR_TYPE = 'error.type';
var ERROR_TYPE_VALUE_OTHER = "_OTHER";
var ATTR_EXCEPTION_ESCAPED = 'exception.escaped';
var ATTR_EXCEPTION_MESSAGE = 'exception.message';
var ATTR_EXCEPTION_STACKTRACE = 'exception.stacktrace';
var ATTR_EXCEPTION_TYPE = 'exception.type';
var ATTR_HTTP_REQUEST_HEADER = function(key) {
    return "http.request.header." + key;
};
var ATTR_HTTP_REQUEST_METHOD = 'http.request.method';
var HTTP_REQUEST_METHOD_VALUE_OTHER = "_OTHER";
var HTTP_REQUEST_METHOD_VALUE_CONNECT = "CONNECT";
var HTTP_REQUEST_METHOD_VALUE_DELETE = "DELETE";
var HTTP_REQUEST_METHOD_VALUE_GET = "GET";
var HTTP_REQUEST_METHOD_VALUE_HEAD = "HEAD";
var HTTP_REQUEST_METHOD_VALUE_OPTIONS = "OPTIONS";
var HTTP_REQUEST_METHOD_VALUE_PATCH = "PATCH";
var HTTP_REQUEST_METHOD_VALUE_POST = "POST";
var HTTP_REQUEST_METHOD_VALUE_PUT = "PUT";
var HTTP_REQUEST_METHOD_VALUE_TRACE = "TRACE";
var ATTR_HTTP_REQUEST_METHOD_ORIGINAL = 'http.request.method_original';
var ATTR_HTTP_REQUEST_RESEND_COUNT = 'http.request.resend_count';
var ATTR_HTTP_RESPONSE_HEADER = function(key) {
    return "http.response.header." + key;
};
var ATTR_HTTP_RESPONSE_STATUS_CODE = 'http.response.status_code';
var ATTR_HTTP_ROUTE = 'http.route';
var ATTR_JVM_GC_ACTION = 'jvm.gc.action';
var ATTR_JVM_GC_NAME = 'jvm.gc.name';
var ATTR_JVM_MEMORY_POOL_NAME = 'jvm.memory.pool.name';
var ATTR_JVM_MEMORY_TYPE = 'jvm.memory.type';
var JVM_MEMORY_TYPE_VALUE_HEAP = "heap";
var JVM_MEMORY_TYPE_VALUE_NON_HEAP = "non_heap";
var ATTR_JVM_THREAD_DAEMON = 'jvm.thread.daemon';
var ATTR_JVM_THREAD_STATE = 'jvm.thread.state';
var JVM_THREAD_STATE_VALUE_BLOCKED = "blocked";
var JVM_THREAD_STATE_VALUE_NEW = "new";
var JVM_THREAD_STATE_VALUE_RUNNABLE = "runnable";
var JVM_THREAD_STATE_VALUE_TERMINATED = "terminated";
var JVM_THREAD_STATE_VALUE_TIMED_WAITING = "timed_waiting";
var JVM_THREAD_STATE_VALUE_WAITING = "waiting";
var ATTR_NETWORK_LOCAL_ADDRESS = 'network.local.address';
var ATTR_NETWORK_LOCAL_PORT = 'network.local.port';
var ATTR_NETWORK_PEER_ADDRESS = 'network.peer.address';
var ATTR_NETWORK_PEER_PORT = 'network.peer.port';
var ATTR_NETWORK_PROTOCOL_NAME = 'network.protocol.name';
var ATTR_NETWORK_PROTOCOL_VERSION = 'network.protocol.version';
var ATTR_NETWORK_TRANSPORT = 'network.transport';
var NETWORK_TRANSPORT_VALUE_PIPE = "pipe";
var NETWORK_TRANSPORT_VALUE_QUIC = "quic";
var NETWORK_TRANSPORT_VALUE_TCP = "tcp";
var NETWORK_TRANSPORT_VALUE_UDP = "udp";
var NETWORK_TRANSPORT_VALUE_UNIX = "unix";
var ATTR_NETWORK_TYPE = 'network.type';
var NETWORK_TYPE_VALUE_IPV4 = "ipv4";
var NETWORK_TYPE_VALUE_IPV6 = "ipv6";
var ATTR_OTEL_SCOPE_NAME = 'otel.scope.name';
var ATTR_OTEL_SCOPE_VERSION = 'otel.scope.version';
var ATTR_OTEL_STATUS_CODE = 'otel.status_code';
var OTEL_STATUS_CODE_VALUE_ERROR = "ERROR";
var OTEL_STATUS_CODE_VALUE_OK = "OK";
var ATTR_OTEL_STATUS_DESCRIPTION = 'otel.status_description';
var ATTR_SERVER_ADDRESS = 'server.address';
var ATTR_SERVER_PORT = 'server.port';
var ATTR_SERVICE_NAME = 'service.name';
var ATTR_SERVICE_VERSION = 'service.version';
var ATTR_SIGNALR_CONNECTION_STATUS = 'signalr.connection.status';
var SIGNALR_CONNECTION_STATUS_VALUE_APP_SHUTDOWN = "app_shutdown";
var SIGNALR_CONNECTION_STATUS_VALUE_NORMAL_CLOSURE = "normal_closure";
var SIGNALR_CONNECTION_STATUS_VALUE_TIMEOUT = "timeout";
var ATTR_SIGNALR_TRANSPORT = 'signalr.transport';
var SIGNALR_TRANSPORT_VALUE_LONG_POLLING = "long_polling";
var SIGNALR_TRANSPORT_VALUE_SERVER_SENT_EVENTS = "server_sent_events";
var SIGNALR_TRANSPORT_VALUE_WEB_SOCKETS = "web_sockets";
var ATTR_URL_FRAGMENT = 'url.fragment';
var ATTR_URL_FULL = 'url.full';
var ATTR_URL_PATH = 'url.path';
var ATTR_URL_QUERY = 'url.query';
var ATTR_URL_SCHEME = 'url.scheme';
var ATTR_USER_AGENT_ORIGINAL = 'user_agent.original'; //# sourceMappingURL=stable_attributes.js.map
}),
"[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/stable_metrics.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ //----------------------------------------------------------------------------------------------------------
// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates/register/stable/metrics.ts.j2
//----------------------------------------------------------------------------------------------------------
/**
 * Number of exceptions caught by exception handling middleware.
 *
 * @note Meter name: `Microsoft.AspNetCore.Diagnostics`; Added in: ASP.NET Core 8.0
 */ __turbopack_context__.s({
    "METRIC_ASPNETCORE_DIAGNOSTICS_EXCEPTIONS": ()=>METRIC_ASPNETCORE_DIAGNOSTICS_EXCEPTIONS,
    "METRIC_ASPNETCORE_RATE_LIMITING_ACTIVE_REQUEST_LEASES": ()=>METRIC_ASPNETCORE_RATE_LIMITING_ACTIVE_REQUEST_LEASES,
    "METRIC_ASPNETCORE_RATE_LIMITING_QUEUED_REQUESTS": ()=>METRIC_ASPNETCORE_RATE_LIMITING_QUEUED_REQUESTS,
    "METRIC_ASPNETCORE_RATE_LIMITING_REQUESTS": ()=>METRIC_ASPNETCORE_RATE_LIMITING_REQUESTS,
    "METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_LEASE_DURATION": ()=>METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_LEASE_DURATION,
    "METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_TIME_IN_QUEUE": ()=>METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_TIME_IN_QUEUE,
    "METRIC_ASPNETCORE_ROUTING_MATCH_ATTEMPTS": ()=>METRIC_ASPNETCORE_ROUTING_MATCH_ATTEMPTS,
    "METRIC_HTTP_CLIENT_REQUEST_DURATION": ()=>METRIC_HTTP_CLIENT_REQUEST_DURATION,
    "METRIC_HTTP_SERVER_REQUEST_DURATION": ()=>METRIC_HTTP_SERVER_REQUEST_DURATION,
    "METRIC_JVM_CLASS_COUNT": ()=>METRIC_JVM_CLASS_COUNT,
    "METRIC_JVM_CLASS_LOADED": ()=>METRIC_JVM_CLASS_LOADED,
    "METRIC_JVM_CLASS_UNLOADED": ()=>METRIC_JVM_CLASS_UNLOADED,
    "METRIC_JVM_CPU_COUNT": ()=>METRIC_JVM_CPU_COUNT,
    "METRIC_JVM_CPU_RECENT_UTILIZATION": ()=>METRIC_JVM_CPU_RECENT_UTILIZATION,
    "METRIC_JVM_CPU_TIME": ()=>METRIC_JVM_CPU_TIME,
    "METRIC_JVM_GC_DURATION": ()=>METRIC_JVM_GC_DURATION,
    "METRIC_JVM_MEMORY_COMMITTED": ()=>METRIC_JVM_MEMORY_COMMITTED,
    "METRIC_JVM_MEMORY_LIMIT": ()=>METRIC_JVM_MEMORY_LIMIT,
    "METRIC_JVM_MEMORY_USED": ()=>METRIC_JVM_MEMORY_USED,
    "METRIC_JVM_MEMORY_USED_AFTER_LAST_GC": ()=>METRIC_JVM_MEMORY_USED_AFTER_LAST_GC,
    "METRIC_JVM_THREAD_COUNT": ()=>METRIC_JVM_THREAD_COUNT,
    "METRIC_KESTREL_ACTIVE_CONNECTIONS": ()=>METRIC_KESTREL_ACTIVE_CONNECTIONS,
    "METRIC_KESTREL_ACTIVE_TLS_HANDSHAKES": ()=>METRIC_KESTREL_ACTIVE_TLS_HANDSHAKES,
    "METRIC_KESTREL_CONNECTION_DURATION": ()=>METRIC_KESTREL_CONNECTION_DURATION,
    "METRIC_KESTREL_QUEUED_CONNECTIONS": ()=>METRIC_KESTREL_QUEUED_CONNECTIONS,
    "METRIC_KESTREL_QUEUED_REQUESTS": ()=>METRIC_KESTREL_QUEUED_REQUESTS,
    "METRIC_KESTREL_REJECTED_CONNECTIONS": ()=>METRIC_KESTREL_REJECTED_CONNECTIONS,
    "METRIC_KESTREL_TLS_HANDSHAKE_DURATION": ()=>METRIC_KESTREL_TLS_HANDSHAKE_DURATION,
    "METRIC_KESTREL_UPGRADED_CONNECTIONS": ()=>METRIC_KESTREL_UPGRADED_CONNECTIONS,
    "METRIC_SIGNALR_SERVER_ACTIVE_CONNECTIONS": ()=>METRIC_SIGNALR_SERVER_ACTIVE_CONNECTIONS,
    "METRIC_SIGNALR_SERVER_CONNECTION_DURATION": ()=>METRIC_SIGNALR_SERVER_CONNECTION_DURATION
});
var METRIC_ASPNETCORE_DIAGNOSTICS_EXCEPTIONS = 'aspnetcore.diagnostics.exceptions';
var METRIC_ASPNETCORE_RATE_LIMITING_ACTIVE_REQUEST_LEASES = 'aspnetcore.rate_limiting.active_request_leases';
var METRIC_ASPNETCORE_RATE_LIMITING_QUEUED_REQUESTS = 'aspnetcore.rate_limiting.queued_requests';
var METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_TIME_IN_QUEUE = 'aspnetcore.rate_limiting.request.time_in_queue';
var METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_LEASE_DURATION = 'aspnetcore.rate_limiting.request_lease.duration';
var METRIC_ASPNETCORE_RATE_LIMITING_REQUESTS = 'aspnetcore.rate_limiting.requests';
var METRIC_ASPNETCORE_ROUTING_MATCH_ATTEMPTS = 'aspnetcore.routing.match_attempts';
var METRIC_HTTP_CLIENT_REQUEST_DURATION = 'http.client.request.duration';
var METRIC_HTTP_SERVER_REQUEST_DURATION = 'http.server.request.duration';
var METRIC_JVM_CLASS_COUNT = 'jvm.class.count';
var METRIC_JVM_CLASS_LOADED = 'jvm.class.loaded';
var METRIC_JVM_CLASS_UNLOADED = 'jvm.class.unloaded';
var METRIC_JVM_CPU_COUNT = 'jvm.cpu.count';
var METRIC_JVM_CPU_RECENT_UTILIZATION = 'jvm.cpu.recent_utilization';
var METRIC_JVM_CPU_TIME = 'jvm.cpu.time';
var METRIC_JVM_GC_DURATION = 'jvm.gc.duration';
var METRIC_JVM_MEMORY_COMMITTED = 'jvm.memory.committed';
var METRIC_JVM_MEMORY_LIMIT = 'jvm.memory.limit';
var METRIC_JVM_MEMORY_USED = 'jvm.memory.used';
var METRIC_JVM_MEMORY_USED_AFTER_LAST_GC = 'jvm.memory.used_after_last_gc';
var METRIC_JVM_THREAD_COUNT = 'jvm.thread.count';
var METRIC_KESTREL_ACTIVE_CONNECTIONS = 'kestrel.active_connections';
var METRIC_KESTREL_ACTIVE_TLS_HANDSHAKES = 'kestrel.active_tls_handshakes';
var METRIC_KESTREL_CONNECTION_DURATION = 'kestrel.connection.duration';
var METRIC_KESTREL_QUEUED_CONNECTIONS = 'kestrel.queued_connections';
var METRIC_KESTREL_QUEUED_REQUESTS = 'kestrel.queued_requests';
var METRIC_KESTREL_REJECTED_CONNECTIONS = 'kestrel.rejected_connections';
var METRIC_KESTREL_TLS_HANDSHAKE_DURATION = 'kestrel.tls_handshake.duration';
var METRIC_KESTREL_UPGRADED_CONNECTIONS = 'kestrel.upgraded_connections';
var METRIC_SIGNALR_SERVER_ACTIVE_CONNECTIONS = 'signalr.server.active_connections';
var METRIC_SIGNALR_SERVER_CONNECTION_DURATION = 'signalr.server.connection.duration'; //# sourceMappingURL=stable_metrics.js.map
}),
"[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index-incubating.js [instrumentation] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ // Incubating export also contains stable constants in order to maintain
// backward compatibility between minor version releases
__turbopack_context__.s({});
;
;
;
;
 //# sourceMappingURL=index-incubating.js.map
}),
"[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index-incubating.js [instrumentation] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f40$opentelemetry$2b$semantic$2d$conventions$40$1$2e$27$2e$0$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$index$2d$incubating$2e$js__$5b$instrumentation$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/index-incubating.js [instrumentation] (ecmascript) <locals>");
}),
"[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_attributes.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ //----------------------------------------------------------------------------------------------------------
// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates/registry/stable/attributes.ts.j2
//----------------------------------------------------------------------------------------------------------
/**
 * The ID of a running ECS task. The ID **MUST** be extracted from `task.arn`.
 *
 * @example 10838bed-421f-43ef-870a-f43feacbbb5b
 *
 * @example 23ebb8ac-c18f-46c6-8bbe-d55d0e37cfbd
 *
 * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.
 */ __turbopack_context__.s({
    "ANDROID_STATE_VALUE_BACKGROUND": ()=>ANDROID_STATE_VALUE_BACKGROUND,
    "ANDROID_STATE_VALUE_CREATED": ()=>ANDROID_STATE_VALUE_CREATED,
    "ANDROID_STATE_VALUE_FOREGROUND": ()=>ANDROID_STATE_VALUE_FOREGROUND,
    "ATTR_ANDROID_OS_API_LEVEL": ()=>ATTR_ANDROID_OS_API_LEVEL,
    "ATTR_ANDROID_STATE": ()=>ATTR_ANDROID_STATE,
    "ATTR_ARTIFACT_ATTESTATION_FILENAME": ()=>ATTR_ARTIFACT_ATTESTATION_FILENAME,
    "ATTR_ARTIFACT_ATTESTATION_HASH": ()=>ATTR_ARTIFACT_ATTESTATION_HASH,
    "ATTR_ARTIFACT_ATTESTATION_ID": ()=>ATTR_ARTIFACT_ATTESTATION_ID,
    "ATTR_ARTIFACT_FILENAME": ()=>ATTR_ARTIFACT_FILENAME,
    "ATTR_ARTIFACT_HASH": ()=>ATTR_ARTIFACT_HASH,
    "ATTR_ARTIFACT_PURL": ()=>ATTR_ARTIFACT_PURL,
    "ATTR_ARTIFACT_VERSION": ()=>ATTR_ARTIFACT_VERSION,
    "ATTR_AWS_DYNAMODB_ATTRIBUTES_TO_GET": ()=>ATTR_AWS_DYNAMODB_ATTRIBUTES_TO_GET,
    "ATTR_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS": ()=>ATTR_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS,
    "ATTR_AWS_DYNAMODB_CONSISTENT_READ": ()=>ATTR_AWS_DYNAMODB_CONSISTENT_READ,
    "ATTR_AWS_DYNAMODB_CONSUMED_CAPACITY": ()=>ATTR_AWS_DYNAMODB_CONSUMED_CAPACITY,
    "ATTR_AWS_DYNAMODB_COUNT": ()=>ATTR_AWS_DYNAMODB_COUNT,
    "ATTR_AWS_DYNAMODB_EXCLUSIVE_START_TABLE": ()=>ATTR_AWS_DYNAMODB_EXCLUSIVE_START_TABLE,
    "ATTR_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES": ()=>ATTR_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES,
    "ATTR_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES": ()=>ATTR_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES,
    "ATTR_AWS_DYNAMODB_INDEX_NAME": ()=>ATTR_AWS_DYNAMODB_INDEX_NAME,
    "ATTR_AWS_DYNAMODB_ITEM_COLLECTION_METRICS": ()=>ATTR_AWS_DYNAMODB_ITEM_COLLECTION_METRICS,
    "ATTR_AWS_DYNAMODB_LIMIT": ()=>ATTR_AWS_DYNAMODB_LIMIT,
    "ATTR_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES": ()=>ATTR_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES,
    "ATTR_AWS_DYNAMODB_PROJECTION": ()=>ATTR_AWS_DYNAMODB_PROJECTION,
    "ATTR_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY": ()=>ATTR_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY,
    "ATTR_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY": ()=>ATTR_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY,
    "ATTR_AWS_DYNAMODB_SCANNED_COUNT": ()=>ATTR_AWS_DYNAMODB_SCANNED_COUNT,
    "ATTR_AWS_DYNAMODB_SCAN_FORWARD": ()=>ATTR_AWS_DYNAMODB_SCAN_FORWARD,
    "ATTR_AWS_DYNAMODB_SEGMENT": ()=>ATTR_AWS_DYNAMODB_SEGMENT,
    "ATTR_AWS_DYNAMODB_SELECT": ()=>ATTR_AWS_DYNAMODB_SELECT,
    "ATTR_AWS_DYNAMODB_TABLE_COUNT": ()=>ATTR_AWS_DYNAMODB_TABLE_COUNT,
    "ATTR_AWS_DYNAMODB_TABLE_NAMES": ()=>ATTR_AWS_DYNAMODB_TABLE_NAMES,
    "ATTR_AWS_DYNAMODB_TOTAL_SEGMENTS": ()=>ATTR_AWS_DYNAMODB_TOTAL_SEGMENTS,
    "ATTR_AWS_ECS_CLUSTER_ARN": ()=>ATTR_AWS_ECS_CLUSTER_ARN,
    "ATTR_AWS_ECS_CONTAINER_ARN": ()=>ATTR_AWS_ECS_CONTAINER_ARN,
    "ATTR_AWS_ECS_LAUNCHTYPE": ()=>ATTR_AWS_ECS_LAUNCHTYPE,
    "ATTR_AWS_ECS_TASK_ARN": ()=>ATTR_AWS_ECS_TASK_ARN,
    "ATTR_AWS_ECS_TASK_FAMILY": ()=>ATTR_AWS_ECS_TASK_FAMILY,
    "ATTR_AWS_ECS_TASK_ID": ()=>ATTR_AWS_ECS_TASK_ID,
    "ATTR_AWS_ECS_TASK_REVISION": ()=>ATTR_AWS_ECS_TASK_REVISION,
    "ATTR_AWS_EKS_CLUSTER_ARN": ()=>ATTR_AWS_EKS_CLUSTER_ARN,
    "ATTR_AWS_LAMBDA_INVOKED_ARN": ()=>ATTR_AWS_LAMBDA_INVOKED_ARN,
    "ATTR_AWS_LOG_GROUP_ARNS": ()=>ATTR_AWS_LOG_GROUP_ARNS,
    "ATTR_AWS_LOG_GROUP_NAMES": ()=>ATTR_AWS_LOG_GROUP_NAMES,
    "ATTR_AWS_LOG_STREAM_ARNS": ()=>ATTR_AWS_LOG_STREAM_ARNS,
    "ATTR_AWS_LOG_STREAM_NAMES": ()=>ATTR_AWS_LOG_STREAM_NAMES,
    "ATTR_AWS_REQUEST_ID": ()=>ATTR_AWS_REQUEST_ID,
    "ATTR_AWS_S3_BUCKET": ()=>ATTR_AWS_S3_BUCKET,
    "ATTR_AWS_S3_COPY_SOURCE": ()=>ATTR_AWS_S3_COPY_SOURCE,
    "ATTR_AWS_S3_DELETE": ()=>ATTR_AWS_S3_DELETE,
    "ATTR_AWS_S3_KEY": ()=>ATTR_AWS_S3_KEY,
    "ATTR_AWS_S3_PART_NUMBER": ()=>ATTR_AWS_S3_PART_NUMBER,
    "ATTR_AWS_S3_UPLOAD_ID": ()=>ATTR_AWS_S3_UPLOAD_ID,
    "ATTR_AZ_SERVICE_REQUEST_ID": ()=>ATTR_AZ_SERVICE_REQUEST_ID,
    "ATTR_BROWSER_BRANDS": ()=>ATTR_BROWSER_BRANDS,
    "ATTR_BROWSER_LANGUAGE": ()=>ATTR_BROWSER_LANGUAGE,
    "ATTR_BROWSER_MOBILE": ()=>ATTR_BROWSER_MOBILE,
    "ATTR_BROWSER_PLATFORM": ()=>ATTR_BROWSER_PLATFORM,
    "ATTR_CICD_PIPELINE_NAME": ()=>ATTR_CICD_PIPELINE_NAME,
    "ATTR_CICD_PIPELINE_RUN_ID": ()=>ATTR_CICD_PIPELINE_RUN_ID,
    "ATTR_CICD_PIPELINE_TASK_NAME": ()=>ATTR_CICD_PIPELINE_TASK_NAME,
    "ATTR_CICD_PIPELINE_TASK_RUN_ID": ()=>ATTR_CICD_PIPELINE_TASK_RUN_ID,
    "ATTR_CICD_PIPELINE_TASK_RUN_URL_FULL": ()=>ATTR_CICD_PIPELINE_TASK_RUN_URL_FULL,
    "ATTR_CICD_PIPELINE_TASK_TYPE": ()=>ATTR_CICD_PIPELINE_TASK_TYPE,
    "ATTR_CLOUDEVENTS_EVENT_ID": ()=>ATTR_CLOUDEVENTS_EVENT_ID,
    "ATTR_CLOUDEVENTS_EVENT_SOURCE": ()=>ATTR_CLOUDEVENTS_EVENT_SOURCE,
    "ATTR_CLOUDEVENTS_EVENT_SPEC_VERSION": ()=>ATTR_CLOUDEVENTS_EVENT_SPEC_VERSION,
    "ATTR_CLOUDEVENTS_EVENT_SUBJECT": ()=>ATTR_CLOUDEVENTS_EVENT_SUBJECT,
    "ATTR_CLOUDEVENTS_EVENT_TYPE": ()=>ATTR_CLOUDEVENTS_EVENT_TYPE,
    "ATTR_CLOUD_ACCOUNT_ID": ()=>ATTR_CLOUD_ACCOUNT_ID,
    "ATTR_CLOUD_AVAILABILITY_ZONE": ()=>ATTR_CLOUD_AVAILABILITY_ZONE,
    "ATTR_CLOUD_PLATFORM": ()=>ATTR_CLOUD_PLATFORM,
    "ATTR_CLOUD_PROVIDER": ()=>ATTR_CLOUD_PROVIDER,
    "ATTR_CLOUD_REGION": ()=>ATTR_CLOUD_REGION,
    "ATTR_CLOUD_RESOURCE_ID": ()=>ATTR_CLOUD_RESOURCE_ID,
    "ATTR_CODE_COLUMN": ()=>ATTR_CODE_COLUMN,
    "ATTR_CODE_FILEPATH": ()=>ATTR_CODE_FILEPATH,
    "ATTR_CODE_FUNCTION": ()=>ATTR_CODE_FUNCTION,
    "ATTR_CODE_LINENO": ()=>ATTR_CODE_LINENO,
    "ATTR_CODE_NAMESPACE": ()=>ATTR_CODE_NAMESPACE,
    "ATTR_CODE_STACKTRACE": ()=>ATTR_CODE_STACKTRACE,
    "ATTR_CONTAINER_COMMAND": ()=>ATTR_CONTAINER_COMMAND,
    "ATTR_CONTAINER_COMMAND_ARGS": ()=>ATTR_CONTAINER_COMMAND_ARGS,
    "ATTR_CONTAINER_COMMAND_LINE": ()=>ATTR_CONTAINER_COMMAND_LINE,
    "ATTR_CONTAINER_CPU_STATE": ()=>ATTR_CONTAINER_CPU_STATE,
    "ATTR_CONTAINER_ID": ()=>ATTR_CONTAINER_ID,
    "ATTR_CONTAINER_IMAGE_ID": ()=>ATTR_CONTAINER_IMAGE_ID,
    "ATTR_CONTAINER_IMAGE_NAME": ()=>ATTR_CONTAINER_IMAGE_NAME,
    "ATTR_CONTAINER_IMAGE_REPO_DIGESTS": ()=>ATTR_CONTAINER_IMAGE_REPO_DIGESTS,
    "ATTR_CONTAINER_IMAGE_TAGS": ()=>ATTR_CONTAINER_IMAGE_TAGS,
    "ATTR_CONTAINER_LABEL": ()=>ATTR_CONTAINER_LABEL,
    "ATTR_CONTAINER_LABELS": ()=>ATTR_CONTAINER_LABELS,
    "ATTR_CONTAINER_NAME": ()=>ATTR_CONTAINER_NAME,
    "ATTR_CONTAINER_RUNTIME": ()=>ATTR_CONTAINER_RUNTIME,
    "ATTR_CPU_MODE": ()=>ATTR_CPU_MODE,
    "ATTR_DB_CASSANDRA_CONSISTENCY_LEVEL": ()=>ATTR_DB_CASSANDRA_CONSISTENCY_LEVEL,
    "ATTR_DB_CASSANDRA_COORDINATOR_DC": ()=>ATTR_DB_CASSANDRA_COORDINATOR_DC,
    "ATTR_DB_CASSANDRA_COORDINATOR_ID": ()=>ATTR_DB_CASSANDRA_COORDINATOR_ID,
    "ATTR_DB_CASSANDRA_IDEMPOTENCE": ()=>ATTR_DB_CASSANDRA_IDEMPOTENCE,
    "ATTR_DB_CASSANDRA_PAGE_SIZE": ()=>ATTR_DB_CASSANDRA_PAGE_SIZE,
    "ATTR_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT": ()=>ATTR_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT,
    "ATTR_DB_CASSANDRA_TABLE": ()=>ATTR_DB_CASSANDRA_TABLE,
    "ATTR_DB_CLIENT_CONNECTIONS_POOL_NAME": ()=>ATTR_DB_CLIENT_CONNECTIONS_POOL_NAME,
    "ATTR_DB_CLIENT_CONNECTIONS_STATE": ()=>ATTR_DB_CLIENT_CONNECTIONS_STATE,
    "ATTR_DB_CLIENT_CONNECTION_POOL_NAME": ()=>ATTR_DB_CLIENT_CONNECTION_POOL_NAME,
    "ATTR_DB_CLIENT_CONNECTION_STATE": ()=>ATTR_DB_CLIENT_CONNECTION_STATE,
    "ATTR_DB_COLLECTION_NAME": ()=>ATTR_DB_COLLECTION_NAME,
    "ATTR_DB_CONNECTION_STRING": ()=>ATTR_DB_CONNECTION_STRING,
    "ATTR_DB_COSMOSDB_CLIENT_ID": ()=>ATTR_DB_COSMOSDB_CLIENT_ID,
    "ATTR_DB_COSMOSDB_CONNECTION_MODE": ()=>ATTR_DB_COSMOSDB_CONNECTION_MODE,
    "ATTR_DB_COSMOSDB_CONTAINER": ()=>ATTR_DB_COSMOSDB_CONTAINER,
    "ATTR_DB_COSMOSDB_OPERATION_TYPE": ()=>ATTR_DB_COSMOSDB_OPERATION_TYPE,
    "ATTR_DB_COSMOSDB_REQUEST_CHARGE": ()=>ATTR_DB_COSMOSDB_REQUEST_CHARGE,
    "ATTR_DB_COSMOSDB_REQUEST_CONTENT_LENGTH": ()=>ATTR_DB_COSMOSDB_REQUEST_CONTENT_LENGTH,
    "ATTR_DB_COSMOSDB_STATUS_CODE": ()=>ATTR_DB_COSMOSDB_STATUS_CODE,
    "ATTR_DB_COSMOSDB_SUB_STATUS_CODE": ()=>ATTR_DB_COSMOSDB_SUB_STATUS_CODE,
    "ATTR_DB_ELASTICSEARCH_CLUSTER_NAME": ()=>ATTR_DB_ELASTICSEARCH_CLUSTER_NAME,
    "ATTR_DB_ELASTICSEARCH_NODE_NAME": ()=>ATTR_DB_ELASTICSEARCH_NODE_NAME,
    "ATTR_DB_ELASTICSEARCH_PATH_PARTS": ()=>ATTR_DB_ELASTICSEARCH_PATH_PARTS,
    "ATTR_DB_INSTANCE_ID": ()=>ATTR_DB_INSTANCE_ID,
    "ATTR_DB_JDBC_DRIVER_CLASSNAME": ()=>ATTR_DB_JDBC_DRIVER_CLASSNAME,
    "ATTR_DB_MONGODB_COLLECTION": ()=>ATTR_DB_MONGODB_COLLECTION,
    "ATTR_DB_MSSQL_INSTANCE_NAME": ()=>ATTR_DB_MSSQL_INSTANCE_NAME,
    "ATTR_DB_NAME": ()=>ATTR_DB_NAME,
    "ATTR_DB_NAMESPACE": ()=>ATTR_DB_NAMESPACE,
    "ATTR_DB_OPERATION": ()=>ATTR_DB_OPERATION,
    "ATTR_DB_OPERATION_BATCH_SIZE": ()=>ATTR_DB_OPERATION_BATCH_SIZE,
    "ATTR_DB_OPERATION_NAME": ()=>ATTR_DB_OPERATION_NAME,
    "ATTR_DB_QUERY_PARAMETER": ()=>ATTR_DB_QUERY_PARAMETER,
    "ATTR_DB_QUERY_TEXT": ()=>ATTR_DB_QUERY_TEXT,
    "ATTR_DB_REDIS_DATABASE_INDEX": ()=>ATTR_DB_REDIS_DATABASE_INDEX,
    "ATTR_DB_SQL_TABLE": ()=>ATTR_DB_SQL_TABLE,
    "ATTR_DB_STATEMENT": ()=>ATTR_DB_STATEMENT,
    "ATTR_DB_SYSTEM": ()=>ATTR_DB_SYSTEM,
    "ATTR_DB_USER": ()=>ATTR_DB_USER,
    "ATTR_DEPLOYMENT_ENVIRONMENT": ()=>ATTR_DEPLOYMENT_ENVIRONMENT,
    "ATTR_DEPLOYMENT_ENVIRONMENT_NAME": ()=>ATTR_DEPLOYMENT_ENVIRONMENT_NAME,
    "ATTR_DEPLOYMENT_ID": ()=>ATTR_DEPLOYMENT_ID,
    "ATTR_DEPLOYMENT_NAME": ()=>ATTR_DEPLOYMENT_NAME,
    "ATTR_DEPLOYMENT_STATUS": ()=>ATTR_DEPLOYMENT_STATUS,
    "ATTR_DESTINATION_ADDRESS": ()=>ATTR_DESTINATION_ADDRESS,
    "ATTR_DESTINATION_PORT": ()=>ATTR_DESTINATION_PORT,
    "ATTR_DEVICE_ID": ()=>ATTR_DEVICE_ID,
    "ATTR_DEVICE_MANUFACTURER": ()=>ATTR_DEVICE_MANUFACTURER,
    "ATTR_DEVICE_MODEL_IDENTIFIER": ()=>ATTR_DEVICE_MODEL_IDENTIFIER,
    "ATTR_DEVICE_MODEL_NAME": ()=>ATTR_DEVICE_MODEL_NAME,
    "ATTR_DISK_IO_DIRECTION": ()=>ATTR_DISK_IO_DIRECTION,
    "ATTR_DNS_QUESTION_NAME": ()=>ATTR_DNS_QUESTION_NAME,
    "ATTR_ENDUSER_ID": ()=>ATTR_ENDUSER_ID,
    "ATTR_ENDUSER_ROLE": ()=>ATTR_ENDUSER_ROLE,
    "ATTR_ENDUSER_SCOPE": ()=>ATTR_ENDUSER_SCOPE,
    "ATTR_EVENT_NAME": ()=>ATTR_EVENT_NAME,
    "ATTR_FAAS_COLDSTART": ()=>ATTR_FAAS_COLDSTART,
    "ATTR_FAAS_CRON": ()=>ATTR_FAAS_CRON,
    "ATTR_FAAS_DOCUMENT_COLLECTION": ()=>ATTR_FAAS_DOCUMENT_COLLECTION,
    "ATTR_FAAS_DOCUMENT_NAME": ()=>ATTR_FAAS_DOCUMENT_NAME,
    "ATTR_FAAS_DOCUMENT_OPERATION": ()=>ATTR_FAAS_DOCUMENT_OPERATION,
    "ATTR_FAAS_DOCUMENT_TIME": ()=>ATTR_FAAS_DOCUMENT_TIME,
    "ATTR_FAAS_INSTANCE": ()=>ATTR_FAAS_INSTANCE,
    "ATTR_FAAS_INVOCATION_ID": ()=>ATTR_FAAS_INVOCATION_ID,
    "ATTR_FAAS_INVOKED_NAME": ()=>ATTR_FAAS_INVOKED_NAME,
    "ATTR_FAAS_INVOKED_PROVIDER": ()=>ATTR_FAAS_INVOKED_PROVIDER,
    "ATTR_FAAS_INVOKED_REGION": ()=>ATTR_FAAS_INVOKED_REGION,
    "ATTR_FAAS_MAX_MEMORY": ()=>ATTR_FAAS_MAX_MEMORY,
    "ATTR_FAAS_NAME": ()=>ATTR_FAAS_NAME,
    "ATTR_FAAS_TIME": ()=>ATTR_FAAS_TIME,
    "ATTR_FAAS_TRIGGER": ()=>ATTR_FAAS_TRIGGER,
    "ATTR_FAAS_VERSION": ()=>ATTR_FAAS_VERSION,
    "ATTR_FEATURE_FLAG_KEY": ()=>ATTR_FEATURE_FLAG_KEY,
    "ATTR_FEATURE_FLAG_PROVIDER_NAME": ()=>ATTR_FEATURE_FLAG_PROVIDER_NAME,
    "ATTR_FEATURE_FLAG_VARIANT": ()=>ATTR_FEATURE_FLAG_VARIANT,
    "ATTR_FILE_DIRECTORY": ()=>ATTR_FILE_DIRECTORY,
    "ATTR_FILE_EXTENSION": ()=>ATTR_FILE_EXTENSION,
    "ATTR_FILE_NAME": ()=>ATTR_FILE_NAME,
    "ATTR_FILE_PATH": ()=>ATTR_FILE_PATH,
    "ATTR_FILE_SIZE": ()=>ATTR_FILE_SIZE,
    "ATTR_GCP_CLIENT_SERVICE": ()=>ATTR_GCP_CLIENT_SERVICE,
    "ATTR_GCP_CLOUD_RUN_JOB_EXECUTION": ()=>ATTR_GCP_CLOUD_RUN_JOB_EXECUTION,
    "ATTR_GCP_CLOUD_RUN_JOB_TASK_INDEX": ()=>ATTR_GCP_CLOUD_RUN_JOB_TASK_INDEX,
    "ATTR_GCP_GCE_INSTANCE_HOSTNAME": ()=>ATTR_GCP_GCE_INSTANCE_HOSTNAME,
    "ATTR_GCP_GCE_INSTANCE_NAME": ()=>ATTR_GCP_GCE_INSTANCE_NAME,
    "ATTR_GEN_AI_COMPLETION": ()=>ATTR_GEN_AI_COMPLETION,
    "ATTR_GEN_AI_OPERATION_NAME": ()=>ATTR_GEN_AI_OPERATION_NAME,
    "ATTR_GEN_AI_PROMPT": ()=>ATTR_GEN_AI_PROMPT,
    "ATTR_GEN_AI_REQUEST_FREQUENCY_PENALTY": ()=>ATTR_GEN_AI_REQUEST_FREQUENCY_PENALTY,
    "ATTR_GEN_AI_REQUEST_MAX_TOKENS": ()=>ATTR_GEN_AI_REQUEST_MAX_TOKENS,
    "ATTR_GEN_AI_REQUEST_MODEL": ()=>ATTR_GEN_AI_REQUEST_MODEL,
    "ATTR_GEN_AI_REQUEST_PRESENCE_PENALTY": ()=>ATTR_GEN_AI_REQUEST_PRESENCE_PENALTY,
    "ATTR_GEN_AI_REQUEST_STOP_SEQUENCES": ()=>ATTR_GEN_AI_REQUEST_STOP_SEQUENCES,
    "ATTR_GEN_AI_REQUEST_TEMPERATURE": ()=>ATTR_GEN_AI_REQUEST_TEMPERATURE,
    "ATTR_GEN_AI_REQUEST_TOP_K": ()=>ATTR_GEN_AI_REQUEST_TOP_K,
    "ATTR_GEN_AI_REQUEST_TOP_P": ()=>ATTR_GEN_AI_REQUEST_TOP_P,
    "ATTR_GEN_AI_RESPONSE_FINISH_REASONS": ()=>ATTR_GEN_AI_RESPONSE_FINISH_REASONS,
    "ATTR_GEN_AI_RESPONSE_ID": ()=>ATTR_GEN_AI_RESPONSE_ID,
    "ATTR_GEN_AI_RESPONSE_MODEL": ()=>ATTR_GEN_AI_RESPONSE_MODEL,
    "ATTR_GEN_AI_SYSTEM": ()=>ATTR_GEN_AI_SYSTEM,
    "ATTR_GEN_AI_TOKEN_TYPE": ()=>ATTR_GEN_AI_TOKEN_TYPE,
    "ATTR_GEN_AI_USAGE_COMPLETION_TOKENS": ()=>ATTR_GEN_AI_USAGE_COMPLETION_TOKENS,
    "ATTR_GEN_AI_USAGE_INPUT_TOKENS": ()=>ATTR_GEN_AI_USAGE_INPUT_TOKENS,
    "ATTR_GEN_AI_USAGE_OUTPUT_TOKENS": ()=>ATTR_GEN_AI_USAGE_OUTPUT_TOKENS,
    "ATTR_GEN_AI_USAGE_PROMPT_TOKENS": ()=>ATTR_GEN_AI_USAGE_PROMPT_TOKENS,
    "ATTR_GO_MEMORY_TYPE": ()=>ATTR_GO_MEMORY_TYPE,
    "ATTR_GRAPHQL_DOCUMENT": ()=>ATTR_GRAPHQL_DOCUMENT,
    "ATTR_GRAPHQL_OPERATION_NAME": ()=>ATTR_GRAPHQL_OPERATION_NAME,
    "ATTR_GRAPHQL_OPERATION_TYPE": ()=>ATTR_GRAPHQL_OPERATION_TYPE,
    "ATTR_HEROKU_APP_ID": ()=>ATTR_HEROKU_APP_ID,
    "ATTR_HEROKU_RELEASE_COMMIT": ()=>ATTR_HEROKU_RELEASE_COMMIT,
    "ATTR_HEROKU_RELEASE_CREATION_TIMESTAMP": ()=>ATTR_HEROKU_RELEASE_CREATION_TIMESTAMP,
    "ATTR_HOST_ARCH": ()=>ATTR_HOST_ARCH,
    "ATTR_HOST_CPU_CACHE_L2_SIZE": ()=>ATTR_HOST_CPU_CACHE_L2_SIZE,
    "ATTR_HOST_CPU_FAMILY": ()=>ATTR_HOST_CPU_FAMILY,
    "ATTR_HOST_CPU_MODEL_ID": ()=>ATTR_HOST_CPU_MODEL_ID,
    "ATTR_HOST_CPU_MODEL_NAME": ()=>ATTR_HOST_CPU_MODEL_NAME,
    "ATTR_HOST_CPU_STEPPING": ()=>ATTR_HOST_CPU_STEPPING,
    "ATTR_HOST_CPU_VENDOR_ID": ()=>ATTR_HOST_CPU_VENDOR_ID,
    "ATTR_HOST_ID": ()=>ATTR_HOST_ID,
    "ATTR_HOST_IMAGE_ID": ()=>ATTR_HOST_IMAGE_ID,
    "ATTR_HOST_IMAGE_NAME": ()=>ATTR_HOST_IMAGE_NAME,
    "ATTR_HOST_IMAGE_VERSION": ()=>ATTR_HOST_IMAGE_VERSION,
    "ATTR_HOST_IP": ()=>ATTR_HOST_IP,
    "ATTR_HOST_MAC": ()=>ATTR_HOST_MAC,
    "ATTR_HOST_NAME": ()=>ATTR_HOST_NAME,
    "ATTR_HOST_TYPE": ()=>ATTR_HOST_TYPE,
    "ATTR_HTTP_CLIENT_IP": ()=>ATTR_HTTP_CLIENT_IP,
    "ATTR_HTTP_CONNECTION_STATE": ()=>ATTR_HTTP_CONNECTION_STATE,
    "ATTR_HTTP_FLAVOR": ()=>ATTR_HTTP_FLAVOR,
    "ATTR_HTTP_HOST": ()=>ATTR_HTTP_HOST,
    "ATTR_HTTP_METHOD": ()=>ATTR_HTTP_METHOD,
    "ATTR_HTTP_REQUEST_BODY_SIZE": ()=>ATTR_HTTP_REQUEST_BODY_SIZE,
    "ATTR_HTTP_REQUEST_CONTENT_LENGTH": ()=>ATTR_HTTP_REQUEST_CONTENT_LENGTH,
    "ATTR_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED": ()=>ATTR_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED,
    "ATTR_HTTP_REQUEST_SIZE": ()=>ATTR_HTTP_REQUEST_SIZE,
    "ATTR_HTTP_RESPONSE_BODY_SIZE": ()=>ATTR_HTTP_RESPONSE_BODY_SIZE,
    "ATTR_HTTP_RESPONSE_CONTENT_LENGTH": ()=>ATTR_HTTP_RESPONSE_CONTENT_LENGTH,
    "ATTR_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED": ()=>ATTR_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED,
    "ATTR_HTTP_RESPONSE_SIZE": ()=>ATTR_HTTP_RESPONSE_SIZE,
    "ATTR_HTTP_SCHEME": ()=>ATTR_HTTP_SCHEME,
    "ATTR_HTTP_SERVER_NAME": ()=>ATTR_HTTP_SERVER_NAME,
    "ATTR_HTTP_STATUS_CODE": ()=>ATTR_HTTP_STATUS_CODE,
    "ATTR_HTTP_TARGET": ()=>ATTR_HTTP_TARGET,
    "ATTR_HTTP_URL": ()=>ATTR_HTTP_URL,
    "ATTR_HTTP_USER_AGENT": ()=>ATTR_HTTP_USER_AGENT,
    "ATTR_IOS_STATE": ()=>ATTR_IOS_STATE,
    "ATTR_JVM_BUFFER_POOL_NAME": ()=>ATTR_JVM_BUFFER_POOL_NAME,
    "ATTR_K8S_CLUSTER_NAME": ()=>ATTR_K8S_CLUSTER_NAME,
    "ATTR_K8S_CLUSTER_UID": ()=>ATTR_K8S_CLUSTER_UID,
    "ATTR_K8S_CONTAINER_NAME": ()=>ATTR_K8S_CONTAINER_NAME,
    "ATTR_K8S_CONTAINER_RESTART_COUNT": ()=>ATTR_K8S_CONTAINER_RESTART_COUNT,
    "ATTR_K8S_CONTAINER_STATUS_LAST_TERMINATED_REASON": ()=>ATTR_K8S_CONTAINER_STATUS_LAST_TERMINATED_REASON,
    "ATTR_K8S_CRONJOB_NAME": ()=>ATTR_K8S_CRONJOB_NAME,
    "ATTR_K8S_CRONJOB_UID": ()=>ATTR_K8S_CRONJOB_UID,
    "ATTR_K8S_DAEMONSET_NAME": ()=>ATTR_K8S_DAEMONSET_NAME,
    "ATTR_K8S_DAEMONSET_UID": ()=>ATTR_K8S_DAEMONSET_UID,
    "ATTR_K8S_DEPLOYMENT_NAME": ()=>ATTR_K8S_DEPLOYMENT_NAME,
    "ATTR_K8S_DEPLOYMENT_UID": ()=>ATTR_K8S_DEPLOYMENT_UID,
    "ATTR_K8S_JOB_NAME": ()=>ATTR_K8S_JOB_NAME,
    "ATTR_K8S_JOB_UID": ()=>ATTR_K8S_JOB_UID,
    "ATTR_K8S_NAMESPACE_NAME": ()=>ATTR_K8S_NAMESPACE_NAME,
    "ATTR_K8S_NODE_NAME": ()=>ATTR_K8S_NODE_NAME,
    "ATTR_K8S_NODE_UID": ()=>ATTR_K8S_NODE_UID,
    "ATTR_K8S_POD_ANNOTATION": ()=>ATTR_K8S_POD_ANNOTATION,
    "ATTR_K8S_POD_LABEL": ()=>ATTR_K8S_POD_LABEL,
    "ATTR_K8S_POD_LABELS": ()=>ATTR_K8S_POD_LABELS,
    "ATTR_K8S_POD_NAME": ()=>ATTR_K8S_POD_NAME,
    "ATTR_K8S_POD_UID": ()=>ATTR_K8S_POD_UID,
    "ATTR_K8S_REPLICASET_NAME": ()=>ATTR_K8S_REPLICASET_NAME,
    "ATTR_K8S_REPLICASET_UID": ()=>ATTR_K8S_REPLICASET_UID,
    "ATTR_K8S_STATEFULSET_NAME": ()=>ATTR_K8S_STATEFULSET_NAME,
    "ATTR_K8S_STATEFULSET_UID": ()=>ATTR_K8S_STATEFULSET_UID,
    "ATTR_LINUX_MEMORY_SLAB_STATE": ()=>ATTR_LINUX_MEMORY_SLAB_STATE,
    "ATTR_LOG_FILE_NAME": ()=>ATTR_LOG_FILE_NAME,
    "ATTR_LOG_FILE_NAME_RESOLVED": ()=>ATTR_LOG_FILE_NAME_RESOLVED,
    "ATTR_LOG_FILE_PATH": ()=>ATTR_LOG_FILE_PATH,
    "ATTR_LOG_FILE_PATH_RESOLVED": ()=>ATTR_LOG_FILE_PATH_RESOLVED,
    "ATTR_LOG_IOSTREAM": ()=>ATTR_LOG_IOSTREAM,
    "ATTR_LOG_RECORD_ORIGINAL": ()=>ATTR_LOG_RECORD_ORIGINAL,
    "ATTR_LOG_RECORD_UID": ()=>ATTR_LOG_RECORD_UID,
    "ATTR_MESSAGE_COMPRESSED_SIZE": ()=>ATTR_MESSAGE_COMPRESSED_SIZE,
    "ATTR_MESSAGE_ID": ()=>ATTR_MESSAGE_ID,
    "ATTR_MESSAGE_TYPE": ()=>ATTR_MESSAGE_TYPE,
    "ATTR_MESSAGE_UNCOMPRESSED_SIZE": ()=>ATTR_MESSAGE_UNCOMPRESSED_SIZE,
    "ATTR_MESSAGING_BATCH_MESSAGE_COUNT": ()=>ATTR_MESSAGING_BATCH_MESSAGE_COUNT,
    "ATTR_MESSAGING_CLIENT_ID": ()=>ATTR_MESSAGING_CLIENT_ID,
    "ATTR_MESSAGING_CONSUMER_GROUP_NAME": ()=>ATTR_MESSAGING_CONSUMER_GROUP_NAME,
    "ATTR_MESSAGING_DESTINATION_ANONYMOUS": ()=>ATTR_MESSAGING_DESTINATION_ANONYMOUS,
    "ATTR_MESSAGING_DESTINATION_NAME": ()=>ATTR_MESSAGING_DESTINATION_NAME,
    "ATTR_MESSAGING_DESTINATION_PARTITION_ID": ()=>ATTR_MESSAGING_DESTINATION_PARTITION_ID,
    "ATTR_MESSAGING_DESTINATION_PUBLISH_ANONYMOUS": ()=>ATTR_MESSAGING_DESTINATION_PUBLISH_ANONYMOUS,
    "ATTR_MESSAGING_DESTINATION_PUBLISH_NAME": ()=>ATTR_MESSAGING_DESTINATION_PUBLISH_NAME,
    "ATTR_MESSAGING_DESTINATION_SUBSCRIPTION_NAME": ()=>ATTR_MESSAGING_DESTINATION_SUBSCRIPTION_NAME,
    "ATTR_MESSAGING_DESTINATION_TEMPLATE": ()=>ATTR_MESSAGING_DESTINATION_TEMPLATE,
    "ATTR_MESSAGING_DESTINATION_TEMPORARY": ()=>ATTR_MESSAGING_DESTINATION_TEMPORARY,
    "ATTR_MESSAGING_EVENTHUBS_CONSUMER_GROUP": ()=>ATTR_MESSAGING_EVENTHUBS_CONSUMER_GROUP,
    "ATTR_MESSAGING_EVENTHUBS_MESSAGE_ENQUEUED_TIME": ()=>ATTR_MESSAGING_EVENTHUBS_MESSAGE_ENQUEUED_TIME,
    "ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_ACK_DEADLINE": ()=>ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_ACK_DEADLINE,
    "ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_ACK_ID": ()=>ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_ACK_ID,
    "ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_DELIVERY_ATTEMPT": ()=>ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_DELIVERY_ATTEMPT,
    "ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_ORDERING_KEY": ()=>ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_ORDERING_KEY,
    "ATTR_MESSAGING_KAFKA_CONSUMER_GROUP": ()=>ATTR_MESSAGING_KAFKA_CONSUMER_GROUP,
    "ATTR_MESSAGING_KAFKA_DESTINATION_PARTITION": ()=>ATTR_MESSAGING_KAFKA_DESTINATION_PARTITION,
    "ATTR_MESSAGING_KAFKA_MESSAGE_KEY": ()=>ATTR_MESSAGING_KAFKA_MESSAGE_KEY,
    "ATTR_MESSAGING_KAFKA_MESSAGE_OFFSET": ()=>ATTR_MESSAGING_KAFKA_MESSAGE_OFFSET,
    "ATTR_MESSAGING_KAFKA_MESSAGE_TOMBSTONE": ()=>ATTR_MESSAGING_KAFKA_MESSAGE_TOMBSTONE,
    "ATTR_MESSAGING_KAFKA_OFFSET": ()=>ATTR_MESSAGING_KAFKA_OFFSET,
    "ATTR_MESSAGING_MESSAGE_BODY_SIZE": ()=>ATTR_MESSAGING_MESSAGE_BODY_SIZE,
    "ATTR_MESSAGING_MESSAGE_CONVERSATION_ID": ()=>ATTR_MESSAGING_MESSAGE_CONVERSATION_ID,
    "ATTR_MESSAGING_MESSAGE_ENVELOPE_SIZE": ()=>ATTR_MESSAGING_MESSAGE_ENVELOPE_SIZE,
    "ATTR_MESSAGING_MESSAGE_ID": ()=>ATTR_MESSAGING_MESSAGE_ID,
    "ATTR_MESSAGING_OPERATION": ()=>ATTR_MESSAGING_OPERATION,
    "ATTR_MESSAGING_OPERATION_NAME": ()=>ATTR_MESSAGING_OPERATION_NAME,
    "ATTR_MESSAGING_OPERATION_TYPE": ()=>ATTR_MESSAGING_OPERATION_TYPE,
    "ATTR_MESSAGING_RABBITMQ_DESTINATION_ROUTING_KEY": ()=>ATTR_MESSAGING_RABBITMQ_DESTINATION_ROUTING_KEY,
    "ATTR_MESSAGING_RABBITMQ_MESSAGE_DELIVERY_TAG": ()=>ATTR_MESSAGING_RABBITMQ_MESSAGE_DELIVERY_TAG,
    "ATTR_MESSAGING_ROCKETMQ_CLIENT_GROUP": ()=>ATTR_MESSAGING_ROCKETMQ_CLIENT_GROUP,
    "ATTR_MESSAGING_ROCKETMQ_CONSUMPTION_MODEL": ()=>ATTR_MESSAGING_ROCKETMQ_CONSUMPTION_MODEL,
    "ATTR_MESSAGING_ROCKETMQ_MESSAGE_DELAY_TIME_LEVEL": ()=>ATTR_MESSAGING_ROCKETMQ_MESSAGE_DELAY_TIME_LEVEL,
    "ATTR_MESSAGING_ROCKETMQ_MESSAGE_DELIVERY_TIMESTAMP": ()=>ATTR_MESSAGING_ROCKETMQ_MESSAGE_DELIVERY_TIMESTAMP,
    "ATTR_MESSAGING_ROCKETMQ_MESSAGE_GROUP": ()=>ATTR_MESSAGING_ROCKETMQ_MESSAGE_GROUP,
    "ATTR_MESSAGING_ROCKETMQ_MESSAGE_KEYS": ()=>ATTR_MESSAGING_ROCKETMQ_MESSAGE_KEYS,
    "ATTR_MESSAGING_ROCKETMQ_MESSAGE_TAG": ()=>ATTR_MESSAGING_ROCKETMQ_MESSAGE_TAG,
    "ATTR_MESSAGING_ROCKETMQ_MESSAGE_TYPE": ()=>ATTR_MESSAGING_ROCKETMQ_MESSAGE_TYPE,
    "ATTR_MESSAGING_ROCKETMQ_NAMESPACE": ()=>ATTR_MESSAGING_ROCKETMQ_NAMESPACE,
    "ATTR_MESSAGING_SERVICEBUS_DESTINATION_SUBSCRIPTION_NAME": ()=>ATTR_MESSAGING_SERVICEBUS_DESTINATION_SUBSCRIPTION_NAME,
    "ATTR_MESSAGING_SERVICEBUS_DISPOSITION_STATUS": ()=>ATTR_MESSAGING_SERVICEBUS_DISPOSITION_STATUS,
    "ATTR_MESSAGING_SERVICEBUS_MESSAGE_DELIVERY_COUNT": ()=>ATTR_MESSAGING_SERVICEBUS_MESSAGE_DELIVERY_COUNT,
    "ATTR_MESSAGING_SERVICEBUS_MESSAGE_ENQUEUED_TIME": ()=>ATTR_MESSAGING_SERVICEBUS_MESSAGE_ENQUEUED_TIME,
    "ATTR_MESSAGING_SYSTEM": ()=>ATTR_MESSAGING_SYSTEM,
    "ATTR_NETWORK_CARRIER_ICC": ()=>ATTR_NETWORK_CARRIER_ICC,
    "ATTR_NETWORK_CARRIER_MCC": ()=>ATTR_NETWORK_CARRIER_MCC,
    "ATTR_NETWORK_CARRIER_MNC": ()=>ATTR_NETWORK_CARRIER_MNC,
    "ATTR_NETWORK_CARRIER_NAME": ()=>ATTR_NETWORK_CARRIER_NAME,
    "ATTR_NETWORK_CONNECTION_SUBTYPE": ()=>ATTR_NETWORK_CONNECTION_SUBTYPE,
    "ATTR_NETWORK_CONNECTION_TYPE": ()=>ATTR_NETWORK_CONNECTION_TYPE,
    "ATTR_NETWORK_IO_DIRECTION": ()=>ATTR_NETWORK_IO_DIRECTION,
    "ATTR_NET_HOST_IP": ()=>ATTR_NET_HOST_IP,
    "ATTR_NET_HOST_NAME": ()=>ATTR_NET_HOST_NAME,
    "ATTR_NET_HOST_PORT": ()=>ATTR_NET_HOST_PORT,
    "ATTR_NET_PEER_IP": ()=>ATTR_NET_PEER_IP,
    "ATTR_NET_PEER_NAME": ()=>ATTR_NET_PEER_NAME,
    "ATTR_NET_PEER_PORT": ()=>ATTR_NET_PEER_PORT,
    "ATTR_NET_PROTOCOL_NAME": ()=>ATTR_NET_PROTOCOL_NAME,
    "ATTR_NET_PROTOCOL_VERSION": ()=>ATTR_NET_PROTOCOL_VERSION,
    "ATTR_NET_SOCK_FAMILY": ()=>ATTR_NET_SOCK_FAMILY,
    "ATTR_NET_SOCK_HOST_ADDR": ()=>ATTR_NET_SOCK_HOST_ADDR,
    "ATTR_NET_SOCK_HOST_PORT": ()=>ATTR_NET_SOCK_HOST_PORT,
    "ATTR_NET_SOCK_PEER_ADDR": ()=>ATTR_NET_SOCK_PEER_ADDR,
    "ATTR_NET_SOCK_PEER_NAME": ()=>ATTR_NET_SOCK_PEER_NAME,
    "ATTR_NET_SOCK_PEER_PORT": ()=>ATTR_NET_SOCK_PEER_PORT,
    "ATTR_NET_TRANSPORT": ()=>ATTR_NET_TRANSPORT,
    "ATTR_OCI_MANIFEST_DIGEST": ()=>ATTR_OCI_MANIFEST_DIGEST,
    "ATTR_OPENTRACING_REF_TYPE": ()=>ATTR_OPENTRACING_REF_TYPE,
    "ATTR_OS_BUILD_ID": ()=>ATTR_OS_BUILD_ID,
    "ATTR_OS_DESCRIPTION": ()=>ATTR_OS_DESCRIPTION,
    "ATTR_OS_NAME": ()=>ATTR_OS_NAME,
    "ATTR_OS_TYPE": ()=>ATTR_OS_TYPE,
    "ATTR_OS_VERSION": ()=>ATTR_OS_VERSION,
    "ATTR_OTEL_LIBRARY_NAME": ()=>ATTR_OTEL_LIBRARY_NAME,
    "ATTR_OTEL_LIBRARY_VERSION": ()=>ATTR_OTEL_LIBRARY_VERSION,
    "ATTR_PEER_SERVICE": ()=>ATTR_PEER_SERVICE,
    "ATTR_POOL_NAME": ()=>ATTR_POOL_NAME,
    "ATTR_PROCESS_COMMAND": ()=>ATTR_PROCESS_COMMAND,
    "ATTR_PROCESS_COMMAND_ARGS": ()=>ATTR_PROCESS_COMMAND_ARGS,
    "ATTR_PROCESS_COMMAND_LINE": ()=>ATTR_PROCESS_COMMAND_LINE,
    "ATTR_PROCESS_CONTEXT_SWITCH_TYPE": ()=>ATTR_PROCESS_CONTEXT_SWITCH_TYPE,
    "ATTR_PROCESS_CPU_STATE": ()=>ATTR_PROCESS_CPU_STATE,
    "ATTR_PROCESS_CREATION_TIME": ()=>ATTR_PROCESS_CREATION_TIME,
    "ATTR_PROCESS_EXECUTABLE_NAME": ()=>ATTR_PROCESS_EXECUTABLE_NAME,
    "ATTR_PROCESS_EXECUTABLE_PATH": ()=>ATTR_PROCESS_EXECUTABLE_PATH,
    "ATTR_PROCESS_EXIT_CODE": ()=>ATTR_PROCESS_EXIT_CODE,
    "ATTR_PROCESS_EXIT_TIME": ()=>ATTR_PROCESS_EXIT_TIME,
    "ATTR_PROCESS_GROUP_LEADER_PID": ()=>ATTR_PROCESS_GROUP_LEADER_PID,
    "ATTR_PROCESS_INTERACTIVE": ()=>ATTR_PROCESS_INTERACTIVE,
    "ATTR_PROCESS_OWNER": ()=>ATTR_PROCESS_OWNER,
    "ATTR_PROCESS_PAGING_FAULT_TYPE": ()=>ATTR_PROCESS_PAGING_FAULT_TYPE,
    "ATTR_PROCESS_PARENT_PID": ()=>ATTR_PROCESS_PARENT_PID,
    "ATTR_PROCESS_PID": ()=>ATTR_PROCESS_PID,
    "ATTR_PROCESS_REAL_USER_ID": ()=>ATTR_PROCESS_REAL_USER_ID,
    "ATTR_PROCESS_REAL_USER_NAME": ()=>ATTR_PROCESS_REAL_USER_NAME,
    "ATTR_PROCESS_RUNTIME_DESCRIPTION": ()=>ATTR_PROCESS_RUNTIME_DESCRIPTION,
    "ATTR_PROCESS_RUNTIME_NAME": ()=>ATTR_PROCESS_RUNTIME_NAME,
    "ATTR_PROCESS_RUNTIME_VERSION": ()=>ATTR_PROCESS_RUNTIME_VERSION,
    "ATTR_PROCESS_SAVED_USER_ID": ()=>ATTR_PROCESS_SAVED_USER_ID,
    "ATTR_PROCESS_SAVED_USER_NAME": ()=>ATTR_PROCESS_SAVED_USER_NAME,
    "ATTR_PROCESS_SESSION_LEADER_PID": ()=>ATTR_PROCESS_SESSION_LEADER_PID,
    "ATTR_PROCESS_USER_ID": ()=>ATTR_PROCESS_USER_ID,
    "ATTR_PROCESS_USER_NAME": ()=>ATTR_PROCESS_USER_NAME,
    "ATTR_PROCESS_VPID": ()=>ATTR_PROCESS_VPID,
    "ATTR_RPC_CONNECT_RPC_ERROR_CODE": ()=>ATTR_RPC_CONNECT_RPC_ERROR_CODE,
    "ATTR_RPC_CONNECT_RPC_REQUEST_METADATA": ()=>ATTR_RPC_CONNECT_RPC_REQUEST_METADATA,
    "ATTR_RPC_CONNECT_RPC_RESPONSE_METADATA": ()=>ATTR_RPC_CONNECT_RPC_RESPONSE_METADATA,
    "ATTR_RPC_GRPC_REQUEST_METADATA": ()=>ATTR_RPC_GRPC_REQUEST_METADATA,
    "ATTR_RPC_GRPC_RESPONSE_METADATA": ()=>ATTR_RPC_GRPC_RESPONSE_METADATA,
    "ATTR_RPC_GRPC_STATUS_CODE": ()=>ATTR_RPC_GRPC_STATUS_CODE,
    "ATTR_RPC_JSONRPC_ERROR_CODE": ()=>ATTR_RPC_JSONRPC_ERROR_CODE,
    "ATTR_RPC_JSONRPC_ERROR_MESSAGE": ()=>ATTR_RPC_JSONRPC_ERROR_MESSAGE,
    "ATTR_RPC_JSONRPC_REQUEST_ID": ()=>ATTR_RPC_JSONRPC_REQUEST_ID,
    "ATTR_RPC_JSONRPC_VERSION": ()=>ATTR_RPC_JSONRPC_VERSION,
    "ATTR_RPC_MESSAGE_COMPRESSED_SIZE": ()=>ATTR_RPC_MESSAGE_COMPRESSED_SIZE,
    "ATTR_RPC_MESSAGE_ID": ()=>ATTR_RPC_MESSAGE_ID,
    "ATTR_RPC_MESSAGE_TYPE": ()=>ATTR_RPC_MESSAGE_TYPE,
    "ATTR_RPC_MESSAGE_UNCOMPRESSED_SIZE": ()=>ATTR_RPC_MESSAGE_UNCOMPRESSED_SIZE,
    "ATTR_RPC_METHOD": ()=>ATTR_RPC_METHOD,
    "ATTR_RPC_SERVICE": ()=>ATTR_RPC_SERVICE,
    "ATTR_RPC_SYSTEM": ()=>ATTR_RPC_SYSTEM,
    "ATTR_SERVICE_INSTANCE_ID": ()=>ATTR_SERVICE_INSTANCE_ID,
    "ATTR_SERVICE_NAMESPACE": ()=>ATTR_SERVICE_NAMESPACE,
    "ATTR_SESSION_ID": ()=>ATTR_SESSION_ID,
    "ATTR_SESSION_PREVIOUS_ID": ()=>ATTR_SESSION_PREVIOUS_ID,
    "ATTR_SOURCE_ADDRESS": ()=>ATTR_SOURCE_ADDRESS,
    "ATTR_SOURCE_PORT": ()=>ATTR_SOURCE_PORT,
    "ATTR_STATE": ()=>ATTR_STATE,
    "ATTR_SYSTEM_CPU_LOGICAL_NUMBER": ()=>ATTR_SYSTEM_CPU_LOGICAL_NUMBER,
    "ATTR_SYSTEM_CPU_STATE": ()=>ATTR_SYSTEM_CPU_STATE,
    "ATTR_SYSTEM_DEVICE": ()=>ATTR_SYSTEM_DEVICE,
    "ATTR_SYSTEM_FILESYSTEM_MODE": ()=>ATTR_SYSTEM_FILESYSTEM_MODE,
    "ATTR_SYSTEM_FILESYSTEM_MOUNTPOINT": ()=>ATTR_SYSTEM_FILESYSTEM_MOUNTPOINT,
    "ATTR_SYSTEM_FILESYSTEM_STATE": ()=>ATTR_SYSTEM_FILESYSTEM_STATE,
    "ATTR_SYSTEM_FILESYSTEM_TYPE": ()=>ATTR_SYSTEM_FILESYSTEM_TYPE,
    "ATTR_SYSTEM_MEMORY_STATE": ()=>ATTR_SYSTEM_MEMORY_STATE,
    "ATTR_SYSTEM_NETWORK_STATE": ()=>ATTR_SYSTEM_NETWORK_STATE,
    "ATTR_SYSTEM_PAGING_DIRECTION": ()=>ATTR_SYSTEM_PAGING_DIRECTION,
    "ATTR_SYSTEM_PAGING_STATE": ()=>ATTR_SYSTEM_PAGING_STATE,
    "ATTR_SYSTEM_PAGING_TYPE": ()=>ATTR_SYSTEM_PAGING_TYPE,
    "ATTR_SYSTEM_PROCESSES_STATUS": ()=>ATTR_SYSTEM_PROCESSES_STATUS,
    "ATTR_SYSTEM_PROCESS_STATUS": ()=>ATTR_SYSTEM_PROCESS_STATUS,
    "ATTR_TELEMETRY_DISTRO_NAME": ()=>ATTR_TELEMETRY_DISTRO_NAME,
    "ATTR_TELEMETRY_DISTRO_VERSION": ()=>ATTR_TELEMETRY_DISTRO_VERSION,
    "ATTR_TEST_CASE_NAME": ()=>ATTR_TEST_CASE_NAME,
    "ATTR_TEST_CASE_RESULT_STATUS": ()=>ATTR_TEST_CASE_RESULT_STATUS,
    "ATTR_TEST_SUITE_NAME": ()=>ATTR_TEST_SUITE_NAME,
    "ATTR_TEST_SUITE_RUN_STATUS": ()=>ATTR_TEST_SUITE_RUN_STATUS,
    "ATTR_THREAD_ID": ()=>ATTR_THREAD_ID,
    "ATTR_THREAD_NAME": ()=>ATTR_THREAD_NAME,
    "ATTR_TLS_CIPHER": ()=>ATTR_TLS_CIPHER,
    "ATTR_TLS_CLIENT_CERTIFICATE": ()=>ATTR_TLS_CLIENT_CERTIFICATE,
    "ATTR_TLS_CLIENT_CERTIFICATE_CHAIN": ()=>ATTR_TLS_CLIENT_CERTIFICATE_CHAIN,
    "ATTR_TLS_CLIENT_HASH_MD5": ()=>ATTR_TLS_CLIENT_HASH_MD5,
    "ATTR_TLS_CLIENT_HASH_SHA1": ()=>ATTR_TLS_CLIENT_HASH_SHA1,
    "ATTR_TLS_CLIENT_HASH_SHA256": ()=>ATTR_TLS_CLIENT_HASH_SHA256,
    "ATTR_TLS_CLIENT_ISSUER": ()=>ATTR_TLS_CLIENT_ISSUER,
    "ATTR_TLS_CLIENT_JA3": ()=>ATTR_TLS_CLIENT_JA3,
    "ATTR_TLS_CLIENT_NOT_AFTER": ()=>ATTR_TLS_CLIENT_NOT_AFTER,
    "ATTR_TLS_CLIENT_NOT_BEFORE": ()=>ATTR_TLS_CLIENT_NOT_BEFORE,
    "ATTR_TLS_CLIENT_SERVER_NAME": ()=>ATTR_TLS_CLIENT_SERVER_NAME,
    "ATTR_TLS_CLIENT_SUBJECT": ()=>ATTR_TLS_CLIENT_SUBJECT,
    "ATTR_TLS_CLIENT_SUPPORTED_CIPHERS": ()=>ATTR_TLS_CLIENT_SUPPORTED_CIPHERS,
    "ATTR_TLS_CURVE": ()=>ATTR_TLS_CURVE,
    "ATTR_TLS_ESTABLISHED": ()=>ATTR_TLS_ESTABLISHED,
    "ATTR_TLS_NEXT_PROTOCOL": ()=>ATTR_TLS_NEXT_PROTOCOL,
    "ATTR_TLS_PROTOCOL_NAME": ()=>ATTR_TLS_PROTOCOL_NAME,
    "ATTR_TLS_PROTOCOL_VERSION": ()=>ATTR_TLS_PROTOCOL_VERSION,
    "ATTR_TLS_RESUMED": ()=>ATTR_TLS_RESUMED,
    "ATTR_TLS_SERVER_CERTIFICATE": ()=>ATTR_TLS_SERVER_CERTIFICATE,
    "ATTR_TLS_SERVER_CERTIFICATE_CHAIN": ()=>ATTR_TLS_SERVER_CERTIFICATE_CHAIN,
    "ATTR_TLS_SERVER_HASH_MD5": ()=>ATTR_TLS_SERVER_HASH_MD5,
    "ATTR_TLS_SERVER_HASH_SHA1": ()=>ATTR_TLS_SERVER_HASH_SHA1,
    "ATTR_TLS_SERVER_HASH_SHA256": ()=>ATTR_TLS_SERVER_HASH_SHA256,
    "ATTR_TLS_SERVER_ISSUER": ()=>ATTR_TLS_SERVER_ISSUER,
    "ATTR_TLS_SERVER_JA3S": ()=>ATTR_TLS_SERVER_JA3S,
    "ATTR_TLS_SERVER_NOT_AFTER": ()=>ATTR_TLS_SERVER_NOT_AFTER,
    "ATTR_TLS_SERVER_NOT_BEFORE": ()=>ATTR_TLS_SERVER_NOT_BEFORE,
    "ATTR_TLS_SERVER_SUBJECT": ()=>ATTR_TLS_SERVER_SUBJECT,
    "ATTR_URL_DOMAIN": ()=>ATTR_URL_DOMAIN,
    "ATTR_URL_EXTENSION": ()=>ATTR_URL_EXTENSION,
    "ATTR_URL_ORIGINAL": ()=>ATTR_URL_ORIGINAL,
    "ATTR_URL_PORT": ()=>ATTR_URL_PORT,
    "ATTR_URL_REGISTERED_DOMAIN": ()=>ATTR_URL_REGISTERED_DOMAIN,
    "ATTR_URL_SUBDOMAIN": ()=>ATTR_URL_SUBDOMAIN,
    "ATTR_URL_TEMPLATE": ()=>ATTR_URL_TEMPLATE,
    "ATTR_URL_TOP_LEVEL_DOMAIN": ()=>ATTR_URL_TOP_LEVEL_DOMAIN,
    "ATTR_USER_AGENT_NAME": ()=>ATTR_USER_AGENT_NAME,
    "ATTR_USER_AGENT_VERSION": ()=>ATTR_USER_AGENT_VERSION,
    "ATTR_USER_EMAIL": ()=>ATTR_USER_EMAIL,
    "ATTR_USER_FULL_NAME": ()=>ATTR_USER_FULL_NAME,
    "ATTR_USER_HASH": ()=>ATTR_USER_HASH,
    "ATTR_USER_ID": ()=>ATTR_USER_ID,
    "ATTR_USER_NAME": ()=>ATTR_USER_NAME,
    "ATTR_USER_ROLES": ()=>ATTR_USER_ROLES,
    "ATTR_V8JS_GC_TYPE": ()=>ATTR_V8JS_GC_TYPE,
    "ATTR_V8JS_HEAP_SPACE_NAME": ()=>ATTR_V8JS_HEAP_SPACE_NAME,
    "ATTR_VCS_REPOSITORY_CHANGE_ID": ()=>ATTR_VCS_REPOSITORY_CHANGE_ID,
    "ATTR_VCS_REPOSITORY_CHANGE_TITLE": ()=>ATTR_VCS_REPOSITORY_CHANGE_TITLE,
    "ATTR_VCS_REPOSITORY_REF_NAME": ()=>ATTR_VCS_REPOSITORY_REF_NAME,
    "ATTR_VCS_REPOSITORY_REF_REVISION": ()=>ATTR_VCS_REPOSITORY_REF_REVISION,
    "ATTR_VCS_REPOSITORY_REF_TYPE": ()=>ATTR_VCS_REPOSITORY_REF_TYPE,
    "ATTR_VCS_REPOSITORY_URL_FULL": ()=>ATTR_VCS_REPOSITORY_URL_FULL,
    "ATTR_WEBENGINE_DESCRIPTION": ()=>ATTR_WEBENGINE_DESCRIPTION,
    "ATTR_WEBENGINE_NAME": ()=>ATTR_WEBENGINE_NAME,
    "ATTR_WEBENGINE_VERSION": ()=>ATTR_WEBENGINE_VERSION,
    "AWS_ECS_LAUNCHTYPE_VALUE_EC2": ()=>AWS_ECS_LAUNCHTYPE_VALUE_EC2,
    "AWS_ECS_LAUNCHTYPE_VALUE_FARGATE": ()=>AWS_ECS_LAUNCHTYPE_VALUE_FARGATE,
    "CICD_PIPELINE_TASK_TYPE_VALUE_BUILD": ()=>CICD_PIPELINE_TASK_TYPE_VALUE_BUILD,
    "CICD_PIPELINE_TASK_TYPE_VALUE_DEPLOY": ()=>CICD_PIPELINE_TASK_TYPE_VALUE_DEPLOY,
    "CICD_PIPELINE_TASK_TYPE_VALUE_TEST": ()=>CICD_PIPELINE_TASK_TYPE_VALUE_TEST,
    "CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_ECS": ()=>CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_ECS,
    "CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_FC": ()=>CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_FC,
    "CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_OPENSHIFT": ()=>CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_OPENSHIFT,
    "CLOUD_PLATFORM_VALUE_AWS_APP_RUNNER": ()=>CLOUD_PLATFORM_VALUE_AWS_APP_RUNNER,
    "CLOUD_PLATFORM_VALUE_AWS_EC2": ()=>CLOUD_PLATFORM_VALUE_AWS_EC2,
    "CLOUD_PLATFORM_VALUE_AWS_ECS": ()=>CLOUD_PLATFORM_VALUE_AWS_ECS,
    "CLOUD_PLATFORM_VALUE_AWS_EKS": ()=>CLOUD_PLATFORM_VALUE_AWS_EKS,
    "CLOUD_PLATFORM_VALUE_AWS_ELASTIC_BEANSTALK": ()=>CLOUD_PLATFORM_VALUE_AWS_ELASTIC_BEANSTALK,
    "CLOUD_PLATFORM_VALUE_AWS_LAMBDA": ()=>CLOUD_PLATFORM_VALUE_AWS_LAMBDA,
    "CLOUD_PLATFORM_VALUE_AWS_OPENSHIFT": ()=>CLOUD_PLATFORM_VALUE_AWS_OPENSHIFT,
    "CLOUD_PLATFORM_VALUE_AZURE_AKS": ()=>CLOUD_PLATFORM_VALUE_AZURE_AKS,
    "CLOUD_PLATFORM_VALUE_AZURE_APP_SERVICE": ()=>CLOUD_PLATFORM_VALUE_AZURE_APP_SERVICE,
    "CLOUD_PLATFORM_VALUE_AZURE_CONTAINER_APPS": ()=>CLOUD_PLATFORM_VALUE_AZURE_CONTAINER_APPS,
    "CLOUD_PLATFORM_VALUE_AZURE_CONTAINER_INSTANCES": ()=>CLOUD_PLATFORM_VALUE_AZURE_CONTAINER_INSTANCES,
    "CLOUD_PLATFORM_VALUE_AZURE_FUNCTIONS": ()=>CLOUD_PLATFORM_VALUE_AZURE_FUNCTIONS,
    "CLOUD_PLATFORM_VALUE_AZURE_OPENSHIFT": ()=>CLOUD_PLATFORM_VALUE_AZURE_OPENSHIFT,
    "CLOUD_PLATFORM_VALUE_AZURE_VM": ()=>CLOUD_PLATFORM_VALUE_AZURE_VM,
    "CLOUD_PLATFORM_VALUE_GCP_APP_ENGINE": ()=>CLOUD_PLATFORM_VALUE_GCP_APP_ENGINE,
    "CLOUD_PLATFORM_VALUE_GCP_BARE_METAL_SOLUTION": ()=>CLOUD_PLATFORM_VALUE_GCP_BARE_METAL_SOLUTION,
    "CLOUD_PLATFORM_VALUE_GCP_CLOUD_FUNCTIONS": ()=>CLOUD_PLATFORM_VALUE_GCP_CLOUD_FUNCTIONS,
    "CLOUD_PLATFORM_VALUE_GCP_CLOUD_RUN": ()=>CLOUD_PLATFORM_VALUE_GCP_CLOUD_RUN,
    "CLOUD_PLATFORM_VALUE_GCP_COMPUTE_ENGINE": ()=>CLOUD_PLATFORM_VALUE_GCP_COMPUTE_ENGINE,
    "CLOUD_PLATFORM_VALUE_GCP_KUBERNETES_ENGINE": ()=>CLOUD_PLATFORM_VALUE_GCP_KUBERNETES_ENGINE,
    "CLOUD_PLATFORM_VALUE_GCP_OPENSHIFT": ()=>CLOUD_PLATFORM_VALUE_GCP_OPENSHIFT,
    "CLOUD_PLATFORM_VALUE_IBM_CLOUD_OPENSHIFT": ()=>CLOUD_PLATFORM_VALUE_IBM_CLOUD_OPENSHIFT,
    "CLOUD_PLATFORM_VALUE_TENCENT_CLOUD_CVM": ()=>CLOUD_PLATFORM_VALUE_TENCENT_CLOUD_CVM,
    "CLOUD_PLATFORM_VALUE_TENCENT_CLOUD_EKS": ()=>CLOUD_PLATFORM_VALUE_TENCENT_CLOUD_EKS,
    "CLOUD_PLATFORM_VALUE_TENCENT_CLOUD_SCF": ()=>CLOUD_PLATFORM_VALUE_TENCENT_CLOUD_SCF,
    "CLOUD_PROVIDER_VALUE_ALIBABA_CLOUD": ()=>CLOUD_PROVIDER_VALUE_ALIBABA_CLOUD,
    "CLOUD_PROVIDER_VALUE_AWS": ()=>CLOUD_PROVIDER_VALUE_AWS,
    "CLOUD_PROVIDER_VALUE_AZURE": ()=>CLOUD_PROVIDER_VALUE_AZURE,
    "CLOUD_PROVIDER_VALUE_GCP": ()=>CLOUD_PROVIDER_VALUE_GCP,
    "CLOUD_PROVIDER_VALUE_HEROKU": ()=>CLOUD_PROVIDER_VALUE_HEROKU,
    "CLOUD_PROVIDER_VALUE_IBM_CLOUD": ()=>CLOUD_PROVIDER_VALUE_IBM_CLOUD,
    "CLOUD_PROVIDER_VALUE_TENCENT_CLOUD": ()=>CLOUD_PROVIDER_VALUE_TENCENT_CLOUD,
    "CONTAINER_CPU_STATE_VALUE_KERNEL": ()=>CONTAINER_CPU_STATE_VALUE_KERNEL,
    "CONTAINER_CPU_STATE_VALUE_SYSTEM": ()=>CONTAINER_CPU_STATE_VALUE_SYSTEM,
    "CONTAINER_CPU_STATE_VALUE_USER": ()=>CONTAINER_CPU_STATE_VALUE_USER,
    "CPU_MODE_VALUE_IDLE": ()=>CPU_MODE_VALUE_IDLE,
    "CPU_MODE_VALUE_INTERRUPT": ()=>CPU_MODE_VALUE_INTERRUPT,
    "CPU_MODE_VALUE_IOWAIT": ()=>CPU_MODE_VALUE_IOWAIT,
    "CPU_MODE_VALUE_KERNEL": ()=>CPU_MODE_VALUE_KERNEL,
    "CPU_MODE_VALUE_NICE": ()=>CPU_MODE_VALUE_NICE,
    "CPU_MODE_VALUE_STEAL": ()=>CPU_MODE_VALUE_STEAL,
    "CPU_MODE_VALUE_SYSTEM": ()=>CPU_MODE_VALUE_SYSTEM,
    "CPU_MODE_VALUE_USER": ()=>CPU_MODE_VALUE_USER,
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ALL": ()=>DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ALL,
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ANY": ()=>DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ANY,
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_EACH_QUORUM": ()=>DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_EACH_QUORUM,
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_ONE": ()=>DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_ONE,
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_QUORUM": ()=>DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_QUORUM,
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_SERIAL": ()=>DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_SERIAL,
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ONE": ()=>DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ONE,
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_QUORUM": ()=>DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_QUORUM,
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_SERIAL": ()=>DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_SERIAL,
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_THREE": ()=>DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_THREE,
    "DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_TWO": ()=>DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_TWO,
    "DB_CLIENT_CONNECTIONS_STATE_VALUE_IDLE": ()=>DB_CLIENT_CONNECTIONS_STATE_VALUE_IDLE,
    "DB_CLIENT_CONNECTIONS_STATE_VALUE_USED": ()=>DB_CLIENT_CONNECTIONS_STATE_VALUE_USED,
    "DB_CLIENT_CONNECTION_STATE_VALUE_IDLE": ()=>DB_CLIENT_CONNECTION_STATE_VALUE_IDLE,
    "DB_CLIENT_CONNECTION_STATE_VALUE_USED": ()=>DB_CLIENT_CONNECTION_STATE_VALUE_USED,
    "DB_COSMOSDB_CONNECTION_MODE_VALUE_DIRECT": ()=>DB_COSMOSDB_CONNECTION_MODE_VALUE_DIRECT,
    "DB_COSMOSDB_CONNECTION_MODE_VALUE_GATEWAY": ()=>DB_COSMOSDB_CONNECTION_MODE_VALUE_GATEWAY,
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_BATCH": ()=>DB_COSMOSDB_OPERATION_TYPE_VALUE_BATCH,
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_CREATE": ()=>DB_COSMOSDB_OPERATION_TYPE_VALUE_CREATE,
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_DELETE": ()=>DB_COSMOSDB_OPERATION_TYPE_VALUE_DELETE,
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_EXECUTE": ()=>DB_COSMOSDB_OPERATION_TYPE_VALUE_EXECUTE,
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_EXECUTE_JAVASCRIPT": ()=>DB_COSMOSDB_OPERATION_TYPE_VALUE_EXECUTE_JAVASCRIPT,
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_HEAD": ()=>DB_COSMOSDB_OPERATION_TYPE_VALUE_HEAD,
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_HEAD_FEED": ()=>DB_COSMOSDB_OPERATION_TYPE_VALUE_HEAD_FEED,
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_INVALID": ()=>DB_COSMOSDB_OPERATION_TYPE_VALUE_INVALID,
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_PATCH": ()=>DB_COSMOSDB_OPERATION_TYPE_VALUE_PATCH,
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_QUERY": ()=>DB_COSMOSDB_OPERATION_TYPE_VALUE_QUERY,
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_QUERY_PLAN": ()=>DB_COSMOSDB_OPERATION_TYPE_VALUE_QUERY_PLAN,
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_READ": ()=>DB_COSMOSDB_OPERATION_TYPE_VALUE_READ,
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_READ_FEED": ()=>DB_COSMOSDB_OPERATION_TYPE_VALUE_READ_FEED,
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_REPLACE": ()=>DB_COSMOSDB_OPERATION_TYPE_VALUE_REPLACE,
    "DB_COSMOSDB_OPERATION_TYPE_VALUE_UPSERT": ()=>DB_COSMOSDB_OPERATION_TYPE_VALUE_UPSERT,
    "DB_SYSTEM_VALUE_ADABAS": ()=>DB_SYSTEM_VALUE_ADABAS,
    "DB_SYSTEM_VALUE_CACHE": ()=>DB_SYSTEM_VALUE_CACHE,
    "DB_SYSTEM_VALUE_CASSANDRA": ()=>DB_SYSTEM_VALUE_CASSANDRA,
    "DB_SYSTEM_VALUE_CLICKHOUSE": ()=>DB_SYSTEM_VALUE_CLICKHOUSE,
    "DB_SYSTEM_VALUE_CLOUDSCAPE": ()=>DB_SYSTEM_VALUE_CLOUDSCAPE,
    "DB_SYSTEM_VALUE_COCKROACHDB": ()=>DB_SYSTEM_VALUE_COCKROACHDB,
    "DB_SYSTEM_VALUE_COLDFUSION": ()=>DB_SYSTEM_VALUE_COLDFUSION,
    "DB_SYSTEM_VALUE_COSMOSDB": ()=>DB_SYSTEM_VALUE_COSMOSDB,
    "DB_SYSTEM_VALUE_COUCHBASE": ()=>DB_SYSTEM_VALUE_COUCHBASE,
    "DB_SYSTEM_VALUE_COUCHDB": ()=>DB_SYSTEM_VALUE_COUCHDB,
    "DB_SYSTEM_VALUE_DB2": ()=>DB_SYSTEM_VALUE_DB2,
    "DB_SYSTEM_VALUE_DERBY": ()=>DB_SYSTEM_VALUE_DERBY,
    "DB_SYSTEM_VALUE_DYNAMODB": ()=>DB_SYSTEM_VALUE_DYNAMODB,
    "DB_SYSTEM_VALUE_EDB": ()=>DB_SYSTEM_VALUE_EDB,
    "DB_SYSTEM_VALUE_ELASTICSEARCH": ()=>DB_SYSTEM_VALUE_ELASTICSEARCH,
    "DB_SYSTEM_VALUE_FILEMAKER": ()=>DB_SYSTEM_VALUE_FILEMAKER,
    "DB_SYSTEM_VALUE_FIREBIRD": ()=>DB_SYSTEM_VALUE_FIREBIRD,
    "DB_SYSTEM_VALUE_FIRSTSQL": ()=>DB_SYSTEM_VALUE_FIRSTSQL,
    "DB_SYSTEM_VALUE_GEODE": ()=>DB_SYSTEM_VALUE_GEODE,
    "DB_SYSTEM_VALUE_H2": ()=>DB_SYSTEM_VALUE_H2,
    "DB_SYSTEM_VALUE_HANADB": ()=>DB_SYSTEM_VALUE_HANADB,
    "DB_SYSTEM_VALUE_HBASE": ()=>DB_SYSTEM_VALUE_HBASE,
    "DB_SYSTEM_VALUE_HIVE": ()=>DB_SYSTEM_VALUE_HIVE,
    "DB_SYSTEM_VALUE_HSQLDB": ()=>DB_SYSTEM_VALUE_HSQLDB,
    "DB_SYSTEM_VALUE_INFLUXDB": ()=>DB_SYSTEM_VALUE_INFLUXDB,
    "DB_SYSTEM_VALUE_INFORMIX": ()=>DB_SYSTEM_VALUE_INFORMIX,
    "DB_SYSTEM_VALUE_INGRES": ()=>DB_SYSTEM_VALUE_INGRES,
    "DB_SYSTEM_VALUE_INSTANTDB": ()=>DB_SYSTEM_VALUE_INSTANTDB,
    "DB_SYSTEM_VALUE_INTERBASE": ()=>DB_SYSTEM_VALUE_INTERBASE,
    "DB_SYSTEM_VALUE_INTERSYSTEMS_CACHE": ()=>DB_SYSTEM_VALUE_INTERSYSTEMS_CACHE,
    "DB_SYSTEM_VALUE_MARIADB": ()=>DB_SYSTEM_VALUE_MARIADB,
    "DB_SYSTEM_VALUE_MAXDB": ()=>DB_SYSTEM_VALUE_MAXDB,
    "DB_SYSTEM_VALUE_MEMCACHED": ()=>DB_SYSTEM_VALUE_MEMCACHED,
    "DB_SYSTEM_VALUE_MONGODB": ()=>DB_SYSTEM_VALUE_MONGODB,
    "DB_SYSTEM_VALUE_MSSQL": ()=>DB_SYSTEM_VALUE_MSSQL,
    "DB_SYSTEM_VALUE_MSSQLCOMPACT": ()=>DB_SYSTEM_VALUE_MSSQLCOMPACT,
    "DB_SYSTEM_VALUE_MYSQL": ()=>DB_SYSTEM_VALUE_MYSQL,
    "DB_SYSTEM_VALUE_NEO4J": ()=>DB_SYSTEM_VALUE_NEO4J,
    "DB_SYSTEM_VALUE_NETEZZA": ()=>DB_SYSTEM_VALUE_NETEZZA,
    "DB_SYSTEM_VALUE_OPENSEARCH": ()=>DB_SYSTEM_VALUE_OPENSEARCH,
    "DB_SYSTEM_VALUE_ORACLE": ()=>DB_SYSTEM_VALUE_ORACLE,
    "DB_SYSTEM_VALUE_OTHER_SQL": ()=>DB_SYSTEM_VALUE_OTHER_SQL,
    "DB_SYSTEM_VALUE_PERVASIVE": ()=>DB_SYSTEM_VALUE_PERVASIVE,
    "DB_SYSTEM_VALUE_POINTBASE": ()=>DB_SYSTEM_VALUE_POINTBASE,
    "DB_SYSTEM_VALUE_POSTGRESQL": ()=>DB_SYSTEM_VALUE_POSTGRESQL,
    "DB_SYSTEM_VALUE_PROGRESS": ()=>DB_SYSTEM_VALUE_PROGRESS,
    "DB_SYSTEM_VALUE_REDIS": ()=>DB_SYSTEM_VALUE_REDIS,
    "DB_SYSTEM_VALUE_REDSHIFT": ()=>DB_SYSTEM_VALUE_REDSHIFT,
    "DB_SYSTEM_VALUE_SPANNER": ()=>DB_SYSTEM_VALUE_SPANNER,
    "DB_SYSTEM_VALUE_SQLITE": ()=>DB_SYSTEM_VALUE_SQLITE,
    "DB_SYSTEM_VALUE_SYBASE": ()=>DB_SYSTEM_VALUE_SYBASE,
    "DB_SYSTEM_VALUE_TERADATA": ()=>DB_SYSTEM_VALUE_TERADATA,
    "DB_SYSTEM_VALUE_TRINO": ()=>DB_SYSTEM_VALUE_TRINO,
    "DB_SYSTEM_VALUE_VERTICA": ()=>DB_SYSTEM_VALUE_VERTICA,
    "DEPLOYMENT_STATUS_VALUE_FAILED": ()=>DEPLOYMENT_STATUS_VALUE_FAILED,
    "DEPLOYMENT_STATUS_VALUE_SUCCEEDED": ()=>DEPLOYMENT_STATUS_VALUE_SUCCEEDED,
    "DISK_IO_DIRECTION_VALUE_READ": ()=>DISK_IO_DIRECTION_VALUE_READ,
    "DISK_IO_DIRECTION_VALUE_WRITE": ()=>DISK_IO_DIRECTION_VALUE_WRITE,
    "FAAS_DOCUMENT_OPERATION_VALUE_DELETE": ()=>FAAS_DOCUMENT_OPERATION_VALUE_DELETE,
    "FAAS_DOCUMENT_OPERATION_VALUE_EDIT": ()=>FAAS_DOCUMENT_OPERATION_VALUE_EDIT,
    "FAAS_DOCUMENT_OPERATION_VALUE_INSERT": ()=>FAAS_DOCUMENT_OPERATION_VALUE_INSERT,
    "FAAS_INVOKED_PROVIDER_VALUE_ALIBABA_CLOUD": ()=>FAAS_INVOKED_PROVIDER_VALUE_ALIBABA_CLOUD,
    "FAAS_INVOKED_PROVIDER_VALUE_AWS": ()=>FAAS_INVOKED_PROVIDER_VALUE_AWS,
    "FAAS_INVOKED_PROVIDER_VALUE_AZURE": ()=>FAAS_INVOKED_PROVIDER_VALUE_AZURE,
    "FAAS_INVOKED_PROVIDER_VALUE_GCP": ()=>FAAS_INVOKED_PROVIDER_VALUE_GCP,
    "FAAS_INVOKED_PROVIDER_VALUE_TENCENT_CLOUD": ()=>FAAS_INVOKED_PROVIDER_VALUE_TENCENT_CLOUD,
    "FAAS_TRIGGER_VALUE_DATASOURCE": ()=>FAAS_TRIGGER_VALUE_DATASOURCE,
    "FAAS_TRIGGER_VALUE_HTTP": ()=>FAAS_TRIGGER_VALUE_HTTP,
    "FAAS_TRIGGER_VALUE_OTHER": ()=>FAAS_TRIGGER_VALUE_OTHER,
    "FAAS_TRIGGER_VALUE_PUBSUB": ()=>FAAS_TRIGGER_VALUE_PUBSUB,
    "FAAS_TRIGGER_VALUE_TIMER": ()=>FAAS_TRIGGER_VALUE_TIMER,
    "GEN_AI_OPERATION_NAME_VALUE_CHAT": ()=>GEN_AI_OPERATION_NAME_VALUE_CHAT,
    "GEN_AI_OPERATION_NAME_VALUE_TEXT_COMPLETION": ()=>GEN_AI_OPERATION_NAME_VALUE_TEXT_COMPLETION,
    "GEN_AI_SYSTEM_VALUE_ANTHROPIC": ()=>GEN_AI_SYSTEM_VALUE_ANTHROPIC,
    "GEN_AI_SYSTEM_VALUE_COHERE": ()=>GEN_AI_SYSTEM_VALUE_COHERE,
    "GEN_AI_SYSTEM_VALUE_OPENAI": ()=>GEN_AI_SYSTEM_VALUE_OPENAI,
    "GEN_AI_SYSTEM_VALUE_VERTEX_AI": ()=>GEN_AI_SYSTEM_VALUE_VERTEX_AI,
    "GEN_AI_TOKEN_TYPE_VALUE_COMPLETION": ()=>GEN_AI_TOKEN_TYPE_VALUE_COMPLETION,
    "GEN_AI_TOKEN_TYPE_VALUE_INPUT": ()=>GEN_AI_TOKEN_TYPE_VALUE_INPUT,
    "GO_MEMORY_TYPE_VALUE_OTHER": ()=>GO_MEMORY_TYPE_VALUE_OTHER,
    "GO_MEMORY_TYPE_VALUE_STACK": ()=>GO_MEMORY_TYPE_VALUE_STACK,
    "GRAPHQL_OPERATION_TYPE_VALUE_MUTATION": ()=>GRAPHQL_OPERATION_TYPE_VALUE_MUTATION,
    "GRAPHQL_OPERATION_TYPE_VALUE_QUERY": ()=>GRAPHQL_OPERATION_TYPE_VALUE_QUERY,
    "GRAPHQL_OPERATION_TYPE_VALUE_SUBSCRIPTION": ()=>GRAPHQL_OPERATION_TYPE_VALUE_SUBSCRIPTION,
    "HOST_ARCH_VALUE_AMD64": ()=>HOST_ARCH_VALUE_AMD64,
    "HOST_ARCH_VALUE_ARM32": ()=>HOST_ARCH_VALUE_ARM32,
    "HOST_ARCH_VALUE_ARM64": ()=>HOST_ARCH_VALUE_ARM64,
    "HOST_ARCH_VALUE_IA64": ()=>HOST_ARCH_VALUE_IA64,
    "HOST_ARCH_VALUE_PPC32": ()=>HOST_ARCH_VALUE_PPC32,
    "HOST_ARCH_VALUE_PPC64": ()=>HOST_ARCH_VALUE_PPC64,
    "HOST_ARCH_VALUE_S390X": ()=>HOST_ARCH_VALUE_S390X,
    "HOST_ARCH_VALUE_X86": ()=>HOST_ARCH_VALUE_X86,
    "HTTP_CONNECTION_STATE_VALUE_ACTIVE": ()=>HTTP_CONNECTION_STATE_VALUE_ACTIVE,
    "HTTP_CONNECTION_STATE_VALUE_IDLE": ()=>HTTP_CONNECTION_STATE_VALUE_IDLE,
    "HTTP_FLAVOR_VALUE_HTTP_1_0": ()=>HTTP_FLAVOR_VALUE_HTTP_1_0,
    "HTTP_FLAVOR_VALUE_HTTP_1_1": ()=>HTTP_FLAVOR_VALUE_HTTP_1_1,
    "HTTP_FLAVOR_VALUE_HTTP_2_0": ()=>HTTP_FLAVOR_VALUE_HTTP_2_0,
    "HTTP_FLAVOR_VALUE_HTTP_3_0": ()=>HTTP_FLAVOR_VALUE_HTTP_3_0,
    "HTTP_FLAVOR_VALUE_QUIC": ()=>HTTP_FLAVOR_VALUE_QUIC,
    "HTTP_FLAVOR_VALUE_SPDY": ()=>HTTP_FLAVOR_VALUE_SPDY,
    "IOS_STATE_VALUE_ACTIVE": ()=>IOS_STATE_VALUE_ACTIVE,
    "IOS_STATE_VALUE_BACKGROUND": ()=>IOS_STATE_VALUE_BACKGROUND,
    "IOS_STATE_VALUE_FOREGROUND": ()=>IOS_STATE_VALUE_FOREGROUND,
    "IOS_STATE_VALUE_INACTIVE": ()=>IOS_STATE_VALUE_INACTIVE,
    "IOS_STATE_VALUE_TERMINATE": ()=>IOS_STATE_VALUE_TERMINATE,
    "LINUX_MEMORY_SLAB_STATE_VALUE_RECLAIMABLE": ()=>LINUX_MEMORY_SLAB_STATE_VALUE_RECLAIMABLE,
    "LINUX_MEMORY_SLAB_STATE_VALUE_UNRECLAIMABLE": ()=>LINUX_MEMORY_SLAB_STATE_VALUE_UNRECLAIMABLE,
    "LOG_IOSTREAM_VALUE_STDERR": ()=>LOG_IOSTREAM_VALUE_STDERR,
    "LOG_IOSTREAM_VALUE_STDOUT": ()=>LOG_IOSTREAM_VALUE_STDOUT,
    "MESSAGE_TYPE_VALUE_RECEIVED": ()=>MESSAGE_TYPE_VALUE_RECEIVED,
    "MESSAGE_TYPE_VALUE_SENT": ()=>MESSAGE_TYPE_VALUE_SENT,
    "MESSAGING_OPERATION_TYPE_VALUE_CREATE": ()=>MESSAGING_OPERATION_TYPE_VALUE_CREATE,
    "MESSAGING_OPERATION_TYPE_VALUE_DELIVER": ()=>MESSAGING_OPERATION_TYPE_VALUE_DELIVER,
    "MESSAGING_OPERATION_TYPE_VALUE_PROCESS": ()=>MESSAGING_OPERATION_TYPE_VALUE_PROCESS,
    "MESSAGING_OPERATION_TYPE_VALUE_PUBLISH": ()=>MESSAGING_OPERATION_TYPE_VALUE_PUBLISH,
    "MESSAGING_OPERATION_TYPE_VALUE_RECEIVE": ()=>MESSAGING_OPERATION_TYPE_VALUE_RECEIVE,
    "MESSAGING_OPERATION_TYPE_VALUE_SETTLE": ()=>MESSAGING_OPERATION_TYPE_VALUE_SETTLE,
    "MESSAGING_ROCKETMQ_CONSUMPTION_MODEL_VALUE_BROADCASTING": ()=>MESSAGING_ROCKETMQ_CONSUMPTION_MODEL_VALUE_BROADCASTING,
    "MESSAGING_ROCKETMQ_CONSUMPTION_MODEL_VALUE_CLUSTERING": ()=>MESSAGING_ROCKETMQ_CONSUMPTION_MODEL_VALUE_CLUSTERING,
    "MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_DELAY": ()=>MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_DELAY,
    "MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_FIFO": ()=>MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_FIFO,
    "MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_NORMAL": ()=>MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_NORMAL,
    "MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_TRANSACTION": ()=>MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_TRANSACTION,
    "MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_ABANDON": ()=>MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_ABANDON,
    "MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_COMPLETE": ()=>MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_COMPLETE,
    "MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_DEAD_LETTER": ()=>MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_DEAD_LETTER,
    "MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_DEFER": ()=>MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_DEFER,
    "MESSAGING_SYSTEM_VALUE_ACTIVEMQ": ()=>MESSAGING_SYSTEM_VALUE_ACTIVEMQ,
    "MESSAGING_SYSTEM_VALUE_AWS_SQS": ()=>MESSAGING_SYSTEM_VALUE_AWS_SQS,
    "MESSAGING_SYSTEM_VALUE_EVENTGRID": ()=>MESSAGING_SYSTEM_VALUE_EVENTGRID,
    "MESSAGING_SYSTEM_VALUE_EVENTHUBS": ()=>MESSAGING_SYSTEM_VALUE_EVENTHUBS,
    "MESSAGING_SYSTEM_VALUE_GCP_PUBSUB": ()=>MESSAGING_SYSTEM_VALUE_GCP_PUBSUB,
    "MESSAGING_SYSTEM_VALUE_JMS": ()=>MESSAGING_SYSTEM_VALUE_JMS,
    "MESSAGING_SYSTEM_VALUE_KAFKA": ()=>MESSAGING_SYSTEM_VALUE_KAFKA,
    "MESSAGING_SYSTEM_VALUE_PULSAR": ()=>MESSAGING_SYSTEM_VALUE_PULSAR,
    "MESSAGING_SYSTEM_VALUE_RABBITMQ": ()=>MESSAGING_SYSTEM_VALUE_RABBITMQ,
    "MESSAGING_SYSTEM_VALUE_ROCKETMQ": ()=>MESSAGING_SYSTEM_VALUE_ROCKETMQ,
    "MESSAGING_SYSTEM_VALUE_SERVICEBUS": ()=>MESSAGING_SYSTEM_VALUE_SERVICEBUS,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_CDMA": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_CDMA,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_CDMA2000_1XRTT": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_CDMA2000_1XRTT,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_EDGE": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_EDGE,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_EHRPD": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_EHRPD,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_0": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_0,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_A": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_A,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_B": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_B,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_GPRS": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_GPRS,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_GSM": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_GSM,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_HSDPA": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_HSDPA,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_HSPA": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_HSPA,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_HSPAP": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_HSPAP,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_HSUPA": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_HSUPA,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_IDEN": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_IDEN,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_IWLAN": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_IWLAN,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_LTE": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_LTE,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_LTE_CA": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_LTE_CA,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_NR": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_NR,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_NRNSA": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_NRNSA,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_TD_SCDMA": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_TD_SCDMA,
    "NETWORK_CONNECTION_SUBTYPE_VALUE_UMTS": ()=>NETWORK_CONNECTION_SUBTYPE_VALUE_UMTS,
    "NETWORK_CONNECTION_TYPE_VALUE_CELL": ()=>NETWORK_CONNECTION_TYPE_VALUE_CELL,
    "NETWORK_CONNECTION_TYPE_VALUE_UNAVAILABLE": ()=>NETWORK_CONNECTION_TYPE_VALUE_UNAVAILABLE,
    "NETWORK_CONNECTION_TYPE_VALUE_UNKNOWN": ()=>NETWORK_CONNECTION_TYPE_VALUE_UNKNOWN,
    "NETWORK_CONNECTION_TYPE_VALUE_WIFI": ()=>NETWORK_CONNECTION_TYPE_VALUE_WIFI,
    "NETWORK_CONNECTION_TYPE_VALUE_WIRED": ()=>NETWORK_CONNECTION_TYPE_VALUE_WIRED,
    "NETWORK_IO_DIRECTION_VALUE_RECEIVE": ()=>NETWORK_IO_DIRECTION_VALUE_RECEIVE,
    "NETWORK_IO_DIRECTION_VALUE_TRANSMIT": ()=>NETWORK_IO_DIRECTION_VALUE_TRANSMIT,
    "NET_SOCK_FAMILY_VALUE_INET": ()=>NET_SOCK_FAMILY_VALUE_INET,
    "NET_SOCK_FAMILY_VALUE_INET6": ()=>NET_SOCK_FAMILY_VALUE_INET6,
    "NET_SOCK_FAMILY_VALUE_UNIX": ()=>NET_SOCK_FAMILY_VALUE_UNIX,
    "NET_TRANSPORT_VALUE_INPROC": ()=>NET_TRANSPORT_VALUE_INPROC,
    "NET_TRANSPORT_VALUE_IP_TCP": ()=>NET_TRANSPORT_VALUE_IP_TCP,
    "NET_TRANSPORT_VALUE_IP_UDP": ()=>NET_TRANSPORT_VALUE_IP_UDP,
    "NET_TRANSPORT_VALUE_OTHER": ()=>NET_TRANSPORT_VALUE_OTHER,
    "NET_TRANSPORT_VALUE_PIPE": ()=>NET_TRANSPORT_VALUE_PIPE,
    "OPENTRACING_REF_TYPE_VALUE_CHILD_OF": ()=>OPENTRACING_REF_TYPE_VALUE_CHILD_OF,
    "OPENTRACING_REF_TYPE_VALUE_FOLLOWS_FROM": ()=>OPENTRACING_REF_TYPE_VALUE_FOLLOWS_FROM,
    "OS_TYPE_VALUE_AIX": ()=>OS_TYPE_VALUE_AIX,
    "OS_TYPE_VALUE_DARWIN": ()=>OS_TYPE_VALUE_DARWIN,
    "OS_TYPE_VALUE_DRAGONFLYBSD": ()=>OS_TYPE_VALUE_DRAGONFLYBSD,
    "OS_TYPE_VALUE_FREEBSD": ()=>OS_TYPE_VALUE_FREEBSD,
    "OS_TYPE_VALUE_HPUX": ()=>OS_TYPE_VALUE_HPUX,
    "OS_TYPE_VALUE_LINUX": ()=>OS_TYPE_VALUE_LINUX,
    "OS_TYPE_VALUE_NETBSD": ()=>OS_TYPE_VALUE_NETBSD,
    "OS_TYPE_VALUE_OPENBSD": ()=>OS_TYPE_VALUE_OPENBSD,
    "OS_TYPE_VALUE_SOLARIS": ()=>OS_TYPE_VALUE_SOLARIS,
    "OS_TYPE_VALUE_WINDOWS": ()=>OS_TYPE_VALUE_WINDOWS,
    "OS_TYPE_VALUE_Z_OS": ()=>OS_TYPE_VALUE_Z_OS,
    "PROCESS_CONTEXT_SWITCH_TYPE_VALUE_INVOLUNTARY": ()=>PROCESS_CONTEXT_SWITCH_TYPE_VALUE_INVOLUNTARY,
    "PROCESS_CONTEXT_SWITCH_TYPE_VALUE_VOLUNTARY": ()=>PROCESS_CONTEXT_SWITCH_TYPE_VALUE_VOLUNTARY,
    "PROCESS_CPU_STATE_VALUE_SYSTEM": ()=>PROCESS_CPU_STATE_VALUE_SYSTEM,
    "PROCESS_CPU_STATE_VALUE_USER": ()=>PROCESS_CPU_STATE_VALUE_USER,
    "PROCESS_CPU_STATE_VALUE_WAIT": ()=>PROCESS_CPU_STATE_VALUE_WAIT,
    "PROCESS_PAGING_FAULT_TYPE_VALUE_MAJOR": ()=>PROCESS_PAGING_FAULT_TYPE_VALUE_MAJOR,
    "PROCESS_PAGING_FAULT_TYPE_VALUE_MINOR": ()=>PROCESS_PAGING_FAULT_TYPE_VALUE_MINOR,
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_ABORTED": ()=>RPC_CONNECT_RPC_ERROR_CODE_VALUE_ABORTED,
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_ALREADY_EXISTS": ()=>RPC_CONNECT_RPC_ERROR_CODE_VALUE_ALREADY_EXISTS,
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_CANCELLED": ()=>RPC_CONNECT_RPC_ERROR_CODE_VALUE_CANCELLED,
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_DATA_LOSS": ()=>RPC_CONNECT_RPC_ERROR_CODE_VALUE_DATA_LOSS,
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_DEADLINE_EXCEEDED": ()=>RPC_CONNECT_RPC_ERROR_CODE_VALUE_DEADLINE_EXCEEDED,
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_FAILED_PRECONDITION": ()=>RPC_CONNECT_RPC_ERROR_CODE_VALUE_FAILED_PRECONDITION,
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_INTERNAL": ()=>RPC_CONNECT_RPC_ERROR_CODE_VALUE_INTERNAL,
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_INVALID_ARGUMENT": ()=>RPC_CONNECT_RPC_ERROR_CODE_VALUE_INVALID_ARGUMENT,
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_NOT_FOUND": ()=>RPC_CONNECT_RPC_ERROR_CODE_VALUE_NOT_FOUND,
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_OUT_OF_RANGE": ()=>RPC_CONNECT_RPC_ERROR_CODE_VALUE_OUT_OF_RANGE,
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_PERMISSION_DENIED": ()=>RPC_CONNECT_RPC_ERROR_CODE_VALUE_PERMISSION_DENIED,
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_RESOURCE_EXHAUSTED": ()=>RPC_CONNECT_RPC_ERROR_CODE_VALUE_RESOURCE_EXHAUSTED,
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNAUTHENTICATED": ()=>RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNAUTHENTICATED,
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNAVAILABLE": ()=>RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNAVAILABLE,
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNIMPLEMENTED": ()=>RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNIMPLEMENTED,
    "RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNKNOWN": ()=>RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNKNOWN,
    "RPC_GRPC_STATUS_CODE_VALUE_ABORTED": ()=>RPC_GRPC_STATUS_CODE_VALUE_ABORTED,
    "RPC_GRPC_STATUS_CODE_VALUE_ALREADY_EXISTS": ()=>RPC_GRPC_STATUS_CODE_VALUE_ALREADY_EXISTS,
    "RPC_GRPC_STATUS_CODE_VALUE_CANCELLED": ()=>RPC_GRPC_STATUS_CODE_VALUE_CANCELLED,
    "RPC_GRPC_STATUS_CODE_VALUE_DATA_LOSS": ()=>RPC_GRPC_STATUS_CODE_VALUE_DATA_LOSS,
    "RPC_GRPC_STATUS_CODE_VALUE_DEADLINE_EXCEEDED": ()=>RPC_GRPC_STATUS_CODE_VALUE_DEADLINE_EXCEEDED,
    "RPC_GRPC_STATUS_CODE_VALUE_FAILED_PRECONDITION": ()=>RPC_GRPC_STATUS_CODE_VALUE_FAILED_PRECONDITION,
    "RPC_GRPC_STATUS_CODE_VALUE_INTERNAL": ()=>RPC_GRPC_STATUS_CODE_VALUE_INTERNAL,
    "RPC_GRPC_STATUS_CODE_VALUE_INVALID_ARGUMENT": ()=>RPC_GRPC_STATUS_CODE_VALUE_INVALID_ARGUMENT,
    "RPC_GRPC_STATUS_CODE_VALUE_NOT_FOUND": ()=>RPC_GRPC_STATUS_CODE_VALUE_NOT_FOUND,
    "RPC_GRPC_STATUS_CODE_VALUE_OK": ()=>RPC_GRPC_STATUS_CODE_VALUE_OK,
    "RPC_GRPC_STATUS_CODE_VALUE_OUT_OF_RANGE": ()=>RPC_GRPC_STATUS_CODE_VALUE_OUT_OF_RANGE,
    "RPC_GRPC_STATUS_CODE_VALUE_PERMISSION_DENIED": ()=>RPC_GRPC_STATUS_CODE_VALUE_PERMISSION_DENIED,
    "RPC_GRPC_STATUS_CODE_VALUE_RESOURCE_EXHAUSTED": ()=>RPC_GRPC_STATUS_CODE_VALUE_RESOURCE_EXHAUSTED,
    "RPC_GRPC_STATUS_CODE_VALUE_UNAUTHENTICATED": ()=>RPC_GRPC_STATUS_CODE_VALUE_UNAUTHENTICATED,
    "RPC_GRPC_STATUS_CODE_VALUE_UNAVAILABLE": ()=>RPC_GRPC_STATUS_CODE_VALUE_UNAVAILABLE,
    "RPC_GRPC_STATUS_CODE_VALUE_UNIMPLEMENTED": ()=>RPC_GRPC_STATUS_CODE_VALUE_UNIMPLEMENTED,
    "RPC_GRPC_STATUS_CODE_VALUE_UNKNOWN": ()=>RPC_GRPC_STATUS_CODE_VALUE_UNKNOWN,
    "RPC_MESSAGE_TYPE_VALUE_RECEIVED": ()=>RPC_MESSAGE_TYPE_VALUE_RECEIVED,
    "RPC_MESSAGE_TYPE_VALUE_SENT": ()=>RPC_MESSAGE_TYPE_VALUE_SENT,
    "RPC_SYSTEM_VALUE_APACHE_DUBBO": ()=>RPC_SYSTEM_VALUE_APACHE_DUBBO,
    "RPC_SYSTEM_VALUE_CONNECT_RPC": ()=>RPC_SYSTEM_VALUE_CONNECT_RPC,
    "RPC_SYSTEM_VALUE_DOTNET_WCF": ()=>RPC_SYSTEM_VALUE_DOTNET_WCF,
    "RPC_SYSTEM_VALUE_GRPC": ()=>RPC_SYSTEM_VALUE_GRPC,
    "RPC_SYSTEM_VALUE_JAVA_RMI": ()=>RPC_SYSTEM_VALUE_JAVA_RMI,
    "STATE_VALUE_IDLE": ()=>STATE_VALUE_IDLE,
    "STATE_VALUE_USED": ()=>STATE_VALUE_USED,
    "SYSTEM_CPU_STATE_VALUE_IDLE": ()=>SYSTEM_CPU_STATE_VALUE_IDLE,
    "SYSTEM_CPU_STATE_VALUE_INTERRUPT": ()=>SYSTEM_CPU_STATE_VALUE_INTERRUPT,
    "SYSTEM_CPU_STATE_VALUE_IOWAIT": ()=>SYSTEM_CPU_STATE_VALUE_IOWAIT,
    "SYSTEM_CPU_STATE_VALUE_NICE": ()=>SYSTEM_CPU_STATE_VALUE_NICE,
    "SYSTEM_CPU_STATE_VALUE_STEAL": ()=>SYSTEM_CPU_STATE_VALUE_STEAL,
    "SYSTEM_CPU_STATE_VALUE_SYSTEM": ()=>SYSTEM_CPU_STATE_VALUE_SYSTEM,
    "SYSTEM_CPU_STATE_VALUE_USER": ()=>SYSTEM_CPU_STATE_VALUE_USER,
    "SYSTEM_FILESYSTEM_STATE_VALUE_FREE": ()=>SYSTEM_FILESYSTEM_STATE_VALUE_FREE,
    "SYSTEM_FILESYSTEM_STATE_VALUE_RESERVED": ()=>SYSTEM_FILESYSTEM_STATE_VALUE_RESERVED,
    "SYSTEM_FILESYSTEM_STATE_VALUE_USED": ()=>SYSTEM_FILESYSTEM_STATE_VALUE_USED,
    "SYSTEM_FILESYSTEM_TYPE_VALUE_EXFAT": ()=>SYSTEM_FILESYSTEM_TYPE_VALUE_EXFAT,
    "SYSTEM_FILESYSTEM_TYPE_VALUE_EXT4": ()=>SYSTEM_FILESYSTEM_TYPE_VALUE_EXT4,
    "SYSTEM_FILESYSTEM_TYPE_VALUE_FAT32": ()=>SYSTEM_FILESYSTEM_TYPE_VALUE_FAT32,
    "SYSTEM_FILESYSTEM_TYPE_VALUE_HFSPLUS": ()=>SYSTEM_FILESYSTEM_TYPE_VALUE_HFSPLUS,
    "SYSTEM_FILESYSTEM_TYPE_VALUE_NTFS": ()=>SYSTEM_FILESYSTEM_TYPE_VALUE_NTFS,
    "SYSTEM_FILESYSTEM_TYPE_VALUE_REFS": ()=>SYSTEM_FILESYSTEM_TYPE_VALUE_REFS,
    "SYSTEM_MEMORY_STATE_VALUE_BUFFERS": ()=>SYSTEM_MEMORY_STATE_VALUE_BUFFERS,
    "SYSTEM_MEMORY_STATE_VALUE_CACHED": ()=>SYSTEM_MEMORY_STATE_VALUE_CACHED,
    "SYSTEM_MEMORY_STATE_VALUE_FREE": ()=>SYSTEM_MEMORY_STATE_VALUE_FREE,
    "SYSTEM_MEMORY_STATE_VALUE_SHARED": ()=>SYSTEM_MEMORY_STATE_VALUE_SHARED,
    "SYSTEM_MEMORY_STATE_VALUE_USED": ()=>SYSTEM_MEMORY_STATE_VALUE_USED,
    "SYSTEM_NETWORK_STATE_VALUE_CLOSE": ()=>SYSTEM_NETWORK_STATE_VALUE_CLOSE,
    "SYSTEM_NETWORK_STATE_VALUE_CLOSE_WAIT": ()=>SYSTEM_NETWORK_STATE_VALUE_CLOSE_WAIT,
    "SYSTEM_NETWORK_STATE_VALUE_CLOSING": ()=>SYSTEM_NETWORK_STATE_VALUE_CLOSING,
    "SYSTEM_NETWORK_STATE_VALUE_DELETE": ()=>SYSTEM_NETWORK_STATE_VALUE_DELETE,
    "SYSTEM_NETWORK_STATE_VALUE_ESTABLISHED": ()=>SYSTEM_NETWORK_STATE_VALUE_ESTABLISHED,
    "SYSTEM_NETWORK_STATE_VALUE_FIN_WAIT_1": ()=>SYSTEM_NETWORK_STATE_VALUE_FIN_WAIT_1,
    "SYSTEM_NETWORK_STATE_VALUE_FIN_WAIT_2": ()=>SYSTEM_NETWORK_STATE_VALUE_FIN_WAIT_2,
    "SYSTEM_NETWORK_STATE_VALUE_LAST_ACK": ()=>SYSTEM_NETWORK_STATE_VALUE_LAST_ACK,
    "SYSTEM_NETWORK_STATE_VALUE_LISTEN": ()=>SYSTEM_NETWORK_STATE_VALUE_LISTEN,
    "SYSTEM_NETWORK_STATE_VALUE_SYN_RECV": ()=>SYSTEM_NETWORK_STATE_VALUE_SYN_RECV,
    "SYSTEM_NETWORK_STATE_VALUE_SYN_SENT": ()=>SYSTEM_NETWORK_STATE_VALUE_SYN_SENT,
    "SYSTEM_NETWORK_STATE_VALUE_TIME_WAIT": ()=>SYSTEM_NETWORK_STATE_VALUE_TIME_WAIT,
    "SYSTEM_PAGING_DIRECTION_VALUE_IN": ()=>SYSTEM_PAGING_DIRECTION_VALUE_IN,
    "SYSTEM_PAGING_DIRECTION_VALUE_OUT": ()=>SYSTEM_PAGING_DIRECTION_VALUE_OUT,
    "SYSTEM_PAGING_STATE_VALUE_FREE": ()=>SYSTEM_PAGING_STATE_VALUE_FREE,
    "SYSTEM_PAGING_STATE_VALUE_USED": ()=>SYSTEM_PAGING_STATE_VALUE_USED,
    "SYSTEM_PAGING_TYPE_VALUE_MAJOR": ()=>SYSTEM_PAGING_TYPE_VALUE_MAJOR,
    "SYSTEM_PAGING_TYPE_VALUE_MINOR": ()=>SYSTEM_PAGING_TYPE_VALUE_MINOR,
    "SYSTEM_PROCESSES_STATUS_VALUE_DEFUNCT": ()=>SYSTEM_PROCESSES_STATUS_VALUE_DEFUNCT,
    "SYSTEM_PROCESSES_STATUS_VALUE_RUNNING": ()=>SYSTEM_PROCESSES_STATUS_VALUE_RUNNING,
    "SYSTEM_PROCESSES_STATUS_VALUE_SLEEPING": ()=>SYSTEM_PROCESSES_STATUS_VALUE_SLEEPING,
    "SYSTEM_PROCESSES_STATUS_VALUE_STOPPED": ()=>SYSTEM_PROCESSES_STATUS_VALUE_STOPPED,
    "SYSTEM_PROCESS_STATUS_VALUE_DEFUNCT": ()=>SYSTEM_PROCESS_STATUS_VALUE_DEFUNCT,
    "SYSTEM_PROCESS_STATUS_VALUE_RUNNING": ()=>SYSTEM_PROCESS_STATUS_VALUE_RUNNING,
    "SYSTEM_PROCESS_STATUS_VALUE_SLEEPING": ()=>SYSTEM_PROCESS_STATUS_VALUE_SLEEPING,
    "SYSTEM_PROCESS_STATUS_VALUE_STOPPED": ()=>SYSTEM_PROCESS_STATUS_VALUE_STOPPED,
    "TEST_CASE_RESULT_STATUS_VALUE_FAIL": ()=>TEST_CASE_RESULT_STATUS_VALUE_FAIL,
    "TEST_CASE_RESULT_STATUS_VALUE_PASS": ()=>TEST_CASE_RESULT_STATUS_VALUE_PASS,
    "TEST_SUITE_RUN_STATUS_VALUE_ABORTED": ()=>TEST_SUITE_RUN_STATUS_VALUE_ABORTED,
    "TEST_SUITE_RUN_STATUS_VALUE_FAILURE": ()=>TEST_SUITE_RUN_STATUS_VALUE_FAILURE,
    "TEST_SUITE_RUN_STATUS_VALUE_IN_PROGRESS": ()=>TEST_SUITE_RUN_STATUS_VALUE_IN_PROGRESS,
    "TEST_SUITE_RUN_STATUS_VALUE_SKIPPED": ()=>TEST_SUITE_RUN_STATUS_VALUE_SKIPPED,
    "TEST_SUITE_RUN_STATUS_VALUE_SUCCESS": ()=>TEST_SUITE_RUN_STATUS_VALUE_SUCCESS,
    "TEST_SUITE_RUN_STATUS_VALUE_TIMED_OUT": ()=>TEST_SUITE_RUN_STATUS_VALUE_TIMED_OUT,
    "TLS_PROTOCOL_NAME_VALUE_SSL": ()=>TLS_PROTOCOL_NAME_VALUE_SSL,
    "TLS_PROTOCOL_NAME_VALUE_TLS": ()=>TLS_PROTOCOL_NAME_VALUE_TLS,
    "V8JS_GC_TYPE_VALUE_INCREMENTAL": ()=>V8JS_GC_TYPE_VALUE_INCREMENTAL,
    "V8JS_GC_TYPE_VALUE_MAJOR": ()=>V8JS_GC_TYPE_VALUE_MAJOR,
    "V8JS_GC_TYPE_VALUE_MINOR": ()=>V8JS_GC_TYPE_VALUE_MINOR,
    "V8JS_GC_TYPE_VALUE_WEAKCB": ()=>V8JS_GC_TYPE_VALUE_WEAKCB,
    "V8JS_HEAP_SPACE_NAME_VALUE_CODE_SPACE": ()=>V8JS_HEAP_SPACE_NAME_VALUE_CODE_SPACE,
    "V8JS_HEAP_SPACE_NAME_VALUE_LARGE_OBJECT_SPACE": ()=>V8JS_HEAP_SPACE_NAME_VALUE_LARGE_OBJECT_SPACE,
    "V8JS_HEAP_SPACE_NAME_VALUE_MAP_SPACE": ()=>V8JS_HEAP_SPACE_NAME_VALUE_MAP_SPACE,
    "V8JS_HEAP_SPACE_NAME_VALUE_NEW_SPACE": ()=>V8JS_HEAP_SPACE_NAME_VALUE_NEW_SPACE,
    "V8JS_HEAP_SPACE_NAME_VALUE_OLD_SPACE": ()=>V8JS_HEAP_SPACE_NAME_VALUE_OLD_SPACE,
    "VCS_REPOSITORY_REF_TYPE_VALUE_BRANCH": ()=>VCS_REPOSITORY_REF_TYPE_VALUE_BRANCH,
    "VCS_REPOSITORY_REF_TYPE_VALUE_TAG": ()=>VCS_REPOSITORY_REF_TYPE_VALUE_TAG
});
var ATTR_AWS_ECS_TASK_ID = 'aws.ecs.task.id';
var ATTR_ANDROID_OS_API_LEVEL = 'android.os.api_level';
var ATTR_ANDROID_STATE = 'android.state';
var ANDROID_STATE_VALUE_BACKGROUND = "background";
var ANDROID_STATE_VALUE_CREATED = "created";
var ANDROID_STATE_VALUE_FOREGROUND = "foreground";
var ATTR_ARTIFACT_ATTESTATION_FILENAME = 'artifact.attestation.filename';
var ATTR_ARTIFACT_ATTESTATION_HASH = 'artifact.attestation.hash';
var ATTR_ARTIFACT_ATTESTATION_ID = 'artifact.attestation.id';
var ATTR_ARTIFACT_FILENAME = 'artifact.filename';
var ATTR_ARTIFACT_HASH = 'artifact.hash';
var ATTR_ARTIFACT_PURL = 'artifact.purl';
var ATTR_ARTIFACT_VERSION = 'artifact.version';
var ATTR_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS = 'aws.dynamodb.attribute_definitions';
var ATTR_AWS_DYNAMODB_ATTRIBUTES_TO_GET = 'aws.dynamodb.attributes_to_get';
var ATTR_AWS_DYNAMODB_CONSISTENT_READ = 'aws.dynamodb.consistent_read';
var ATTR_AWS_DYNAMODB_CONSUMED_CAPACITY = 'aws.dynamodb.consumed_capacity';
var ATTR_AWS_DYNAMODB_COUNT = 'aws.dynamodb.count';
var ATTR_AWS_DYNAMODB_EXCLUSIVE_START_TABLE = 'aws.dynamodb.exclusive_start_table';
var ATTR_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES = 'aws.dynamodb.global_secondary_index_updates';
var ATTR_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES = 'aws.dynamodb.global_secondary_indexes';
var ATTR_AWS_DYNAMODB_INDEX_NAME = 'aws.dynamodb.index_name';
var ATTR_AWS_DYNAMODB_ITEM_COLLECTION_METRICS = 'aws.dynamodb.item_collection_metrics';
var ATTR_AWS_DYNAMODB_LIMIT = 'aws.dynamodb.limit';
var ATTR_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES = 'aws.dynamodb.local_secondary_indexes';
var ATTR_AWS_DYNAMODB_PROJECTION = 'aws.dynamodb.projection';
var ATTR_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY = 'aws.dynamodb.provisioned_read_capacity';
var ATTR_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY = 'aws.dynamodb.provisioned_write_capacity';
var ATTR_AWS_DYNAMODB_SCAN_FORWARD = 'aws.dynamodb.scan_forward';
var ATTR_AWS_DYNAMODB_SCANNED_COUNT = 'aws.dynamodb.scanned_count';
var ATTR_AWS_DYNAMODB_SEGMENT = 'aws.dynamodb.segment';
var ATTR_AWS_DYNAMODB_SELECT = 'aws.dynamodb.select';
var ATTR_AWS_DYNAMODB_TABLE_COUNT = 'aws.dynamodb.table_count';
var ATTR_AWS_DYNAMODB_TABLE_NAMES = 'aws.dynamodb.table_names';
var ATTR_AWS_DYNAMODB_TOTAL_SEGMENTS = 'aws.dynamodb.total_segments';
var ATTR_AWS_ECS_CLUSTER_ARN = 'aws.ecs.cluster.arn';
var ATTR_AWS_ECS_CONTAINER_ARN = 'aws.ecs.container.arn';
var ATTR_AWS_ECS_LAUNCHTYPE = 'aws.ecs.launchtype';
var AWS_ECS_LAUNCHTYPE_VALUE_EC2 = "ec2";
var AWS_ECS_LAUNCHTYPE_VALUE_FARGATE = "fargate";
var ATTR_AWS_ECS_TASK_ARN = 'aws.ecs.task.arn';
var ATTR_AWS_ECS_TASK_FAMILY = 'aws.ecs.task.family';
var ATTR_AWS_ECS_TASK_REVISION = 'aws.ecs.task.revision';
var ATTR_AWS_EKS_CLUSTER_ARN = 'aws.eks.cluster.arn';
var ATTR_AWS_LAMBDA_INVOKED_ARN = 'aws.lambda.invoked_arn';
var ATTR_AWS_LOG_GROUP_ARNS = 'aws.log.group.arns';
var ATTR_AWS_LOG_GROUP_NAMES = 'aws.log.group.names';
var ATTR_AWS_LOG_STREAM_ARNS = 'aws.log.stream.arns';
var ATTR_AWS_LOG_STREAM_NAMES = 'aws.log.stream.names';
var ATTR_AWS_REQUEST_ID = 'aws.request_id';
var ATTR_AWS_S3_BUCKET = 'aws.s3.bucket';
var ATTR_AWS_S3_COPY_SOURCE = 'aws.s3.copy_source';
var ATTR_AWS_S3_DELETE = 'aws.s3.delete';
var ATTR_AWS_S3_KEY = 'aws.s3.key';
var ATTR_AWS_S3_PART_NUMBER = 'aws.s3.part_number';
var ATTR_AWS_S3_UPLOAD_ID = 'aws.s3.upload_id';
var ATTR_AZ_SERVICE_REQUEST_ID = 'az.service_request_id';
var ATTR_BROWSER_BRANDS = 'browser.brands';
var ATTR_BROWSER_LANGUAGE = 'browser.language';
var ATTR_BROWSER_MOBILE = 'browser.mobile';
var ATTR_BROWSER_PLATFORM = 'browser.platform';
var ATTR_CICD_PIPELINE_NAME = 'cicd.pipeline.name';
var ATTR_CICD_PIPELINE_RUN_ID = 'cicd.pipeline.run.id';
var ATTR_CICD_PIPELINE_TASK_NAME = 'cicd.pipeline.task.name';
var ATTR_CICD_PIPELINE_TASK_RUN_ID = 'cicd.pipeline.task.run.id';
var ATTR_CICD_PIPELINE_TASK_RUN_URL_FULL = 'cicd.pipeline.task.run.url.full';
var ATTR_CICD_PIPELINE_TASK_TYPE = 'cicd.pipeline.task.type';
var CICD_PIPELINE_TASK_TYPE_VALUE_BUILD = "build";
var CICD_PIPELINE_TASK_TYPE_VALUE_DEPLOY = "deploy";
var CICD_PIPELINE_TASK_TYPE_VALUE_TEST = "test";
var ATTR_CLOUD_ACCOUNT_ID = 'cloud.account.id';
var ATTR_CLOUD_AVAILABILITY_ZONE = 'cloud.availability_zone';
var ATTR_CLOUD_PLATFORM = 'cloud.platform';
var CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_ECS = "alibaba_cloud_ecs";
var CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_FC = "alibaba_cloud_fc";
var CLOUD_PLATFORM_VALUE_ALIBABA_CLOUD_OPENSHIFT = "alibaba_cloud_openshift";
var CLOUD_PLATFORM_VALUE_AWS_APP_RUNNER = "aws_app_runner";
var CLOUD_PLATFORM_VALUE_AWS_EC2 = "aws_ec2";
var CLOUD_PLATFORM_VALUE_AWS_ECS = "aws_ecs";
var CLOUD_PLATFORM_VALUE_AWS_EKS = "aws_eks";
var CLOUD_PLATFORM_VALUE_AWS_ELASTIC_BEANSTALK = "aws_elastic_beanstalk";
var CLOUD_PLATFORM_VALUE_AWS_LAMBDA = "aws_lambda";
var CLOUD_PLATFORM_VALUE_AWS_OPENSHIFT = "aws_openshift";
var CLOUD_PLATFORM_VALUE_AZURE_AKS = "azure_aks";
var CLOUD_PLATFORM_VALUE_AZURE_APP_SERVICE = "azure_app_service";
var CLOUD_PLATFORM_VALUE_AZURE_CONTAINER_APPS = "azure_container_apps";
var CLOUD_PLATFORM_VALUE_AZURE_CONTAINER_INSTANCES = "azure_container_instances";
var CLOUD_PLATFORM_VALUE_AZURE_FUNCTIONS = "azure_functions";
var CLOUD_PLATFORM_VALUE_AZURE_OPENSHIFT = "azure_openshift";
var CLOUD_PLATFORM_VALUE_AZURE_VM = "azure_vm";
var CLOUD_PLATFORM_VALUE_GCP_APP_ENGINE = "gcp_app_engine";
var CLOUD_PLATFORM_VALUE_GCP_BARE_METAL_SOLUTION = "gcp_bare_metal_solution";
var CLOUD_PLATFORM_VALUE_GCP_CLOUD_FUNCTIONS = "gcp_cloud_functions";
var CLOUD_PLATFORM_VALUE_GCP_CLOUD_RUN = "gcp_cloud_run";
var CLOUD_PLATFORM_VALUE_GCP_COMPUTE_ENGINE = "gcp_compute_engine";
var CLOUD_PLATFORM_VALUE_GCP_KUBERNETES_ENGINE = "gcp_kubernetes_engine";
var CLOUD_PLATFORM_VALUE_GCP_OPENSHIFT = "gcp_openshift";
var CLOUD_PLATFORM_VALUE_IBM_CLOUD_OPENSHIFT = "ibm_cloud_openshift";
var CLOUD_PLATFORM_VALUE_TENCENT_CLOUD_CVM = "tencent_cloud_cvm";
var CLOUD_PLATFORM_VALUE_TENCENT_CLOUD_EKS = "tencent_cloud_eks";
var CLOUD_PLATFORM_VALUE_TENCENT_CLOUD_SCF = "tencent_cloud_scf";
var ATTR_CLOUD_PROVIDER = 'cloud.provider';
var CLOUD_PROVIDER_VALUE_ALIBABA_CLOUD = "alibaba_cloud";
var CLOUD_PROVIDER_VALUE_AWS = "aws";
var CLOUD_PROVIDER_VALUE_AZURE = "azure";
var CLOUD_PROVIDER_VALUE_GCP = "gcp";
var CLOUD_PROVIDER_VALUE_HEROKU = "heroku";
var CLOUD_PROVIDER_VALUE_IBM_CLOUD = "ibm_cloud";
var CLOUD_PROVIDER_VALUE_TENCENT_CLOUD = "tencent_cloud";
var ATTR_CLOUD_REGION = 'cloud.region';
var ATTR_CLOUD_RESOURCE_ID = 'cloud.resource_id';
var ATTR_CLOUDEVENTS_EVENT_ID = 'cloudevents.event_id';
var ATTR_CLOUDEVENTS_EVENT_SOURCE = 'cloudevents.event_source';
var ATTR_CLOUDEVENTS_EVENT_SPEC_VERSION = 'cloudevents.event_spec_version';
var ATTR_CLOUDEVENTS_EVENT_SUBJECT = 'cloudevents.event_subject';
var ATTR_CLOUDEVENTS_EVENT_TYPE = 'cloudevents.event_type';
var ATTR_CODE_COLUMN = 'code.column';
var ATTR_CODE_FILEPATH = 'code.filepath';
var ATTR_CODE_FUNCTION = 'code.function';
var ATTR_CODE_LINENO = 'code.lineno';
var ATTR_CODE_NAMESPACE = 'code.namespace';
var ATTR_CODE_STACKTRACE = 'code.stacktrace';
var ATTR_CONTAINER_COMMAND = 'container.command';
var ATTR_CONTAINER_COMMAND_ARGS = 'container.command_args';
var ATTR_CONTAINER_COMMAND_LINE = 'container.command_line';
var ATTR_CONTAINER_CPU_STATE = 'container.cpu.state';
var CONTAINER_CPU_STATE_VALUE_KERNEL = "kernel";
var CONTAINER_CPU_STATE_VALUE_SYSTEM = "system";
var CONTAINER_CPU_STATE_VALUE_USER = "user";
var ATTR_CONTAINER_ID = 'container.id';
var ATTR_CONTAINER_IMAGE_ID = 'container.image.id';
var ATTR_CONTAINER_IMAGE_NAME = 'container.image.name';
var ATTR_CONTAINER_IMAGE_REPO_DIGESTS = 'container.image.repo_digests';
var ATTR_CONTAINER_IMAGE_TAGS = 'container.image.tags';
var ATTR_CONTAINER_LABEL = function(key) {
    return "container.label." + key;
};
var ATTR_CONTAINER_LABELS = function(key) {
    return "container.labels." + key;
};
var ATTR_CONTAINER_NAME = 'container.name';
var ATTR_CONTAINER_RUNTIME = 'container.runtime';
var ATTR_CPU_MODE = 'cpu.mode';
var CPU_MODE_VALUE_IDLE = "idle";
var CPU_MODE_VALUE_INTERRUPT = "interrupt";
var CPU_MODE_VALUE_IOWAIT = "iowait";
var CPU_MODE_VALUE_KERNEL = "kernel";
var CPU_MODE_VALUE_NICE = "nice";
var CPU_MODE_VALUE_STEAL = "steal";
var CPU_MODE_VALUE_SYSTEM = "system";
var CPU_MODE_VALUE_USER = "user";
var ATTR_DB_CASSANDRA_CONSISTENCY_LEVEL = 'db.cassandra.consistency_level';
var DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ALL = "all";
var DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ANY = "any";
var DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_EACH_QUORUM = "each_quorum";
var DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_ONE = "local_one";
var DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_QUORUM = "local_quorum";
var DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_LOCAL_SERIAL = "local_serial";
var DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_ONE = "one";
var DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_QUORUM = "quorum";
var DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_SERIAL = "serial";
var DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_THREE = "three";
var DB_CASSANDRA_CONSISTENCY_LEVEL_VALUE_TWO = "two";
var ATTR_DB_CASSANDRA_COORDINATOR_DC = 'db.cassandra.coordinator.dc';
var ATTR_DB_CASSANDRA_COORDINATOR_ID = 'db.cassandra.coordinator.id';
var ATTR_DB_CASSANDRA_IDEMPOTENCE = 'db.cassandra.idempotence';
var ATTR_DB_CASSANDRA_PAGE_SIZE = 'db.cassandra.page_size';
var ATTR_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT = 'db.cassandra.speculative_execution_count';
var ATTR_DB_CASSANDRA_TABLE = 'db.cassandra.table';
var ATTR_DB_CLIENT_CONNECTION_POOL_NAME = 'db.client.connection.pool.name';
var ATTR_DB_CLIENT_CONNECTION_STATE = 'db.client.connection.state';
var DB_CLIENT_CONNECTION_STATE_VALUE_IDLE = "idle";
var DB_CLIENT_CONNECTION_STATE_VALUE_USED = "used";
var ATTR_DB_CLIENT_CONNECTIONS_POOL_NAME = 'db.client.connections.pool.name';
var ATTR_DB_CLIENT_CONNECTIONS_STATE = 'db.client.connections.state';
var DB_CLIENT_CONNECTIONS_STATE_VALUE_IDLE = "idle";
var DB_CLIENT_CONNECTIONS_STATE_VALUE_USED = "used";
var ATTR_DB_COLLECTION_NAME = 'db.collection.name';
var ATTR_DB_CONNECTION_STRING = 'db.connection_string';
var ATTR_DB_COSMOSDB_CLIENT_ID = 'db.cosmosdb.client_id';
var ATTR_DB_COSMOSDB_CONNECTION_MODE = 'db.cosmosdb.connection_mode';
var DB_COSMOSDB_CONNECTION_MODE_VALUE_DIRECT = "direct";
var DB_COSMOSDB_CONNECTION_MODE_VALUE_GATEWAY = "gateway";
var ATTR_DB_COSMOSDB_CONTAINER = 'db.cosmosdb.container';
var ATTR_DB_COSMOSDB_OPERATION_TYPE = 'db.cosmosdb.operation_type';
var DB_COSMOSDB_OPERATION_TYPE_VALUE_BATCH = "Batch";
var DB_COSMOSDB_OPERATION_TYPE_VALUE_CREATE = "Create";
var DB_COSMOSDB_OPERATION_TYPE_VALUE_DELETE = "Delete";
var DB_COSMOSDB_OPERATION_TYPE_VALUE_EXECUTE = "Execute";
var DB_COSMOSDB_OPERATION_TYPE_VALUE_EXECUTE_JAVASCRIPT = "ExecuteJavaScript";
var DB_COSMOSDB_OPERATION_TYPE_VALUE_HEAD = "Head";
var DB_COSMOSDB_OPERATION_TYPE_VALUE_HEAD_FEED = "HeadFeed";
var DB_COSMOSDB_OPERATION_TYPE_VALUE_INVALID = "Invalid";
var DB_COSMOSDB_OPERATION_TYPE_VALUE_PATCH = "Patch";
var DB_COSMOSDB_OPERATION_TYPE_VALUE_QUERY = "Query";
var DB_COSMOSDB_OPERATION_TYPE_VALUE_QUERY_PLAN = "QueryPlan";
var DB_COSMOSDB_OPERATION_TYPE_VALUE_READ = "Read";
var DB_COSMOSDB_OPERATION_TYPE_VALUE_READ_FEED = "ReadFeed";
var DB_COSMOSDB_OPERATION_TYPE_VALUE_REPLACE = "Replace";
var DB_COSMOSDB_OPERATION_TYPE_VALUE_UPSERT = "Upsert";
var ATTR_DB_COSMOSDB_REQUEST_CHARGE = 'db.cosmosdb.request_charge';
var ATTR_DB_COSMOSDB_REQUEST_CONTENT_LENGTH = 'db.cosmosdb.request_content_length';
var ATTR_DB_COSMOSDB_STATUS_CODE = 'db.cosmosdb.status_code';
var ATTR_DB_COSMOSDB_SUB_STATUS_CODE = 'db.cosmosdb.sub_status_code';
var ATTR_DB_ELASTICSEARCH_CLUSTER_NAME = 'db.elasticsearch.cluster.name';
var ATTR_DB_ELASTICSEARCH_NODE_NAME = 'db.elasticsearch.node.name';
var ATTR_DB_ELASTICSEARCH_PATH_PARTS = function(key) {
    return "db.elasticsearch.path_parts." + key;
};
var ATTR_DB_INSTANCE_ID = 'db.instance.id';
var ATTR_DB_JDBC_DRIVER_CLASSNAME = 'db.jdbc.driver_classname';
var ATTR_DB_MONGODB_COLLECTION = 'db.mongodb.collection';
var ATTR_DB_MSSQL_INSTANCE_NAME = 'db.mssql.instance_name';
var ATTR_DB_NAME = 'db.name';
var ATTR_DB_NAMESPACE = 'db.namespace';
var ATTR_DB_OPERATION = 'db.operation';
var ATTR_DB_OPERATION_BATCH_SIZE = 'db.operation.batch.size';
var ATTR_DB_OPERATION_NAME = 'db.operation.name';
var ATTR_DB_QUERY_PARAMETER = function(key) {
    return "db.query.parameter." + key;
};
var ATTR_DB_QUERY_TEXT = 'db.query.text';
var ATTR_DB_REDIS_DATABASE_INDEX = 'db.redis.database_index';
var ATTR_DB_SQL_TABLE = 'db.sql.table';
var ATTR_DB_STATEMENT = 'db.statement';
var ATTR_DB_SYSTEM = 'db.system';
var DB_SYSTEM_VALUE_ADABAS = "adabas";
var DB_SYSTEM_VALUE_CACHE = "cache";
var DB_SYSTEM_VALUE_CASSANDRA = "cassandra";
var DB_SYSTEM_VALUE_CLICKHOUSE = "clickhouse";
var DB_SYSTEM_VALUE_CLOUDSCAPE = "cloudscape";
var DB_SYSTEM_VALUE_COCKROACHDB = "cockroachdb";
var DB_SYSTEM_VALUE_COLDFUSION = "coldfusion";
var DB_SYSTEM_VALUE_COSMOSDB = "cosmosdb";
var DB_SYSTEM_VALUE_COUCHBASE = "couchbase";
var DB_SYSTEM_VALUE_COUCHDB = "couchdb";
var DB_SYSTEM_VALUE_DB2 = "db2";
var DB_SYSTEM_VALUE_DERBY = "derby";
var DB_SYSTEM_VALUE_DYNAMODB = "dynamodb";
var DB_SYSTEM_VALUE_EDB = "edb";
var DB_SYSTEM_VALUE_ELASTICSEARCH = "elasticsearch";
var DB_SYSTEM_VALUE_FILEMAKER = "filemaker";
var DB_SYSTEM_VALUE_FIREBIRD = "firebird";
var DB_SYSTEM_VALUE_FIRSTSQL = "firstsql";
var DB_SYSTEM_VALUE_GEODE = "geode";
var DB_SYSTEM_VALUE_H2 = "h2";
var DB_SYSTEM_VALUE_HANADB = "hanadb";
var DB_SYSTEM_VALUE_HBASE = "hbase";
var DB_SYSTEM_VALUE_HIVE = "hive";
var DB_SYSTEM_VALUE_HSQLDB = "hsqldb";
var DB_SYSTEM_VALUE_INFLUXDB = "influxdb";
var DB_SYSTEM_VALUE_INFORMIX = "informix";
var DB_SYSTEM_VALUE_INGRES = "ingres";
var DB_SYSTEM_VALUE_INSTANTDB = "instantdb";
var DB_SYSTEM_VALUE_INTERBASE = "interbase";
var DB_SYSTEM_VALUE_INTERSYSTEMS_CACHE = "intersystems_cache";
var DB_SYSTEM_VALUE_MARIADB = "mariadb";
var DB_SYSTEM_VALUE_MAXDB = "maxdb";
var DB_SYSTEM_VALUE_MEMCACHED = "memcached";
var DB_SYSTEM_VALUE_MONGODB = "mongodb";
var DB_SYSTEM_VALUE_MSSQL = "mssql";
var DB_SYSTEM_VALUE_MSSQLCOMPACT = "mssqlcompact";
var DB_SYSTEM_VALUE_MYSQL = "mysql";
var DB_SYSTEM_VALUE_NEO4J = "neo4j";
var DB_SYSTEM_VALUE_NETEZZA = "netezza";
var DB_SYSTEM_VALUE_OPENSEARCH = "opensearch";
var DB_SYSTEM_VALUE_ORACLE = "oracle";
var DB_SYSTEM_VALUE_OTHER_SQL = "other_sql";
var DB_SYSTEM_VALUE_PERVASIVE = "pervasive";
var DB_SYSTEM_VALUE_POINTBASE = "pointbase";
var DB_SYSTEM_VALUE_POSTGRESQL = "postgresql";
var DB_SYSTEM_VALUE_PROGRESS = "progress";
var DB_SYSTEM_VALUE_REDIS = "redis";
var DB_SYSTEM_VALUE_REDSHIFT = "redshift";
var DB_SYSTEM_VALUE_SPANNER = "spanner";
var DB_SYSTEM_VALUE_SQLITE = "sqlite";
var DB_SYSTEM_VALUE_SYBASE = "sybase";
var DB_SYSTEM_VALUE_TERADATA = "teradata";
var DB_SYSTEM_VALUE_TRINO = "trino";
var DB_SYSTEM_VALUE_VERTICA = "vertica";
var ATTR_DB_USER = 'db.user';
var ATTR_DEPLOYMENT_ENVIRONMENT = 'deployment.environment';
var ATTR_DEPLOYMENT_ENVIRONMENT_NAME = 'deployment.environment.name';
var ATTR_DEPLOYMENT_ID = 'deployment.id';
var ATTR_DEPLOYMENT_NAME = 'deployment.name';
var ATTR_DEPLOYMENT_STATUS = 'deployment.status';
var DEPLOYMENT_STATUS_VALUE_FAILED = "failed";
var DEPLOYMENT_STATUS_VALUE_SUCCEEDED = "succeeded";
var ATTR_DESTINATION_ADDRESS = 'destination.address';
var ATTR_DESTINATION_PORT = 'destination.port';
var ATTR_DEVICE_ID = 'device.id';
var ATTR_DEVICE_MANUFACTURER = 'device.manufacturer';
var ATTR_DEVICE_MODEL_IDENTIFIER = 'device.model.identifier';
var ATTR_DEVICE_MODEL_NAME = 'device.model.name';
var ATTR_DISK_IO_DIRECTION = 'disk.io.direction';
var DISK_IO_DIRECTION_VALUE_READ = "read";
var DISK_IO_DIRECTION_VALUE_WRITE = "write";
var ATTR_DNS_QUESTION_NAME = 'dns.question.name';
var ATTR_ENDUSER_ID = 'enduser.id';
var ATTR_ENDUSER_ROLE = 'enduser.role';
var ATTR_ENDUSER_SCOPE = 'enduser.scope';
var ATTR_EVENT_NAME = 'event.name';
var ATTR_FAAS_COLDSTART = 'faas.coldstart';
var ATTR_FAAS_CRON = 'faas.cron';
var ATTR_FAAS_DOCUMENT_COLLECTION = 'faas.document.collection';
var ATTR_FAAS_DOCUMENT_NAME = 'faas.document.name';
var ATTR_FAAS_DOCUMENT_OPERATION = 'faas.document.operation';
var FAAS_DOCUMENT_OPERATION_VALUE_DELETE = "delete";
var FAAS_DOCUMENT_OPERATION_VALUE_EDIT = "edit";
var FAAS_DOCUMENT_OPERATION_VALUE_INSERT = "insert";
var ATTR_FAAS_DOCUMENT_TIME = 'faas.document.time';
var ATTR_FAAS_INSTANCE = 'faas.instance';
var ATTR_FAAS_INVOCATION_ID = 'faas.invocation_id';
var ATTR_FAAS_INVOKED_NAME = 'faas.invoked_name';
var ATTR_FAAS_INVOKED_PROVIDER = 'faas.invoked_provider';
var FAAS_INVOKED_PROVIDER_VALUE_ALIBABA_CLOUD = "alibaba_cloud";
var FAAS_INVOKED_PROVIDER_VALUE_AWS = "aws";
var FAAS_INVOKED_PROVIDER_VALUE_AZURE = "azure";
var FAAS_INVOKED_PROVIDER_VALUE_GCP = "gcp";
var FAAS_INVOKED_PROVIDER_VALUE_TENCENT_CLOUD = "tencent_cloud";
var ATTR_FAAS_INVOKED_REGION = 'faas.invoked_region';
var ATTR_FAAS_MAX_MEMORY = 'faas.max_memory';
var ATTR_FAAS_NAME = 'faas.name';
var ATTR_FAAS_TIME = 'faas.time';
var ATTR_FAAS_TRIGGER = 'faas.trigger';
var FAAS_TRIGGER_VALUE_DATASOURCE = "datasource";
var FAAS_TRIGGER_VALUE_HTTP = "http";
var FAAS_TRIGGER_VALUE_OTHER = "other";
var FAAS_TRIGGER_VALUE_PUBSUB = "pubsub";
var FAAS_TRIGGER_VALUE_TIMER = "timer";
var ATTR_FAAS_VERSION = 'faas.version';
var ATTR_FEATURE_FLAG_KEY = 'feature_flag.key';
var ATTR_FEATURE_FLAG_PROVIDER_NAME = 'feature_flag.provider_name';
var ATTR_FEATURE_FLAG_VARIANT = 'feature_flag.variant';
var ATTR_FILE_DIRECTORY = 'file.directory';
var ATTR_FILE_EXTENSION = 'file.extension';
var ATTR_FILE_NAME = 'file.name';
var ATTR_FILE_PATH = 'file.path';
var ATTR_FILE_SIZE = 'file.size';
var ATTR_GCP_CLIENT_SERVICE = 'gcp.client.service';
var ATTR_GCP_CLOUD_RUN_JOB_EXECUTION = 'gcp.cloud_run.job.execution';
var ATTR_GCP_CLOUD_RUN_JOB_TASK_INDEX = 'gcp.cloud_run.job.task_index';
var ATTR_GCP_GCE_INSTANCE_HOSTNAME = 'gcp.gce.instance.hostname';
var ATTR_GCP_GCE_INSTANCE_NAME = 'gcp.gce.instance.name';
var ATTR_GEN_AI_COMPLETION = 'gen_ai.completion';
var ATTR_GEN_AI_OPERATION_NAME = 'gen_ai.operation.name';
var GEN_AI_OPERATION_NAME_VALUE_CHAT = "chat";
var GEN_AI_OPERATION_NAME_VALUE_TEXT_COMPLETION = "text_completion";
var ATTR_GEN_AI_PROMPT = 'gen_ai.prompt';
var ATTR_GEN_AI_REQUEST_FREQUENCY_PENALTY = 'gen_ai.request.frequency_penalty';
var ATTR_GEN_AI_REQUEST_MAX_TOKENS = 'gen_ai.request.max_tokens';
var ATTR_GEN_AI_REQUEST_MODEL = 'gen_ai.request.model';
var ATTR_GEN_AI_REQUEST_PRESENCE_PENALTY = 'gen_ai.request.presence_penalty';
var ATTR_GEN_AI_REQUEST_STOP_SEQUENCES = 'gen_ai.request.stop_sequences';
var ATTR_GEN_AI_REQUEST_TEMPERATURE = 'gen_ai.request.temperature';
var ATTR_GEN_AI_REQUEST_TOP_K = 'gen_ai.request.top_k';
var ATTR_GEN_AI_REQUEST_TOP_P = 'gen_ai.request.top_p';
var ATTR_GEN_AI_RESPONSE_FINISH_REASONS = 'gen_ai.response.finish_reasons';
var ATTR_GEN_AI_RESPONSE_ID = 'gen_ai.response.id';
var ATTR_GEN_AI_RESPONSE_MODEL = 'gen_ai.response.model';
var ATTR_GEN_AI_SYSTEM = 'gen_ai.system';
var GEN_AI_SYSTEM_VALUE_ANTHROPIC = "anthropic";
var GEN_AI_SYSTEM_VALUE_COHERE = "cohere";
var GEN_AI_SYSTEM_VALUE_OPENAI = "openai";
var GEN_AI_SYSTEM_VALUE_VERTEX_AI = "vertex_ai";
var ATTR_GEN_AI_TOKEN_TYPE = 'gen_ai.token.type';
var GEN_AI_TOKEN_TYPE_VALUE_INPUT = "input";
var GEN_AI_TOKEN_TYPE_VALUE_COMPLETION = "output";
var ATTR_GEN_AI_USAGE_COMPLETION_TOKENS = 'gen_ai.usage.completion_tokens';
var ATTR_GEN_AI_USAGE_INPUT_TOKENS = 'gen_ai.usage.input_tokens';
var ATTR_GEN_AI_USAGE_OUTPUT_TOKENS = 'gen_ai.usage.output_tokens';
var ATTR_GEN_AI_USAGE_PROMPT_TOKENS = 'gen_ai.usage.prompt_tokens';
var ATTR_GO_MEMORY_TYPE = 'go.memory.type';
var GO_MEMORY_TYPE_VALUE_OTHER = "other";
var GO_MEMORY_TYPE_VALUE_STACK = "stack";
var ATTR_GRAPHQL_DOCUMENT = 'graphql.document';
var ATTR_GRAPHQL_OPERATION_NAME = 'graphql.operation.name';
var ATTR_GRAPHQL_OPERATION_TYPE = 'graphql.operation.type';
var GRAPHQL_OPERATION_TYPE_VALUE_MUTATION = "mutation";
var GRAPHQL_OPERATION_TYPE_VALUE_QUERY = "query";
var GRAPHQL_OPERATION_TYPE_VALUE_SUBSCRIPTION = "subscription";
var ATTR_HEROKU_APP_ID = 'heroku.app.id';
var ATTR_HEROKU_RELEASE_COMMIT = 'heroku.release.commit';
var ATTR_HEROKU_RELEASE_CREATION_TIMESTAMP = 'heroku.release.creation_timestamp';
var ATTR_HOST_ARCH = 'host.arch';
var HOST_ARCH_VALUE_AMD64 = "amd64";
var HOST_ARCH_VALUE_ARM32 = "arm32";
var HOST_ARCH_VALUE_ARM64 = "arm64";
var HOST_ARCH_VALUE_IA64 = "ia64";
var HOST_ARCH_VALUE_PPC32 = "ppc32";
var HOST_ARCH_VALUE_PPC64 = "ppc64";
var HOST_ARCH_VALUE_S390X = "s390x";
var HOST_ARCH_VALUE_X86 = "x86";
var ATTR_HOST_CPU_CACHE_L2_SIZE = 'host.cpu.cache.l2.size';
var ATTR_HOST_CPU_FAMILY = 'host.cpu.family';
var ATTR_HOST_CPU_MODEL_ID = 'host.cpu.model.id';
var ATTR_HOST_CPU_MODEL_NAME = 'host.cpu.model.name';
var ATTR_HOST_CPU_STEPPING = 'host.cpu.stepping';
var ATTR_HOST_CPU_VENDOR_ID = 'host.cpu.vendor.id';
var ATTR_HOST_ID = 'host.id';
var ATTR_HOST_IMAGE_ID = 'host.image.id';
var ATTR_HOST_IMAGE_NAME = 'host.image.name';
var ATTR_HOST_IMAGE_VERSION = 'host.image.version';
var ATTR_HOST_IP = 'host.ip';
var ATTR_HOST_MAC = 'host.mac';
var ATTR_HOST_NAME = 'host.name';
var ATTR_HOST_TYPE = 'host.type';
var ATTR_HTTP_CLIENT_IP = 'http.client_ip';
var ATTR_HTTP_CONNECTION_STATE = 'http.connection.state';
var HTTP_CONNECTION_STATE_VALUE_ACTIVE = "active";
var HTTP_CONNECTION_STATE_VALUE_IDLE = "idle";
var ATTR_HTTP_FLAVOR = 'http.flavor';
var HTTP_FLAVOR_VALUE_HTTP_1_0 = "1.0";
var HTTP_FLAVOR_VALUE_HTTP_1_1 = "1.1";
var HTTP_FLAVOR_VALUE_HTTP_2_0 = "2.0";
var HTTP_FLAVOR_VALUE_HTTP_3_0 = "3.0";
var HTTP_FLAVOR_VALUE_QUIC = "QUIC";
var HTTP_FLAVOR_VALUE_SPDY = "SPDY";
var ATTR_HTTP_HOST = 'http.host';
var ATTR_HTTP_METHOD = 'http.method';
var ATTR_HTTP_REQUEST_BODY_SIZE = 'http.request.body.size';
var ATTR_HTTP_REQUEST_SIZE = 'http.request.size';
var ATTR_HTTP_REQUEST_CONTENT_LENGTH = 'http.request_content_length';
var ATTR_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED = 'http.request_content_length_uncompressed';
var ATTR_HTTP_RESPONSE_BODY_SIZE = 'http.response.body.size';
var ATTR_HTTP_RESPONSE_SIZE = 'http.response.size';
var ATTR_HTTP_RESPONSE_CONTENT_LENGTH = 'http.response_content_length';
var ATTR_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED = 'http.response_content_length_uncompressed';
var ATTR_HTTP_SCHEME = 'http.scheme';
var ATTR_HTTP_SERVER_NAME = 'http.server_name';
var ATTR_HTTP_STATUS_CODE = 'http.status_code';
var ATTR_HTTP_TARGET = 'http.target';
var ATTR_HTTP_URL = 'http.url';
var ATTR_HTTP_USER_AGENT = 'http.user_agent';
var ATTR_IOS_STATE = 'ios.state';
var IOS_STATE_VALUE_ACTIVE = "active";
var IOS_STATE_VALUE_BACKGROUND = "background";
var IOS_STATE_VALUE_FOREGROUND = "foreground";
var IOS_STATE_VALUE_INACTIVE = "inactive";
var IOS_STATE_VALUE_TERMINATE = "terminate";
var ATTR_JVM_BUFFER_POOL_NAME = 'jvm.buffer.pool.name';
var ATTR_K8S_CLUSTER_NAME = 'k8s.cluster.name';
var ATTR_K8S_CLUSTER_UID = 'k8s.cluster.uid';
var ATTR_K8S_CONTAINER_NAME = 'k8s.container.name';
var ATTR_K8S_CONTAINER_RESTART_COUNT = 'k8s.container.restart_count';
var ATTR_K8S_CONTAINER_STATUS_LAST_TERMINATED_REASON = 'k8s.container.status.last_terminated_reason';
var ATTR_K8S_CRONJOB_NAME = 'k8s.cronjob.name';
var ATTR_K8S_CRONJOB_UID = 'k8s.cronjob.uid';
var ATTR_K8S_DAEMONSET_NAME = 'k8s.daemonset.name';
var ATTR_K8S_DAEMONSET_UID = 'k8s.daemonset.uid';
var ATTR_K8S_DEPLOYMENT_NAME = 'k8s.deployment.name';
var ATTR_K8S_DEPLOYMENT_UID = 'k8s.deployment.uid';
var ATTR_K8S_JOB_NAME = 'k8s.job.name';
var ATTR_K8S_JOB_UID = 'k8s.job.uid';
var ATTR_K8S_NAMESPACE_NAME = 'k8s.namespace.name';
var ATTR_K8S_NODE_NAME = 'k8s.node.name';
var ATTR_K8S_NODE_UID = 'k8s.node.uid';
var ATTR_K8S_POD_ANNOTATION = function(key) {
    return "k8s.pod.annotation." + key;
};
var ATTR_K8S_POD_LABEL = function(key) {
    return "k8s.pod.label." + key;
};
var ATTR_K8S_POD_LABELS = function(key) {
    return "k8s.pod.labels." + key;
};
var ATTR_K8S_POD_NAME = 'k8s.pod.name';
var ATTR_K8S_POD_UID = 'k8s.pod.uid';
var ATTR_K8S_REPLICASET_NAME = 'k8s.replicaset.name';
var ATTR_K8S_REPLICASET_UID = 'k8s.replicaset.uid';
var ATTR_K8S_STATEFULSET_NAME = 'k8s.statefulset.name';
var ATTR_K8S_STATEFULSET_UID = 'k8s.statefulset.uid';
var ATTR_LINUX_MEMORY_SLAB_STATE = 'linux.memory.slab.state';
var LINUX_MEMORY_SLAB_STATE_VALUE_RECLAIMABLE = "reclaimable";
var LINUX_MEMORY_SLAB_STATE_VALUE_UNRECLAIMABLE = "unreclaimable";
var ATTR_LOG_FILE_NAME = 'log.file.name';
var ATTR_LOG_FILE_NAME_RESOLVED = 'log.file.name_resolved';
var ATTR_LOG_FILE_PATH = 'log.file.path';
var ATTR_LOG_FILE_PATH_RESOLVED = 'log.file.path_resolved';
var ATTR_LOG_IOSTREAM = 'log.iostream';
var LOG_IOSTREAM_VALUE_STDERR = "stderr";
var LOG_IOSTREAM_VALUE_STDOUT = "stdout";
var ATTR_LOG_RECORD_ORIGINAL = 'log.record.original';
var ATTR_LOG_RECORD_UID = 'log.record.uid';
var ATTR_MESSAGE_COMPRESSED_SIZE = 'message.compressed_size';
var ATTR_MESSAGE_ID = 'message.id';
var ATTR_MESSAGE_TYPE = 'message.type';
var MESSAGE_TYPE_VALUE_RECEIVED = "RECEIVED";
var MESSAGE_TYPE_VALUE_SENT = "SENT";
var ATTR_MESSAGE_UNCOMPRESSED_SIZE = 'message.uncompressed_size';
var ATTR_MESSAGING_BATCH_MESSAGE_COUNT = 'messaging.batch.message_count';
var ATTR_MESSAGING_CLIENT_ID = 'messaging.client.id';
var ATTR_MESSAGING_CONSUMER_GROUP_NAME = 'messaging.consumer.group.name';
var ATTR_MESSAGING_DESTINATION_ANONYMOUS = 'messaging.destination.anonymous';
var ATTR_MESSAGING_DESTINATION_NAME = 'messaging.destination.name';
var ATTR_MESSAGING_DESTINATION_PARTITION_ID = 'messaging.destination.partition.id';
var ATTR_MESSAGING_DESTINATION_SUBSCRIPTION_NAME = 'messaging.destination.subscription.name';
var ATTR_MESSAGING_DESTINATION_TEMPLATE = 'messaging.destination.template';
var ATTR_MESSAGING_DESTINATION_TEMPORARY = 'messaging.destination.temporary';
var ATTR_MESSAGING_DESTINATION_PUBLISH_ANONYMOUS = 'messaging.destination_publish.anonymous';
var ATTR_MESSAGING_DESTINATION_PUBLISH_NAME = 'messaging.destination_publish.name';
var ATTR_MESSAGING_EVENTHUBS_CONSUMER_GROUP = 'messaging.eventhubs.consumer.group';
var ATTR_MESSAGING_EVENTHUBS_MESSAGE_ENQUEUED_TIME = 'messaging.eventhubs.message.enqueued_time';
var ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_ACK_DEADLINE = 'messaging.gcp_pubsub.message.ack_deadline';
var ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_ACK_ID = 'messaging.gcp_pubsub.message.ack_id';
var ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_DELIVERY_ATTEMPT = 'messaging.gcp_pubsub.message.delivery_attempt';
var ATTR_MESSAGING_GCP_PUBSUB_MESSAGE_ORDERING_KEY = 'messaging.gcp_pubsub.message.ordering_key';
var ATTR_MESSAGING_KAFKA_CONSUMER_GROUP = 'messaging.kafka.consumer.group';
var ATTR_MESSAGING_KAFKA_DESTINATION_PARTITION = 'messaging.kafka.destination.partition';
var ATTR_MESSAGING_KAFKA_MESSAGE_KEY = 'messaging.kafka.message.key';
var ATTR_MESSAGING_KAFKA_MESSAGE_OFFSET = 'messaging.kafka.message.offset';
var ATTR_MESSAGING_KAFKA_MESSAGE_TOMBSTONE = 'messaging.kafka.message.tombstone';
var ATTR_MESSAGING_KAFKA_OFFSET = 'messaging.kafka.offset';
var ATTR_MESSAGING_MESSAGE_BODY_SIZE = 'messaging.message.body.size';
var ATTR_MESSAGING_MESSAGE_CONVERSATION_ID = 'messaging.message.conversation_id';
var ATTR_MESSAGING_MESSAGE_ENVELOPE_SIZE = 'messaging.message.envelope.size';
var ATTR_MESSAGING_MESSAGE_ID = 'messaging.message.id';
var ATTR_MESSAGING_OPERATION = 'messaging.operation';
var ATTR_MESSAGING_OPERATION_NAME = 'messaging.operation.name';
var ATTR_MESSAGING_OPERATION_TYPE = 'messaging.operation.type';
var MESSAGING_OPERATION_TYPE_VALUE_CREATE = "create";
var MESSAGING_OPERATION_TYPE_VALUE_DELIVER = "deliver";
var MESSAGING_OPERATION_TYPE_VALUE_PROCESS = "process";
var MESSAGING_OPERATION_TYPE_VALUE_PUBLISH = "publish";
var MESSAGING_OPERATION_TYPE_VALUE_RECEIVE = "receive";
var MESSAGING_OPERATION_TYPE_VALUE_SETTLE = "settle";
var ATTR_MESSAGING_RABBITMQ_DESTINATION_ROUTING_KEY = 'messaging.rabbitmq.destination.routing_key';
var ATTR_MESSAGING_RABBITMQ_MESSAGE_DELIVERY_TAG = 'messaging.rabbitmq.message.delivery_tag';
var ATTR_MESSAGING_ROCKETMQ_CLIENT_GROUP = 'messaging.rocketmq.client_group';
var ATTR_MESSAGING_ROCKETMQ_CONSUMPTION_MODEL = 'messaging.rocketmq.consumption_model';
var MESSAGING_ROCKETMQ_CONSUMPTION_MODEL_VALUE_BROADCASTING = "broadcasting";
var MESSAGING_ROCKETMQ_CONSUMPTION_MODEL_VALUE_CLUSTERING = "clustering";
var ATTR_MESSAGING_ROCKETMQ_MESSAGE_DELAY_TIME_LEVEL = 'messaging.rocketmq.message.delay_time_level';
var ATTR_MESSAGING_ROCKETMQ_MESSAGE_DELIVERY_TIMESTAMP = 'messaging.rocketmq.message.delivery_timestamp';
var ATTR_MESSAGING_ROCKETMQ_MESSAGE_GROUP = 'messaging.rocketmq.message.group';
var ATTR_MESSAGING_ROCKETMQ_MESSAGE_KEYS = 'messaging.rocketmq.message.keys';
var ATTR_MESSAGING_ROCKETMQ_MESSAGE_TAG = 'messaging.rocketmq.message.tag';
var ATTR_MESSAGING_ROCKETMQ_MESSAGE_TYPE = 'messaging.rocketmq.message.type';
var MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_DELAY = "delay";
var MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_FIFO = "fifo";
var MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_NORMAL = "normal";
var MESSAGING_ROCKETMQ_MESSAGE_TYPE_VALUE_TRANSACTION = "transaction";
var ATTR_MESSAGING_ROCKETMQ_NAMESPACE = 'messaging.rocketmq.namespace';
var ATTR_MESSAGING_SERVICEBUS_DESTINATION_SUBSCRIPTION_NAME = 'messaging.servicebus.destination.subscription_name';
var ATTR_MESSAGING_SERVICEBUS_DISPOSITION_STATUS = 'messaging.servicebus.disposition_status';
var MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_ABANDON = "abandon";
var MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_COMPLETE = "complete";
var MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_DEAD_LETTER = "dead_letter";
var MESSAGING_SERVICEBUS_DISPOSITION_STATUS_VALUE_DEFER = "defer";
var ATTR_MESSAGING_SERVICEBUS_MESSAGE_DELIVERY_COUNT = 'messaging.servicebus.message.delivery_count';
var ATTR_MESSAGING_SERVICEBUS_MESSAGE_ENQUEUED_TIME = 'messaging.servicebus.message.enqueued_time';
var ATTR_MESSAGING_SYSTEM = 'messaging.system';
var MESSAGING_SYSTEM_VALUE_ACTIVEMQ = "activemq";
var MESSAGING_SYSTEM_VALUE_AWS_SQS = "aws_sqs";
var MESSAGING_SYSTEM_VALUE_EVENTGRID = "eventgrid";
var MESSAGING_SYSTEM_VALUE_EVENTHUBS = "eventhubs";
var MESSAGING_SYSTEM_VALUE_GCP_PUBSUB = "gcp_pubsub";
var MESSAGING_SYSTEM_VALUE_JMS = "jms";
var MESSAGING_SYSTEM_VALUE_KAFKA = "kafka";
var MESSAGING_SYSTEM_VALUE_PULSAR = "pulsar";
var MESSAGING_SYSTEM_VALUE_RABBITMQ = "rabbitmq";
var MESSAGING_SYSTEM_VALUE_ROCKETMQ = "rocketmq";
var MESSAGING_SYSTEM_VALUE_SERVICEBUS = "servicebus";
var ATTR_NET_HOST_IP = 'net.host.ip';
var ATTR_NET_HOST_NAME = 'net.host.name';
var ATTR_NET_HOST_PORT = 'net.host.port';
var ATTR_NET_PEER_IP = 'net.peer.ip';
var ATTR_NET_PEER_NAME = 'net.peer.name';
var ATTR_NET_PEER_PORT = 'net.peer.port';
var ATTR_NET_PROTOCOL_NAME = 'net.protocol.name';
var ATTR_NET_PROTOCOL_VERSION = 'net.protocol.version';
var ATTR_NET_SOCK_FAMILY = 'net.sock.family';
var NET_SOCK_FAMILY_VALUE_INET = "inet";
var NET_SOCK_FAMILY_VALUE_INET6 = "inet6";
var NET_SOCK_FAMILY_VALUE_UNIX = "unix";
var ATTR_NET_SOCK_HOST_ADDR = 'net.sock.host.addr';
var ATTR_NET_SOCK_HOST_PORT = 'net.sock.host.port';
var ATTR_NET_SOCK_PEER_ADDR = 'net.sock.peer.addr';
var ATTR_NET_SOCK_PEER_NAME = 'net.sock.peer.name';
var ATTR_NET_SOCK_PEER_PORT = 'net.sock.peer.port';
var ATTR_NET_TRANSPORT = 'net.transport';
var NET_TRANSPORT_VALUE_INPROC = "inproc";
var NET_TRANSPORT_VALUE_IP_TCP = "ip_tcp";
var NET_TRANSPORT_VALUE_IP_UDP = "ip_udp";
var NET_TRANSPORT_VALUE_OTHER = "other";
var NET_TRANSPORT_VALUE_PIPE = "pipe";
var ATTR_NETWORK_CARRIER_ICC = 'network.carrier.icc';
var ATTR_NETWORK_CARRIER_MCC = 'network.carrier.mcc';
var ATTR_NETWORK_CARRIER_MNC = 'network.carrier.mnc';
var ATTR_NETWORK_CARRIER_NAME = 'network.carrier.name';
var ATTR_NETWORK_CONNECTION_SUBTYPE = 'network.connection.subtype';
var NETWORK_CONNECTION_SUBTYPE_VALUE_CDMA = "cdma";
var NETWORK_CONNECTION_SUBTYPE_VALUE_CDMA2000_1XRTT = "cdma2000_1xrtt";
var NETWORK_CONNECTION_SUBTYPE_VALUE_EDGE = "edge";
var NETWORK_CONNECTION_SUBTYPE_VALUE_EHRPD = "ehrpd";
var NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_0 = "evdo_0";
var NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_A = "evdo_a";
var NETWORK_CONNECTION_SUBTYPE_VALUE_EVDO_B = "evdo_b";
var NETWORK_CONNECTION_SUBTYPE_VALUE_GPRS = "gprs";
var NETWORK_CONNECTION_SUBTYPE_VALUE_GSM = "gsm";
var NETWORK_CONNECTION_SUBTYPE_VALUE_HSDPA = "hsdpa";
var NETWORK_CONNECTION_SUBTYPE_VALUE_HSPA = "hspa";
var NETWORK_CONNECTION_SUBTYPE_VALUE_HSPAP = "hspap";
var NETWORK_CONNECTION_SUBTYPE_VALUE_HSUPA = "hsupa";
var NETWORK_CONNECTION_SUBTYPE_VALUE_IDEN = "iden";
var NETWORK_CONNECTION_SUBTYPE_VALUE_IWLAN = "iwlan";
var NETWORK_CONNECTION_SUBTYPE_VALUE_LTE = "lte";
var NETWORK_CONNECTION_SUBTYPE_VALUE_LTE_CA = "lte_ca";
var NETWORK_CONNECTION_SUBTYPE_VALUE_NR = "nr";
var NETWORK_CONNECTION_SUBTYPE_VALUE_NRNSA = "nrnsa";
var NETWORK_CONNECTION_SUBTYPE_VALUE_TD_SCDMA = "td_scdma";
var NETWORK_CONNECTION_SUBTYPE_VALUE_UMTS = "umts";
var ATTR_NETWORK_CONNECTION_TYPE = 'network.connection.type';
var NETWORK_CONNECTION_TYPE_VALUE_CELL = "cell";
var NETWORK_CONNECTION_TYPE_VALUE_UNAVAILABLE = "unavailable";
var NETWORK_CONNECTION_TYPE_VALUE_UNKNOWN = "unknown";
var NETWORK_CONNECTION_TYPE_VALUE_WIFI = "wifi";
var NETWORK_CONNECTION_TYPE_VALUE_WIRED = "wired";
var ATTR_NETWORK_IO_DIRECTION = 'network.io.direction';
var NETWORK_IO_DIRECTION_VALUE_RECEIVE = "receive";
var NETWORK_IO_DIRECTION_VALUE_TRANSMIT = "transmit";
var ATTR_OCI_MANIFEST_DIGEST = 'oci.manifest.digest';
var ATTR_OPENTRACING_REF_TYPE = 'opentracing.ref_type';
var OPENTRACING_REF_TYPE_VALUE_CHILD_OF = "child_of";
var OPENTRACING_REF_TYPE_VALUE_FOLLOWS_FROM = "follows_from";
var ATTR_OS_BUILD_ID = 'os.build_id';
var ATTR_OS_DESCRIPTION = 'os.description';
var ATTR_OS_NAME = 'os.name';
var ATTR_OS_TYPE = 'os.type';
var OS_TYPE_VALUE_AIX = "aix";
var OS_TYPE_VALUE_DARWIN = "darwin";
var OS_TYPE_VALUE_DRAGONFLYBSD = "dragonflybsd";
var OS_TYPE_VALUE_FREEBSD = "freebsd";
var OS_TYPE_VALUE_HPUX = "hpux";
var OS_TYPE_VALUE_LINUX = "linux";
var OS_TYPE_VALUE_NETBSD = "netbsd";
var OS_TYPE_VALUE_OPENBSD = "openbsd";
var OS_TYPE_VALUE_SOLARIS = "solaris";
var OS_TYPE_VALUE_WINDOWS = "windows";
var OS_TYPE_VALUE_Z_OS = "z_os";
var ATTR_OS_VERSION = 'os.version';
var ATTR_OTEL_LIBRARY_NAME = 'otel.library.name';
var ATTR_OTEL_LIBRARY_VERSION = 'otel.library.version';
var ATTR_PEER_SERVICE = 'peer.service';
var ATTR_POOL_NAME = 'pool.name';
var ATTR_PROCESS_COMMAND = 'process.command';
var ATTR_PROCESS_COMMAND_ARGS = 'process.command_args';
var ATTR_PROCESS_COMMAND_LINE = 'process.command_line';
var ATTR_PROCESS_CONTEXT_SWITCH_TYPE = 'process.context_switch_type';
var PROCESS_CONTEXT_SWITCH_TYPE_VALUE_INVOLUNTARY = "involuntary";
var PROCESS_CONTEXT_SWITCH_TYPE_VALUE_VOLUNTARY = "voluntary";
var ATTR_PROCESS_CPU_STATE = 'process.cpu.state';
var PROCESS_CPU_STATE_VALUE_SYSTEM = "system";
var PROCESS_CPU_STATE_VALUE_USER = "user";
var PROCESS_CPU_STATE_VALUE_WAIT = "wait";
var ATTR_PROCESS_CREATION_TIME = 'process.creation.time';
var ATTR_PROCESS_EXECUTABLE_NAME = 'process.executable.name';
var ATTR_PROCESS_EXECUTABLE_PATH = 'process.executable.path';
var ATTR_PROCESS_EXIT_CODE = 'process.exit.code';
var ATTR_PROCESS_EXIT_TIME = 'process.exit.time';
var ATTR_PROCESS_GROUP_LEADER_PID = 'process.group_leader.pid';
var ATTR_PROCESS_INTERACTIVE = 'process.interactive';
var ATTR_PROCESS_OWNER = 'process.owner';
var ATTR_PROCESS_PAGING_FAULT_TYPE = 'process.paging.fault_type';
var PROCESS_PAGING_FAULT_TYPE_VALUE_MAJOR = "major";
var PROCESS_PAGING_FAULT_TYPE_VALUE_MINOR = "minor";
var ATTR_PROCESS_PARENT_PID = 'process.parent_pid';
var ATTR_PROCESS_PID = 'process.pid';
var ATTR_PROCESS_REAL_USER_ID = 'process.real_user.id';
var ATTR_PROCESS_REAL_USER_NAME = 'process.real_user.name';
var ATTR_PROCESS_RUNTIME_DESCRIPTION = 'process.runtime.description';
var ATTR_PROCESS_RUNTIME_NAME = 'process.runtime.name';
var ATTR_PROCESS_RUNTIME_VERSION = 'process.runtime.version';
var ATTR_PROCESS_SAVED_USER_ID = 'process.saved_user.id';
var ATTR_PROCESS_SAVED_USER_NAME = 'process.saved_user.name';
var ATTR_PROCESS_SESSION_LEADER_PID = 'process.session_leader.pid';
var ATTR_PROCESS_USER_ID = 'process.user.id';
var ATTR_PROCESS_USER_NAME = 'process.user.name';
var ATTR_PROCESS_VPID = 'process.vpid';
var ATTR_RPC_CONNECT_RPC_ERROR_CODE = 'rpc.connect_rpc.error_code';
var RPC_CONNECT_RPC_ERROR_CODE_VALUE_ABORTED = "aborted";
var RPC_CONNECT_RPC_ERROR_CODE_VALUE_ALREADY_EXISTS = "already_exists";
var RPC_CONNECT_RPC_ERROR_CODE_VALUE_CANCELLED = "cancelled";
var RPC_CONNECT_RPC_ERROR_CODE_VALUE_DATA_LOSS = "data_loss";
var RPC_CONNECT_RPC_ERROR_CODE_VALUE_DEADLINE_EXCEEDED = "deadline_exceeded";
var RPC_CONNECT_RPC_ERROR_CODE_VALUE_FAILED_PRECONDITION = "failed_precondition";
var RPC_CONNECT_RPC_ERROR_CODE_VALUE_INTERNAL = "internal";
var RPC_CONNECT_RPC_ERROR_CODE_VALUE_INVALID_ARGUMENT = "invalid_argument";
var RPC_CONNECT_RPC_ERROR_CODE_VALUE_NOT_FOUND = "not_found";
var RPC_CONNECT_RPC_ERROR_CODE_VALUE_OUT_OF_RANGE = "out_of_range";
var RPC_CONNECT_RPC_ERROR_CODE_VALUE_PERMISSION_DENIED = "permission_denied";
var RPC_CONNECT_RPC_ERROR_CODE_VALUE_RESOURCE_EXHAUSTED = "resource_exhausted";
var RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNAUTHENTICATED = "unauthenticated";
var RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNAVAILABLE = "unavailable";
var RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNIMPLEMENTED = "unimplemented";
var RPC_CONNECT_RPC_ERROR_CODE_VALUE_UNKNOWN = "unknown";
var ATTR_RPC_CONNECT_RPC_REQUEST_METADATA = function(key) {
    return "rpc.connect_rpc.request.metadata." + key;
};
var ATTR_RPC_CONNECT_RPC_RESPONSE_METADATA = function(key) {
    return "rpc.connect_rpc.response.metadata." + key;
};
var ATTR_RPC_GRPC_REQUEST_METADATA = function(key) {
    return "rpc.grpc.request.metadata." + key;
};
var ATTR_RPC_GRPC_RESPONSE_METADATA = function(key) {
    return "rpc.grpc.response.metadata." + key;
};
var ATTR_RPC_GRPC_STATUS_CODE = 'rpc.grpc.status_code';
var RPC_GRPC_STATUS_CODE_VALUE_OK = 0;
var RPC_GRPC_STATUS_CODE_VALUE_CANCELLED = 1;
var RPC_GRPC_STATUS_CODE_VALUE_UNKNOWN = 2;
var RPC_GRPC_STATUS_CODE_VALUE_INVALID_ARGUMENT = 3;
var RPC_GRPC_STATUS_CODE_VALUE_DEADLINE_EXCEEDED = 4;
var RPC_GRPC_STATUS_CODE_VALUE_NOT_FOUND = 5;
var RPC_GRPC_STATUS_CODE_VALUE_ALREADY_EXISTS = 6;
var RPC_GRPC_STATUS_CODE_VALUE_PERMISSION_DENIED = 7;
var RPC_GRPC_STATUS_CODE_VALUE_RESOURCE_EXHAUSTED = 8;
var RPC_GRPC_STATUS_CODE_VALUE_FAILED_PRECONDITION = 9;
var RPC_GRPC_STATUS_CODE_VALUE_ABORTED = 10;
var RPC_GRPC_STATUS_CODE_VALUE_OUT_OF_RANGE = 11;
var RPC_GRPC_STATUS_CODE_VALUE_UNIMPLEMENTED = 12;
var RPC_GRPC_STATUS_CODE_VALUE_INTERNAL = 13;
var RPC_GRPC_STATUS_CODE_VALUE_UNAVAILABLE = 14;
var RPC_GRPC_STATUS_CODE_VALUE_DATA_LOSS = 15;
var RPC_GRPC_STATUS_CODE_VALUE_UNAUTHENTICATED = 16;
var ATTR_RPC_JSONRPC_ERROR_CODE = 'rpc.jsonrpc.error_code';
var ATTR_RPC_JSONRPC_ERROR_MESSAGE = 'rpc.jsonrpc.error_message';
var ATTR_RPC_JSONRPC_REQUEST_ID = 'rpc.jsonrpc.request_id';
var ATTR_RPC_JSONRPC_VERSION = 'rpc.jsonrpc.version';
var ATTR_RPC_MESSAGE_COMPRESSED_SIZE = 'rpc.message.compressed_size';
var ATTR_RPC_MESSAGE_ID = 'rpc.message.id';
var ATTR_RPC_MESSAGE_TYPE = 'rpc.message.type';
var RPC_MESSAGE_TYPE_VALUE_RECEIVED = "RECEIVED";
var RPC_MESSAGE_TYPE_VALUE_SENT = "SENT";
var ATTR_RPC_MESSAGE_UNCOMPRESSED_SIZE = 'rpc.message.uncompressed_size';
var ATTR_RPC_METHOD = 'rpc.method';
var ATTR_RPC_SERVICE = 'rpc.service';
var ATTR_RPC_SYSTEM = 'rpc.system';
var RPC_SYSTEM_VALUE_APACHE_DUBBO = "apache_dubbo";
var RPC_SYSTEM_VALUE_CONNECT_RPC = "connect_rpc";
var RPC_SYSTEM_VALUE_DOTNET_WCF = "dotnet_wcf";
var RPC_SYSTEM_VALUE_GRPC = "grpc";
var RPC_SYSTEM_VALUE_JAVA_RMI = "java_rmi";
var ATTR_SERVICE_INSTANCE_ID = 'service.instance.id';
var ATTR_SERVICE_NAMESPACE = 'service.namespace';
var ATTR_SESSION_ID = 'session.id';
var ATTR_SESSION_PREVIOUS_ID = 'session.previous_id';
var ATTR_SOURCE_ADDRESS = 'source.address';
var ATTR_SOURCE_PORT = 'source.port';
var ATTR_STATE = 'state';
var STATE_VALUE_IDLE = "idle";
var STATE_VALUE_USED = "used";
var ATTR_SYSTEM_CPU_LOGICAL_NUMBER = 'system.cpu.logical_number';
var ATTR_SYSTEM_CPU_STATE = 'system.cpu.state';
var SYSTEM_CPU_STATE_VALUE_IDLE = "idle";
var SYSTEM_CPU_STATE_VALUE_INTERRUPT = "interrupt";
var SYSTEM_CPU_STATE_VALUE_IOWAIT = "iowait";
var SYSTEM_CPU_STATE_VALUE_NICE = "nice";
var SYSTEM_CPU_STATE_VALUE_STEAL = "steal";
var SYSTEM_CPU_STATE_VALUE_SYSTEM = "system";
var SYSTEM_CPU_STATE_VALUE_USER = "user";
var ATTR_SYSTEM_DEVICE = 'system.device';
var ATTR_SYSTEM_FILESYSTEM_MODE = 'system.filesystem.mode';
var ATTR_SYSTEM_FILESYSTEM_MOUNTPOINT = 'system.filesystem.mountpoint';
var ATTR_SYSTEM_FILESYSTEM_STATE = 'system.filesystem.state';
var SYSTEM_FILESYSTEM_STATE_VALUE_FREE = "free";
var SYSTEM_FILESYSTEM_STATE_VALUE_RESERVED = "reserved";
var SYSTEM_FILESYSTEM_STATE_VALUE_USED = "used";
var ATTR_SYSTEM_FILESYSTEM_TYPE = 'system.filesystem.type';
var SYSTEM_FILESYSTEM_TYPE_VALUE_EXFAT = "exfat";
var SYSTEM_FILESYSTEM_TYPE_VALUE_EXT4 = "ext4";
var SYSTEM_FILESYSTEM_TYPE_VALUE_FAT32 = "fat32";
var SYSTEM_FILESYSTEM_TYPE_VALUE_HFSPLUS = "hfsplus";
var SYSTEM_FILESYSTEM_TYPE_VALUE_NTFS = "ntfs";
var SYSTEM_FILESYSTEM_TYPE_VALUE_REFS = "refs";
var ATTR_SYSTEM_MEMORY_STATE = 'system.memory.state';
var SYSTEM_MEMORY_STATE_VALUE_BUFFERS = "buffers";
var SYSTEM_MEMORY_STATE_VALUE_CACHED = "cached";
var SYSTEM_MEMORY_STATE_VALUE_FREE = "free";
var SYSTEM_MEMORY_STATE_VALUE_SHARED = "shared";
var SYSTEM_MEMORY_STATE_VALUE_USED = "used";
var ATTR_SYSTEM_NETWORK_STATE = 'system.network.state';
var SYSTEM_NETWORK_STATE_VALUE_CLOSE = "close";
var SYSTEM_NETWORK_STATE_VALUE_CLOSE_WAIT = "close_wait";
var SYSTEM_NETWORK_STATE_VALUE_CLOSING = "closing";
var SYSTEM_NETWORK_STATE_VALUE_DELETE = "delete";
var SYSTEM_NETWORK_STATE_VALUE_ESTABLISHED = "established";
var SYSTEM_NETWORK_STATE_VALUE_FIN_WAIT_1 = "fin_wait_1";
var SYSTEM_NETWORK_STATE_VALUE_FIN_WAIT_2 = "fin_wait_2";
var SYSTEM_NETWORK_STATE_VALUE_LAST_ACK = "last_ack";
var SYSTEM_NETWORK_STATE_VALUE_LISTEN = "listen";
var SYSTEM_NETWORK_STATE_VALUE_SYN_RECV = "syn_recv";
var SYSTEM_NETWORK_STATE_VALUE_SYN_SENT = "syn_sent";
var SYSTEM_NETWORK_STATE_VALUE_TIME_WAIT = "time_wait";
var ATTR_SYSTEM_PAGING_DIRECTION = 'system.paging.direction';
var SYSTEM_PAGING_DIRECTION_VALUE_IN = "in";
var SYSTEM_PAGING_DIRECTION_VALUE_OUT = "out";
var ATTR_SYSTEM_PAGING_STATE = 'system.paging.state';
var SYSTEM_PAGING_STATE_VALUE_FREE = "free";
var SYSTEM_PAGING_STATE_VALUE_USED = "used";
var ATTR_SYSTEM_PAGING_TYPE = 'system.paging.type';
var SYSTEM_PAGING_TYPE_VALUE_MAJOR = "major";
var SYSTEM_PAGING_TYPE_VALUE_MINOR = "minor";
var ATTR_SYSTEM_PROCESS_STATUS = 'system.process.status';
var SYSTEM_PROCESS_STATUS_VALUE_DEFUNCT = "defunct";
var SYSTEM_PROCESS_STATUS_VALUE_RUNNING = "running";
var SYSTEM_PROCESS_STATUS_VALUE_SLEEPING = "sleeping";
var SYSTEM_PROCESS_STATUS_VALUE_STOPPED = "stopped";
var ATTR_SYSTEM_PROCESSES_STATUS = 'system.processes.status';
var SYSTEM_PROCESSES_STATUS_VALUE_DEFUNCT = "defunct";
var SYSTEM_PROCESSES_STATUS_VALUE_RUNNING = "running";
var SYSTEM_PROCESSES_STATUS_VALUE_SLEEPING = "sleeping";
var SYSTEM_PROCESSES_STATUS_VALUE_STOPPED = "stopped";
var ATTR_TELEMETRY_DISTRO_NAME = 'telemetry.distro.name';
var ATTR_TELEMETRY_DISTRO_VERSION = 'telemetry.distro.version';
var ATTR_TEST_CASE_NAME = 'test.case.name';
var ATTR_TEST_CASE_RESULT_STATUS = 'test.case.result.status';
var TEST_CASE_RESULT_STATUS_VALUE_FAIL = "fail";
var TEST_CASE_RESULT_STATUS_VALUE_PASS = "pass";
var ATTR_TEST_SUITE_NAME = 'test.suite.name';
var ATTR_TEST_SUITE_RUN_STATUS = 'test.suite.run.status';
var TEST_SUITE_RUN_STATUS_VALUE_ABORTED = "aborted";
var TEST_SUITE_RUN_STATUS_VALUE_FAILURE = "failure";
var TEST_SUITE_RUN_STATUS_VALUE_IN_PROGRESS = "in_progress";
var TEST_SUITE_RUN_STATUS_VALUE_SKIPPED = "skipped";
var TEST_SUITE_RUN_STATUS_VALUE_SUCCESS = "success";
var TEST_SUITE_RUN_STATUS_VALUE_TIMED_OUT = "timed_out";
var ATTR_THREAD_ID = 'thread.id';
var ATTR_THREAD_NAME = 'thread.name';
var ATTR_TLS_CIPHER = 'tls.cipher';
var ATTR_TLS_CLIENT_CERTIFICATE = 'tls.client.certificate';
var ATTR_TLS_CLIENT_CERTIFICATE_CHAIN = 'tls.client.certificate_chain';
var ATTR_TLS_CLIENT_HASH_MD5 = 'tls.client.hash.md5';
var ATTR_TLS_CLIENT_HASH_SHA1 = 'tls.client.hash.sha1';
var ATTR_TLS_CLIENT_HASH_SHA256 = 'tls.client.hash.sha256';
var ATTR_TLS_CLIENT_ISSUER = 'tls.client.issuer';
var ATTR_TLS_CLIENT_JA3 = 'tls.client.ja3';
var ATTR_TLS_CLIENT_NOT_AFTER = 'tls.client.not_after';
var ATTR_TLS_CLIENT_NOT_BEFORE = 'tls.client.not_before';
var ATTR_TLS_CLIENT_SERVER_NAME = 'tls.client.server_name';
var ATTR_TLS_CLIENT_SUBJECT = 'tls.client.subject';
var ATTR_TLS_CLIENT_SUPPORTED_CIPHERS = 'tls.client.supported_ciphers';
var ATTR_TLS_CURVE = 'tls.curve';
var ATTR_TLS_ESTABLISHED = 'tls.established';
var ATTR_TLS_NEXT_PROTOCOL = 'tls.next_protocol';
var ATTR_TLS_PROTOCOL_NAME = 'tls.protocol.name';
var TLS_PROTOCOL_NAME_VALUE_SSL = "ssl";
var TLS_PROTOCOL_NAME_VALUE_TLS = "tls";
var ATTR_TLS_PROTOCOL_VERSION = 'tls.protocol.version';
var ATTR_TLS_RESUMED = 'tls.resumed';
var ATTR_TLS_SERVER_CERTIFICATE = 'tls.server.certificate';
var ATTR_TLS_SERVER_CERTIFICATE_CHAIN = 'tls.server.certificate_chain';
var ATTR_TLS_SERVER_HASH_MD5 = 'tls.server.hash.md5';
var ATTR_TLS_SERVER_HASH_SHA1 = 'tls.server.hash.sha1';
var ATTR_TLS_SERVER_HASH_SHA256 = 'tls.server.hash.sha256';
var ATTR_TLS_SERVER_ISSUER = 'tls.server.issuer';
var ATTR_TLS_SERVER_JA3S = 'tls.server.ja3s';
var ATTR_TLS_SERVER_NOT_AFTER = 'tls.server.not_after';
var ATTR_TLS_SERVER_NOT_BEFORE = 'tls.server.not_before';
var ATTR_TLS_SERVER_SUBJECT = 'tls.server.subject';
var ATTR_URL_DOMAIN = 'url.domain';
var ATTR_URL_EXTENSION = 'url.extension';
var ATTR_URL_ORIGINAL = 'url.original';
var ATTR_URL_PORT = 'url.port';
var ATTR_URL_REGISTERED_DOMAIN = 'url.registered_domain';
var ATTR_URL_SUBDOMAIN = 'url.subdomain';
var ATTR_URL_TEMPLATE = 'url.template';
var ATTR_URL_TOP_LEVEL_DOMAIN = 'url.top_level_domain';
var ATTR_USER_EMAIL = 'user.email';
var ATTR_USER_FULL_NAME = 'user.full_name';
var ATTR_USER_HASH = 'user.hash';
var ATTR_USER_ID = 'user.id';
var ATTR_USER_NAME = 'user.name';
var ATTR_USER_ROLES = 'user.roles';
var ATTR_USER_AGENT_NAME = 'user_agent.name';
var ATTR_USER_AGENT_VERSION = 'user_agent.version';
var ATTR_V8JS_GC_TYPE = 'v8js.gc.type';
var V8JS_GC_TYPE_VALUE_INCREMENTAL = "incremental";
var V8JS_GC_TYPE_VALUE_MAJOR = "major";
var V8JS_GC_TYPE_VALUE_MINOR = "minor";
var V8JS_GC_TYPE_VALUE_WEAKCB = "weakcb";
var ATTR_V8JS_HEAP_SPACE_NAME = 'v8js.heap.space.name';
var V8JS_HEAP_SPACE_NAME_VALUE_CODE_SPACE = "code_space";
var V8JS_HEAP_SPACE_NAME_VALUE_LARGE_OBJECT_SPACE = "large_object_space";
var V8JS_HEAP_SPACE_NAME_VALUE_MAP_SPACE = "map_space";
var V8JS_HEAP_SPACE_NAME_VALUE_NEW_SPACE = "new_space";
var V8JS_HEAP_SPACE_NAME_VALUE_OLD_SPACE = "old_space";
var ATTR_VCS_REPOSITORY_CHANGE_ID = 'vcs.repository.change.id';
var ATTR_VCS_REPOSITORY_CHANGE_TITLE = 'vcs.repository.change.title';
var ATTR_VCS_REPOSITORY_REF_NAME = 'vcs.repository.ref.name';
var ATTR_VCS_REPOSITORY_REF_REVISION = 'vcs.repository.ref.revision';
var ATTR_VCS_REPOSITORY_REF_TYPE = 'vcs.repository.ref.type';
var VCS_REPOSITORY_REF_TYPE_VALUE_BRANCH = "branch";
var VCS_REPOSITORY_REF_TYPE_VALUE_TAG = "tag";
var ATTR_VCS_REPOSITORY_URL_FULL = 'vcs.repository.url.full';
var ATTR_WEBENGINE_DESCRIPTION = 'webengine.description';
var ATTR_WEBENGINE_NAME = 'webengine.name';
var ATTR_WEBENGINE_VERSION = 'webengine.version'; //# sourceMappingURL=experimental_attributes.js.map
}),
"[project]/node_modules/.pnpm/@opentelemetry+semantic-conventions@1.27.0/node_modules/@opentelemetry/semantic-conventions/build/esm/experimental_metrics.js [instrumentation] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ //----------------------------------------------------------------------------------------------------------
// DO NOT EDIT, this is an Auto-generated file from scripts/semconv/templates/register/stable/metrics.ts.j2
//----------------------------------------------------------------------------------------------------------
/**
 * Total CPU time consumed
 *
 * @note Total CPU time consumed by the specific container on all available CPU cores
 *
 * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.
 */ __turbopack_context__.s({
    "METRIC_CONTAINER_CPU_TIME": ()=>METRIC_CONTAINER_CPU_TIME,
    "METRIC_CONTAINER_DISK_IO": ()=>METRIC_CONTAINER_DISK_IO,
    "METRIC_CONTAINER_MEMORY_USAGE": ()=>METRIC_CONTAINER_MEMORY_USAGE,
    "METRIC_CONTAINER_NETWORK_IO": ()=>METRIC_CONTAINER_NETWORK_IO,
    "METRIC_DB_CLIENT_CONNECTIONS_CREATE_TIME": ()=>METRIC_DB_CLIENT_CONNECTIONS_CREATE_TIME,
    "METRIC_DB_CLIENT_CONNECTIONS_IDLE_MAX": ()=>METRIC_DB_CLIENT_CONNECTIONS_IDLE_MAX,
    "METRIC_DB_CLIENT_CONNECTIONS_IDLE_MIN": ()=>METRIC_DB_CLIENT_CONNECTIONS_IDLE_MIN,
    "METRIC_DB_CLIENT_CONNECTIONS_MAX": ()=>METRIC_DB_CLIENT_CONNECTIONS_MAX,
    "METRIC_DB_CLIENT_CONNECTIONS_PENDING_REQUESTS": ()=>METRIC_DB_CLIENT_CONNECTIONS_PENDING_REQUESTS,
    "METRIC_DB_CLIENT_CONNECTIONS_TIMEOUTS": ()=>METRIC_DB_CLIENT_CONNECTIONS_TIMEOUTS,
    "METRIC_DB_CLIENT_CONNECTIONS_USAGE": ()=>METRIC_DB_CLIENT_CONNECTIONS_USAGE,
    "METRIC_DB_CLIENT_CONNECTIONS_USE_TIME": ()=>METRIC_DB_CLIENT_CONNECTIONS_USE_TIME,
    "METRIC_DB_CLIENT_CONNECTIONS_WAIT_TIME": ()=>METRIC_DB_CLIENT_CONNECTIONS_WAIT_TIME,
    "METRIC_DB_CLIENT_CONNECTION_COUNT": ()=>METRIC_DB_CLIENT_CONNECTION_COUNT,
    "METRIC_DB_CLIENT_CONNECTION_CREATE_TIME": ()=>METRIC_DB_CLIENT_CONNECTION_CREATE_TIME,
    "METRIC_DB_CLIENT_CONNECTION_IDLE_MAX": ()=>METRIC_DB_CLIENT_CONNECTION_IDLE_MAX,
    "METRIC_DB_CLIENT_CONNECTION_IDLE_MIN": ()=>METRIC_DB_CLIENT_CONNECTION_IDLE_MIN,
    "METRIC_DB_CLIENT_CONNECTION_MAX": ()=>METRIC_DB_CLIENT_CONNECTION_MAX,
    "METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS": ()=>METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS,
    "METRIC_DB_CLIENT_CONNECTION_TIMEOUTS": ()=>METRIC_DB_CLIENT_CONNECTION_TIMEOUTS,
    "METRIC_DB_CLIENT_CONNECTION_USE_TIME": ()=>METRIC_DB_CLIENT_CONNECTION_USE_TIME,
    "METRIC_DB_CLIENT_CONNECTION_WAIT_TIME": ()=>METRIC_DB_CLIENT_CONNECTION_WAIT_TIME,
    "METRIC_DB_CLIENT_OPERATION_DURATION": ()=>METRIC_DB_CLIENT_OPERATION_DURATION,
    "METRIC_DNS_LOOKUP_DURATION": ()=>METRIC_DNS_LOOKUP_DURATION,
    "METRIC_FAAS_COLDSTARTS": ()=>METRIC_FAAS_COLDSTARTS,
    "METRIC_FAAS_CPU_USAGE": ()=>METRIC_FAAS_CPU_USAGE,
    "METRIC_FAAS_ERRORS": ()=>METRIC_FAAS_ERRORS,
    "METRIC_FAAS_INIT_DURATION": ()=>METRIC_FAAS_INIT_DURATION,
    "METRIC_FAAS_INVOCATIONS": ()=>METRIC_FAAS_INVOCATIONS,
    "METRIC_FAAS_INVOKE_DURATION": ()=>METRIC_FAAS_INVOKE_DURATION,
    "METRIC_FAAS_MEM_USAGE": ()=>METRIC_FAAS_MEM_USAGE,
    "METRIC_FAAS_NET_IO": ()=>METRIC_FAAS_NET_IO,
    "METRIC_FAAS_TIMEOUTS": ()=>METRIC_FAAS_TIMEOUTS,
    "METRIC_GEN_AI_CLIENT_OPERATION_DURATION": ()=>METRIC_GEN_AI_CLIENT_OPERATION_DURATION,
    "METRIC_GEN_AI_CLIENT_TOKEN_USAGE": ()=>METRIC_GEN_AI_CLIENT_TOKEN_USAGE,
    "METRIC_GEN_AI_SERVER_REQUEST_DURATION": ()=>METRIC_GEN_AI_SERVER_REQUEST_DURATION,
    "METRIC_GEN_AI_SERVER_TIME_PER_OUTPUT_TOKEN": ()=>METRIC_GEN_AI_SERVER_TIME_PER_OUTPUT_TOKEN,
    "METRIC_GEN_AI_SERVER_TIME_TO_FIRST_TOKEN": ()=>METRIC_GEN_AI_SERVER_TIME_TO_FIRST_TOKEN,
    "METRIC_GO_CONFIG_GOGC": ()=>METRIC_GO_CONFIG_GOGC,
    "METRIC_GO_GOROUTINE_COUNT": ()=>METRIC_GO_GOROUTINE_COUNT,
    "METRIC_GO_MEMORY_ALLOCATED": ()=>METRIC_GO_MEMORY_ALLOCATED,
    "METRIC_GO_MEMORY_ALLOCATIONS": ()=>METRIC_GO_MEMORY_ALLOCATIONS,
    "METRIC_GO_MEMORY_GC_GOAL": ()=>METRIC_GO_MEMORY_GC_GOAL,
    "METRIC_GO_MEMORY_LIMIT": ()=>METRIC_GO_MEMORY_LIMIT,
    "METRIC_GO_MEMORY_USED": ()=>METRIC_GO_MEMORY_USED,
    "METRIC_GO_PROCESSOR_LIMIT": ()=>METRIC_GO_PROCESSOR_LIMIT,
    "METRIC_GO_SCHEDULE_DURATION": ()=>METRIC_GO_SCHEDULE_DURATION,
    "METRIC_HTTP_CLIENT_ACTIVE_REQUESTS": ()=>METRIC_HTTP_CLIENT_ACTIVE_REQUESTS,
    "METRIC_HTTP_CLIENT_CONNECTION_DURATION": ()=>METRIC_HTTP_CLIENT_CONNECTION_DURATION,
    "METRIC_HTTP_CLIENT_OPEN_CONNECTIONS": ()=>METRIC_HTTP_CLIENT_OPEN_CONNECTIONS,
    "METRIC_HTTP_CLIENT_REQUEST_BODY_SIZE": ()=>METRIC_HTTP_CLIENT_REQUEST_BODY_SIZE,
    "METRIC_HTTP_CLIENT_RESPONSE_BODY_SIZE": ()=>METRIC_HTTP_CLIENT_RESPONSE_BODY_SIZE,
    "METRIC_HTTP_SERVER_ACTIVE_REQUESTS": ()=>METRIC_HTTP_SERVER_ACTIVE_REQUESTS,
    "METRIC_HTTP_SERVER_REQUEST_BODY_SIZE": ()=>METRIC_HTTP_SERVER_REQUEST_BODY_SIZE,
    "METRIC_HTTP_SERVER_RESPONSE_BODY_SIZE": ()=>METRIC_HTTP_SERVER_RESPONSE_BODY_SIZE,
    "METRIC_JVM_BUFFER_COUNT": ()=>METRIC_JVM_BUFFER_COUNT,
    "METRIC_JVM_BUFFER_MEMORY_LIMIT": ()=>METRIC_JVM_BUFFER_MEMORY_LIMIT,
    "METRIC_JVM_BUFFER_MEMORY_USAGE": ()=>METRIC_JVM_BUFFER_MEMORY_USAGE,
    "METRIC_JVM_BUFFER_MEMORY_USED": ()=>METRIC_JVM_BUFFER_MEMORY_USED,
    "METRIC_JVM_MEMORY_INIT": ()=>METRIC_JVM_MEMORY_INIT,
    "METRIC_JVM_SYSTEM_CPU_LOAD_1M": ()=>METRIC_JVM_SYSTEM_CPU_LOAD_1M,
    "METRIC_JVM_SYSTEM_CPU_UTILIZATION": ()=>METRIC_JVM_SYSTEM_CPU_UTILIZATION,
    "METRIC_MESSAGING_CLIENT_CONSUMED_MESSAGES": ()=>METRIC_MESSAGING_CLIENT_CONSUMED_MESSAGES,
    "METRIC_MESSAGING_CLIENT_OPERATION_DURATION": ()=>METRIC_MESSAGING_CLIENT_OPERATION_DURATION,
    "METRIC_MESSAGING_CLIENT_PUBLISHED_MESSAGES": ()=>METRIC_MESSAGING_CLIENT_PUBLISHED_MESSAGES,
    "METRIC_MESSAGING_PROCESS_DURATION": ()=>METRIC_MESSAGING_PROCESS_DURATION,
    "METRIC_MESSAGING_PROCESS_MESSAGES": ()=>METRIC_MESSAGING_PROCESS_MESSAGES,
    "METRIC_MESSAGING_PUBLISH_DURATION": ()=>METRIC_MESSAGING_PUBLISH_DURATION,
    "METRIC_MESSAGING_PUBLISH_MESSAGES": ()=>METRIC_MESSAGING_PUBLISH_MESSAGES,
    "METRIC_MESSAGING_RECEIVE_DURATION": ()=>METRIC_MESSAGING_RECEIVE_DURATION,
    "METRIC_MESSAGING_RECEIVE_MESSAGES": ()=>METRIC_MESSAGING_RECEIVE_MESSAGES,
    "METRIC_NODEJS_EVENTLOOP_DELAY_MAX": ()=>METRIC_NODEJS_EVENTLOOP_DELAY_MAX,
    "METRIC_NODEJS_EVENTLOOP_DELAY_MEAN": ()=>METRIC_NODEJS_EVENTLOOP_DELAY_MEAN,
    "METRIC_NODEJS_EVENTLOOP_DELAY_MIN": ()=>METRIC_NODEJS_EVENTLOOP_DELAY_MIN,
    "METRIC_NODEJS_EVENTLOOP_DELAY_P50": ()=>METRIC_NODEJS_EVENTLOOP_DELAY_P50,
    "METRIC_NODEJS_EVENTLOOP_DELAY_P90": ()=>METRIC_NODEJS_EVENTLOOP_DELAY_P90,
    "METRIC_NODEJS_EVENTLOOP_DELAY_P99": ()=>METRIC_NODEJS_EVENTLOOP_DELAY_P99,
    "METRIC_NODEJS_EVENTLOOP_DELAY_STDDEV": ()=>METRIC_NODEJS_EVENTLOOP_DELAY_STDDEV,
    "METRIC_NODEJS_EVENTLOOP_UTILIZATION": ()=>METRIC_NODEJS_EVENTLOOP_UTILIZATION,
    "METRIC_PROCESS_CONTEXT_SWITCHES": ()=>METRIC_PROCESS_CONTEXT_SWITCHES,
    "METRIC_PROCESS_CPU_TIME": ()=>METRIC_PROCESS_CPU_TIME,
    "METRIC_PROCESS_CPU_UTILIZATION": ()=>METRIC_PROCESS_CPU_UTILIZATION,
    "METRIC_PROCESS_DISK_IO": ()=>METRIC_PROCESS_DISK_IO,
    "METRIC_PROCESS_MEMORY_USAGE": ()=>METRIC_PROCESS_MEMORY_USAGE,
    "METRIC_PROCESS_MEMORY_VIRTUAL": ()=>METRIC_PROCESS_MEMORY_VIRTUAL,
    "METRIC_PROCESS_NETWORK_IO": ()=>METRIC_PROCESS_NETWORK_IO,
    "METRIC_PROCESS_OPEN_FILE_DESCRIPTOR_COUNT": ()=>METRIC_PROCESS_OPEN_FILE_DESCRIPTOR_COUNT,
    "METRIC_PROCESS_PAGING_FAULTS": ()=>METRIC_PROCESS_PAGING_FAULTS,
    "METRIC_PROCESS_THREAD_COUNT": ()=>METRIC_PROCESS_THREAD_COUNT,
    "METRIC_RPC_CLIENT_DURATION": ()=>METRIC_RPC_CLIENT_DURATION,
    "METRIC_RPC_CLIENT_REQUESTS_PER_RPC": ()=>METRIC_RPC_CLIENT_REQUESTS_PER_RPC,
    "METRIC_RPC_CLIENT_REQUEST_SIZE": ()=>METRIC_RPC_CLIENT_REQUEST_SIZE,
    "METRIC_RPC_CLIENT_RESPONSES_PER_RPC": ()=>METRIC_RPC_CLIENT_RESPONSES_PER_RPC,
    "METRIC_RPC_CLIENT_RESPONSE_SIZE": ()=>METRIC_RPC_CLIENT_RESPONSE_SIZE,
    "METRIC_RPC_SERVER_DURATION": ()=>METRIC_RPC_SERVER_DURATION,
    "METRIC_RPC_SERVER_REQUESTS_PER_RPC": ()=>METRIC_RPC_SERVER_REQUESTS_PER_RPC,
    "METRIC_RPC_SERVER_REQUEST_SIZE": ()=>METRIC_RPC_SERVER_REQUEST_SIZE,
    "METRIC_RPC_SERVER_RESPONSES_PER_RPC": ()=>METRIC_RPC_SERVER_RESPONSES_PER_RPC,
    "METRIC_RPC_SERVER_RESPONSE_SIZE": ()=>METRIC_RPC_SERVER_RESPONSE_SIZE,
    "METRIC_SYSTEM_CPU_FREQUENCY": ()=>METRIC_SYSTEM_CPU_FREQUENCY,
    "METRIC_SYSTEM_CPU_LOGICAL_COUNT": ()=>METRIC_SYSTEM_CPU_LOGICAL_COUNT,
    "METRIC_SYSTEM_CPU_PHYSICAL_COUNT": ()=>METRIC_SYSTEM_CPU_PHYSICAL_COUNT,
    "METRIC_SYSTEM_CPU_TIME": ()=>METRIC_SYSTEM_CPU_TIME,
    "METRIC_SYSTEM_CPU_UTILIZATION": ()=>METRIC_SYSTEM_CPU_UTILIZATION,
    "METRIC_SYSTEM_DISK_IO": ()=>METRIC_SYSTEM_DISK_IO,
    "METRIC_SYSTEM_DISK_IO_TIME": ()=>METRIC_SYSTEM_DISK_IO_TIME,
    "METRIC_SYSTEM_DISK_MERGED": ()=>METRIC_SYSTEM_DISK_MERGED,
    "METRIC_SYSTEM_DISK_OPERATIONS": ()=>METRIC_SYSTEM_DISK_OPERATIONS,
    "METRIC_SYSTEM_DISK_OPERATION_TIME": ()=>METRIC_SYSTEM_DISK_OPERATION_TIME,
    "METRIC_SYSTEM_FILESYSTEM_USAGE": ()=>METRIC_SYSTEM_FILESYSTEM_USAGE,
    "METRIC_SYSTEM_FILESYSTEM_UTILIZATION": ()=>METRIC_SYSTEM_FILESYSTEM_UTILIZATION,
    "METRIC_SYSTEM_LINUX_MEMORY_AVAILABLE": ()=>METRIC_SYSTEM_LINUX_MEMORY_AVAILABLE,
    "METRIC_SYSTEM_LINUX_MEMORY_SLAB_USAGE": ()=>METRIC_SYSTEM_LINUX_MEMORY_SLAB_USAGE,
    "METRIC_SYSTEM_MEMORY_LIMIT": ()=>METRIC_SYSTEM_MEMORY_LIMIT,
    "METRIC_SYSTEM_MEMORY_SHARED": ()=>METRIC_SYSTEM_MEMORY_SHARED,
    "METRIC_SYSTEM_MEMORY_USAGE": ()=>METRIC_SYSTEM_MEMORY_USAGE,
    "METRIC_SYSTEM_MEMORY_UTILIZATION": ()=>METRIC_SYSTEM_MEMORY_UTILIZATION,
    "METRIC_SYSTEM_NETWORK_CONNECTIONS": ()=>METRIC_SYSTEM_NETWORK_CONNECTIONS,
    "METRIC_SYSTEM_NETWORK_DROPPED": ()=>METRIC_SYSTEM_NETWORK_DROPPED,
    "METRIC_SYSTEM_NETWORK_ERRORS": ()=>METRIC_SYSTEM_NETWORK_ERRORS,
    "METRIC_SYSTEM_NETWORK_IO": ()=>METRIC_SYSTEM_NETWORK_IO,
    "METRIC_SYSTEM_NETWORK_PACKETS": ()=>METRIC_SYSTEM_NETWORK_PACKETS,
    "METRIC_SYSTEM_PAGING_FAULTS": ()=>METRIC_SYSTEM_PAGING_FAULTS,
    "METRIC_SYSTEM_PAGING_OPERATIONS": ()=>METRIC_SYSTEM_PAGING_OPERATIONS,
    "METRIC_SYSTEM_PAGING_USAGE": ()=>METRIC_SYSTEM_PAGING_USAGE,
    "METRIC_SYSTEM_PAGING_UTILIZATION": ()=>METRIC_SYSTEM_PAGING_UTILIZATION,
    "METRIC_SYSTEM_PROCESS_COUNT": ()=>METRIC_SYSTEM_PROCESS_COUNT,
    "METRIC_SYSTEM_PROCESS_CREATED": ()=>METRIC_SYSTEM_PROCESS_CREATED,
    "METRIC_V8JS_GC_DURATION": ()=>METRIC_V8JS_GC_DURATION,
    "METRIC_V8JS_HEAP_SPACE_AVAILABLE_SIZE": ()=>METRIC_V8JS_HEAP_SPACE_AVAILABLE_SIZE,
    "METRIC_V8JS_HEAP_SPACE_PHYSICAL_SIZE": ()=>METRIC_V8JS_HEAP_SPACE_PHYSICAL_SIZE,
    "METRIC_V8JS_MEMORY_HEAP_LIMIT": ()=>METRIC_V8JS_MEMORY_HEAP_LIMIT,
    "METRIC_V8JS_MEMORY_HEAP_USED": ()=>METRIC_V8JS_MEMORY_HEAP_USED
});
var METRIC_CONTAINER_CPU_TIME = 'container.cpu.time';
var METRIC_CONTAINER_DISK_IO = 'container.disk.io';
var METRIC_CONTAINER_MEMORY_USAGE = 'container.memory.usage';
var METRIC_CONTAINER_NETWORK_IO = 'container.network.io';
var METRIC_DB_CLIENT_CONNECTION_COUNT = 'db.client.connection.count';
var METRIC_DB_CLIENT_CONNECTION_CREATE_TIME = 'db.client.connection.create_time';
var METRIC_DB_CLIENT_CONNECTION_IDLE_MAX = 'db.client.connection.idle.max';
var METRIC_DB_CLIENT_CONNECTION_IDLE_MIN = 'db.client.connection.idle.min';
var METRIC_DB_CLIENT_CONNECTION_MAX = 'db.client.connection.max';
var METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS = 'db.client.connection.pending_requests';
var METRIC_DB_CLIENT_CONNECTION_TIMEOUTS = 'db.client.connection.timeouts';
var METRIC_DB_CLIENT_CONNECTION_USE_TIME = 'db.client.connection.use_time';
var METRIC_DB_CLIENT_CONNECTION_WAIT_TIME = 'db.client.connection.wait_time';
var METRIC_DB_CLIENT_CONNECTIONS_CREATE_TIME = 'db.client.connections.create_time';
var METRIC_DB_CLIENT_CONNECTIONS_IDLE_MAX = 'db.client.connections.idle.max';
var METRIC_DB_CLIENT_CONNECTIONS_IDLE_MIN = 'db.client.connections.idle.min';
var METRIC_DB_CLIENT_CONNECTIONS_MAX = 'db.client.connections.max';
var METRIC_DB_CLIENT_CONNECTIONS_PENDING_REQUESTS = 'db.client.connections.pending_requests';
var METRIC_DB_CLIENT_CONNECTIONS_TIMEOUTS = 'db.client.connections.timeouts';
var METRIC_DB_CLIENT_CONNECTIONS_USAGE = 'db.client.connections.usage';
var METRIC_DB_CLIENT_CONNECTIONS_USE_TIME = 'db.client.connections.use_time';
var METRIC_DB_CLIENT_CONNECTIONS_WAIT_TIME = 'db.client.connections.wait_time';
var METRIC_DB_CLIENT_OPERATION_DURATION = 'db.client.operation.duration';
var METRIC_DNS_LOOKUP_DURATION = 'dns.lookup.duration';
var METRIC_FAAS_COLDSTARTS = 'faas.coldstarts';
var METRIC_FAAS_CPU_USAGE = 'faas.cpu_usage';
var METRIC_FAAS_ERRORS = 'faas.errors';
var METRIC_FAAS_INIT_DURATION = 'faas.init_duration';
var METRIC_FAAS_INVOCATIONS = 'faas.invocations';
var METRIC_FAAS_INVOKE_DURATION = 'faas.invoke_duration';
var METRIC_FAAS_MEM_USAGE = 'faas.mem_usage';
var METRIC_FAAS_NET_IO = 'faas.net_io';
var METRIC_FAAS_TIMEOUTS = 'faas.timeouts';
var METRIC_GEN_AI_CLIENT_OPERATION_DURATION = 'gen_ai.client.operation.duration';
var METRIC_GEN_AI_CLIENT_TOKEN_USAGE = 'gen_ai.client.token.usage';
var METRIC_GEN_AI_SERVER_REQUEST_DURATION = 'gen_ai.server.request.duration';
var METRIC_GEN_AI_SERVER_TIME_PER_OUTPUT_TOKEN = 'gen_ai.server.time_per_output_token';
var METRIC_GEN_AI_SERVER_TIME_TO_FIRST_TOKEN = 'gen_ai.server.time_to_first_token';
var METRIC_GO_CONFIG_GOGC = 'go.config.gogc';
var METRIC_GO_GOROUTINE_COUNT = 'go.goroutine.count';
var METRIC_GO_MEMORY_ALLOCATED = 'go.memory.allocated';
var METRIC_GO_MEMORY_ALLOCATIONS = 'go.memory.allocations';
var METRIC_GO_MEMORY_GC_GOAL = 'go.memory.gc.goal';
var METRIC_GO_MEMORY_LIMIT = 'go.memory.limit';
var METRIC_GO_MEMORY_USED = 'go.memory.used';
var METRIC_GO_PROCESSOR_LIMIT = 'go.processor.limit';
var METRIC_GO_SCHEDULE_DURATION = 'go.schedule.duration';
var METRIC_HTTP_CLIENT_ACTIVE_REQUESTS = 'http.client.active_requests';
var METRIC_HTTP_CLIENT_CONNECTION_DURATION = 'http.client.connection.duration';
var METRIC_HTTP_CLIENT_OPEN_CONNECTIONS = 'http.client.open_connections';
var METRIC_HTTP_CLIENT_REQUEST_BODY_SIZE = 'http.client.request.body.size';
var METRIC_HTTP_CLIENT_RESPONSE_BODY_SIZE = 'http.client.response.body.size';
var METRIC_HTTP_SERVER_ACTIVE_REQUESTS = 'http.server.active_requests';
var METRIC_HTTP_SERVER_REQUEST_BODY_SIZE = 'http.server.request.body.size';
var METRIC_HTTP_SERVER_RESPONSE_BODY_SIZE = 'http.server.response.body.size';
var METRIC_JVM_BUFFER_COUNT = 'jvm.buffer.count';
var METRIC_JVM_BUFFER_MEMORY_LIMIT = 'jvm.buffer.memory.limit';
var METRIC_JVM_BUFFER_MEMORY_USAGE = 'jvm.buffer.memory.usage';
var METRIC_JVM_BUFFER_MEMORY_USED = 'jvm.buffer.memory.used';
var METRIC_JVM_MEMORY_INIT = 'jvm.memory.init';
var METRIC_JVM_SYSTEM_CPU_LOAD_1M = 'jvm.system.cpu.load_1m';
var METRIC_JVM_SYSTEM_CPU_UTILIZATION = 'jvm.system.cpu.utilization';
var METRIC_MESSAGING_CLIENT_CONSUMED_MESSAGES = 'messaging.client.consumed.messages';
var METRIC_MESSAGING_CLIENT_OPERATION_DURATION = 'messaging.client.operation.duration';
var METRIC_MESSAGING_CLIENT_PUBLISHED_MESSAGES = 'messaging.client.published.messages';
var METRIC_MESSAGING_PROCESS_DURATION = 'messaging.process.duration';
var METRIC_MESSAGING_PROCESS_MESSAGES = 'messaging.process.messages';
var METRIC_MESSAGING_PUBLISH_DURATION = 'messaging.publish.duration';
var METRIC_MESSAGING_PUBLISH_MESSAGES = 'messaging.publish.messages';
var METRIC_MESSAGING_RECEIVE_DURATION = 'messaging.receive.duration';
var METRIC_MESSAGING_RECEIVE_MESSAGES = 'messaging.receive.messages';
var METRIC_NODEJS_EVENTLOOP_DELAY_MAX = 'nodejs.eventloop.delay.max';
var METRIC_NODEJS_EVENTLOOP_DELAY_MEAN = 'nodejs.eventloop.delay.mean';
var METRIC_NODEJS_EVENTLOOP_DELAY_MIN = 'nodejs.eventloop.delay.min';
var METRIC_NODEJS_EVENTLOOP_DELAY_P50 = 'nodejs.eventloop.delay.p50';
var METRIC_NODEJS_EVENTLOOP_DELAY_P90 = 'nodejs.eventloop.delay.p90';
var METRIC_NODEJS_EVENTLOOP_DELAY_P99 = 'nodejs.eventloop.delay.p99';
var METRIC_NODEJS_EVENTLOOP_DELAY_STDDEV = 'nodejs.eventloop.delay.stddev';
var METRIC_NODEJS_EVENTLOOP_UTILIZATION = 'nodejs.eventloop.utilization';
var METRIC_PROCESS_CONTEXT_SWITCHES = 'process.context_switches';
var METRIC_PROCESS_CPU_TIME = 'process.cpu.time';
var METRIC_PROCESS_CPU_UTILIZATION = 'process.cpu.utilization';
var METRIC_PROCESS_DISK_IO = 'process.disk.io';
var METRIC_PROCESS_MEMORY_USAGE = 'process.memory.usage';
var METRIC_PROCESS_MEMORY_VIRTUAL = 'process.memory.virtual';
var METRIC_PROCESS_NETWORK_IO = 'process.network.io';
var METRIC_PROCESS_OPEN_FILE_DESCRIPTOR_COUNT = 'process.open_file_descriptor.count';
var METRIC_PROCESS_PAGING_FAULTS = 'process.paging.faults';
var METRIC_PROCESS_THREAD_COUNT = 'process.thread.count';
var METRIC_RPC_CLIENT_DURATION = 'rpc.client.duration';
var METRIC_RPC_CLIENT_REQUEST_SIZE = 'rpc.client.request.size';
var METRIC_RPC_CLIENT_REQUESTS_PER_RPC = 'rpc.client.requests_per_rpc';
var METRIC_RPC_CLIENT_RESPONSE_SIZE = 'rpc.client.response.size';
var METRIC_RPC_CLIENT_RESPONSES_PER_RPC = 'rpc.client.responses_per_rpc';
var METRIC_RPC_SERVER_DURATION = 'rpc.server.duration';
var METRIC_RPC_SERVER_REQUEST_SIZE = 'rpc.server.request.size';
var METRIC_RPC_SERVER_REQUESTS_PER_RPC = 'rpc.server.requests_per_rpc';
var METRIC_RPC_SERVER_RESPONSE_SIZE = 'rpc.server.response.size';
var METRIC_RPC_SERVER_RESPONSES_PER_RPC = 'rpc.server.responses_per_rpc';
var METRIC_SYSTEM_CPU_FREQUENCY = 'system.cpu.frequency';
var METRIC_SYSTEM_CPU_LOGICAL_COUNT = 'system.cpu.logical.count';
var METRIC_SYSTEM_CPU_PHYSICAL_COUNT = 'system.cpu.physical.count';
var METRIC_SYSTEM_CPU_TIME = 'system.cpu.time';
var METRIC_SYSTEM_CPU_UTILIZATION = 'system.cpu.utilization';
var METRIC_SYSTEM_DISK_IO = 'system.disk.io';
var METRIC_SYSTEM_DISK_IO_TIME = 'system.disk.io_time';
var METRIC_SYSTEM_DISK_MERGED = 'system.disk.merged';
var METRIC_SYSTEM_DISK_OPERATION_TIME = 'system.disk.operation_time';
var METRIC_SYSTEM_DISK_OPERATIONS = 'system.disk.operations';
var METRIC_SYSTEM_FILESYSTEM_USAGE = 'system.filesystem.usage';
var METRIC_SYSTEM_FILESYSTEM_UTILIZATION = 'system.filesystem.utilization';
var METRIC_SYSTEM_LINUX_MEMORY_AVAILABLE = 'system.linux.memory.available';
var METRIC_SYSTEM_LINUX_MEMORY_SLAB_USAGE = 'system.linux.memory.slab.usage';
var METRIC_SYSTEM_MEMORY_LIMIT = 'system.memory.limit';
var METRIC_SYSTEM_MEMORY_SHARED = 'system.memory.shared';
var METRIC_SYSTEM_MEMORY_USAGE = 'system.memory.usage';
var METRIC_SYSTEM_MEMORY_UTILIZATION = 'system.memory.utilization';
var METRIC_SYSTEM_NETWORK_CONNECTIONS = 'system.network.connections';
var METRIC_SYSTEM_NETWORK_DROPPED = 'system.network.dropped';
var METRIC_SYSTEM_NETWORK_ERRORS = 'system.network.errors';
var METRIC_SYSTEM_NETWORK_IO = 'system.network.io';
var METRIC_SYSTEM_NETWORK_PACKETS = 'system.network.packets';
var METRIC_SYSTEM_PAGING_FAULTS = 'system.paging.faults';
var METRIC_SYSTEM_PAGING_OPERATIONS = 'system.paging.operations';
var METRIC_SYSTEM_PAGING_USAGE = 'system.paging.usage';
var METRIC_SYSTEM_PAGING_UTILIZATION = 'system.paging.utilization';
var METRIC_SYSTEM_PROCESS_COUNT = 'system.process.count';
var METRIC_SYSTEM_PROCESS_CREATED = 'system.process.created';
var METRIC_V8JS_GC_DURATION = 'v8js.gc.duration';
var METRIC_V8JS_HEAP_SPACE_AVAILABLE_SIZE = 'v8js.heap.space.available_size';
var METRIC_V8JS_HEAP_SPACE_PHYSICAL_SIZE = 'v8js.heap.space.physical_size';
var METRIC_V8JS_MEMORY_HEAP_LIMIT = 'v8js.memory.heap.limit';
var METRIC_V8JS_MEMORY_HEAP_USED = 'v8js.memory.heap.used'; //# sourceMappingURL=experimental_metrics.js.map
}),

};

//# sourceMappingURL=a7dda_%40opentelemetry_semantic-conventions_build_esm_0b9b473b._.js.map