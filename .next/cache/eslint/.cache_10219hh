[{"/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/app/layout.tsx": "1", "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/app/page.tsx": "2"}, {"size": 689, "mtime": 1753682548797, "results": "3", "hashOfConfig": "4"}, {"size": 3990, "mtime": 1753682548800, "results": "5", "hashOfConfig": "4"}, {"filePath": "6", "messages": "7", "suppressedMessages": "8", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kjtxg9", {"filePath": "9", "messages": "10", "suppressedMessages": "11", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/app/layout.tsx", [], [], "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/app/page.tsx", [], []]