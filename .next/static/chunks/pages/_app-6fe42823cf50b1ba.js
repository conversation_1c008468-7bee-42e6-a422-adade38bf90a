!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="0e438eaa-c86a-41fe-9a23-9ea2c8f9f188",e._sentryDebugIdIdentifier="sentry-dbid-0e438eaa-c86a-41fe-9a23-9ea2c8f9f188")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{1824:(e,t,n)=>{"use strict";let r,i,s,a,o,l,c,u,d,h,p,f,m,g,y,_,v,S,b,E,w,k;function T(e){let t,n=e[0],r=1;for(;r<e.length;){let i=e[r],s=e[r+1];if(r+=2,("optionalAccess"===i||"optionalCall"===i)&&null==n)return;"access"===i||"optionalAccess"===i?(t=n,n=s(n)):("call"===i||"optionalCall"===i)&&(n=s((...e)=>n.call(t,...e)),t=void 0)}return n}let x="8.46.0",I=globalThis;function C(e,t,n){let r=n||I,i=r.__SENTRY__=r.__SENTRY__||{},s=i[x]=i[x]||{};return s[e]||(s[e]=t())}function M(e,t,n=[t],r="npm"){let i=e._metadata||{};i.sdk||(i.sdk={name:`sentry.javascript.${t}`,packages:n.map(e=>({name:`${r}:@sentry/${e}`,version:x})),version:x}),e._metadata=i}let A="production";function R(){return O(I),I}function O(e){let t=e.__SENTRY__=e.__SENTRY__||{};return t.version=t.version||x,t[x]=t[x]||{}}let D=Object.prototype.toString;function N(e){switch(D.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return H(e,Error)}}function L(e,t){return D.call(e)===`[object ${t}]`}function P(e){return L(e,"ErrorEvent")}function $(e){return L(e,"DOMError")}function j(e){return L(e,"String")}function F(e){return"object"==typeof e&&null!==e&&"__sentry_template_string__"in e&&"__sentry_template_values__"in e}function U(e){return null===e||F(e)||"object"!=typeof e&&"function"!=typeof e}function B(e){return L(e,"Object")}function W(e){return"undefined"!=typeof Event&&H(e,Event)}function z(e){return!!(e&&e.then&&"function"==typeof e.then)}function H(e,t){try{return e instanceof t}catch(e){return!1}}function q(e){return!!("object"==typeof e&&null!==e&&(e.__isVue||e._isVue))}function V(e,t={}){if(!e)return"<unknown>";try{let n,r=e,i=[],s=0,a=0,o=Array.isArray(t)?t:t.keyAttrs,l=!Array.isArray(t)&&t.maxStringLength||80;for(;r&&s++<5&&(n=function(e,t){let n=[];if(!e||!e.tagName)return"";if(I.HTMLElement&&e instanceof HTMLElement&&e.dataset){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}n.push(e.tagName.toLowerCase());let r=t&&t.length?t.filter(t=>e.getAttribute(t)).map(t=>[t,e.getAttribute(t)]):null;if(r&&r.length)r.forEach(e=>{n.push(`[${e[0]}="${e[1]}"]`)});else{e.id&&n.push(`#${e.id}`);let t=e.className;if(t&&j(t))for(let e of t.split(/\s+/))n.push(`.${e}`)}for(let t of["aria-label","type","name","title","alt"]){let r=e.getAttribute(t);r&&n.push(`[${t}="${r}"]`)}return n.join("")}(r,o),"html"!==n&&(!(s>1)||!(a+3*i.length+n.length>=l)));)i.push(n),a+=n.length,r=r.parentNode;return i.reverse().join(" > ")}catch(e){return"<unknown>"}}function Y(){try{return I.document.location.href}catch(e){return""}}function J(e){if(!I.HTMLElement)return null;let t=e;for(let e=0;e<5&&t;e++){if(t instanceof HTMLElement){if(t.dataset.sentryComponent)return t.dataset.sentryComponent;if(t.dataset.sentryElement)return t.dataset.sentryElement}t=t.parentNode}return null}let G=["debug","info","warn","error","log","assert","trace"],K={};function X(e){if(!("console"in I))return e();let t=I.console,n={},r=Object.keys(K);r.forEach(e=>{let r=K[e];n[e]=t[e],t[e]=r});try{return e()}finally{r.forEach(e=>{t[e]=n[e]})}}let Q=C("logger",function(){let e=!1,t={enable:()=>{e=!0},disable:()=>{e=!1},isEnabled:()=>e};return G.forEach(e=>{t[e]=()=>void 0}),t});function Z(e,t=0){return"string"!=typeof e||0===t||e.length<=t?e:`${e.slice(0,t)}...`}function ee(e,t){if(!Array.isArray(e))return"";let n=[];for(let t=0;t<e.length;t++){let r=e[t];try{q(r)?n.push("[VueViewModel]"):n.push(String(r))}catch(e){n.push("[value cannot be serialized]")}}return n.join(t)}function et(e,t=[],n=!1){return t.some(t=>(function(e,t,n=!1){return!!j(e)&&(L(t,"RegExp")?t.test(e):!!j(t)&&(n?e===t:e.includes(t)))})(e,t,n))}function en(e,t,n){if(!(t in e))return;let r=e[t],i=n(r);"function"==typeof i&&ei(i,r);try{e[t]=i}catch(e){}}function er(e,t,n){try{Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0})}catch(e){}}function ei(e,t){try{let n=t.prototype||{};e.prototype=t.prototype=n,er(e,"__sentry_original__",t)}catch(e){}}function es(e){return e.__sentry_original__}function ea(e){if(N(e))return{message:e.message,name:e.name,stack:e.stack,...el(e)};if(!W(e))return e;{let t={type:e.type,target:eo(e.target),currentTarget:eo(e.currentTarget),...el(e)};return"undefined"!=typeof CustomEvent&&H(e,CustomEvent)&&(t.detail=e.detail),t}}function eo(e){try{return"undefined"!=typeof Element&&H(e,Element)?V(e):Object.prototype.toString.call(e)}catch(e){return"<unknown>"}}function el(e){if("object"!=typeof e||null===e)return{};{let t={};for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}}function ec(e){return function e(t,n){if(function(e){if(!B(e))return!1;try{let t=Object.getPrototypeOf(e).constructor.name;return!t||"Object"===t}catch(e){return!0}}(t)){let r=n.get(t);if(void 0!==r)return r;let i={};for(let r of(n.set(t,i),Object.getOwnPropertyNames(t)))void 0!==t[r]&&(i[r]=e(t[r],n));return i}if(Array.isArray(t)){let r=n.get(t);if(void 0!==r)return r;let i=[];return n.set(t,i),t.forEach(t=>{i.push(e(t,n))}),i}return t}(e,new Map)}function eu(){return Date.now()/1e3}let ed=function(){let{performance:e}=I;if(!e||!e.now)return eu;let t=Date.now()-e.now(),n=void 0==e.timeOrigin?t:e.timeOrigin;return()=>(n+e.now())/1e3}(),eh=(()=>{let{performance:e}=I;if(!e||!e.now)return;let t=e.now(),n=Date.now(),r=e.timeOrigin?Math.abs(e.timeOrigin+t-n):36e5,i=e.timing&&e.timing.navigationStart,s="number"==typeof i?Math.abs(i+t-n):36e5;if(r<36e5||s<36e5)if(r<=s)return e.timeOrigin;else return i;return n})();function ep(){let e=I.crypto||I.msCrypto,t=()=>16*Math.random();try{if(e&&e.randomUUID)return e.randomUUID().replace(/-/g,"");e&&e.getRandomValues&&(t=()=>{let t=new Uint8Array(1);return e.getRandomValues(t),t[0]})}catch(e){}return"10000000100040008000100000000000".replace(/[018]/g,e=>(e^(15&t())>>e/4).toString(16))}function ef(e){return e.exception&&e.exception.values?e.exception.values[0]:void 0}function em(e,t,n){let r=e.exception=e.exception||{},i=r.values=r.values||[],s=i[0]=i[0]||{};s.value||(s.value=t||""),s.type||(s.type=n||"Error")}function eg(e,t){let n=ef(e);if(!n)return;let r=n.mechanism;if(n.mechanism={type:"generic",handled:!0,...r,...t},t&&"data"in t){let e={...r&&r.data,...t.data};n.mechanism.data=e}}function ey(e){if(function(e){try{return e.__sentry_captured__}catch(e){}}(e))return!0;try{er(e,"__sentry_captured__",!0)}catch(e){}return!1}function e_(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),e.did||t.did||(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||ed(),t.abnormal_mechanism&&(e.abnormal_mechanism=t.abnormal_mechanism),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=32===t.sid.length?t.sid:ep()),void 0!==t.init&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),"number"==typeof t.started&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if("number"==typeof t.duration)e.duration=t.duration;else{let t=e.timestamp-e.started;e.duration=t>=0?t:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),"number"==typeof t.errors&&(e.errors=t.errors),t.status&&(e.status=t.status)}function ev(){return ep().substring(16)}function eS(e,t,n=2){if(!t||"object"!=typeof t||n<=0)return t;if(e&&t&&0===Object.keys(t).length)return e;let r={...e};for(let e in t)Object.prototype.hasOwnProperty.call(t,e)&&(r[e]=eS(r[e],t[e],n-1));return r}let eb="_sentrySpan";function eE(e,t){t?er(e,eb,t):delete e[eb]}class ew{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:ep(),spanId:ev()}}clone(){let e=new ew;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._requestSession=this._requestSession,e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,eE(e,this[eb]),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&e_(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(e){return this._requestSession=e,this}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,t){return this._tags={...this._tags,[e]:t},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,t){return this._extra={...this._extra,[e]:t},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,t){return null===t?delete this._contexts[e]:this._contexts[e]=t,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;let t="function"==typeof e?e(this):e,[n,r]=t instanceof ek?[t.getScopeData(),t.getRequestSession()]:B(t)?[e,e.requestSession]:[],{tags:i,extra:s,user:a,contexts:o,level:l,fingerprint:c=[],propagationContext:u}=n||{};return this._tags={...this._tags,...i},this._extra={...this._extra,...s},this._contexts={...this._contexts,...o},a&&Object.keys(a).length&&(this._user=a),l&&(this._level=l),c.length&&(this._fingerprint=c),u&&(this._propagationContext=u),r&&(this._requestSession=r),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._session=void 0,eE(this,void 0),this._attachments=[],this.setPropagationContext({traceId:ep()}),this._notifyScopeListeners(),this}addBreadcrumb(e,t){let n="number"==typeof t?t:100;if(n<=0)return this;let r={timestamp:eu(),...e},i=this._breadcrumbs;return i.push(r),this._breadcrumbs=i.length>n?i.slice(-n):i,this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:this[eb]}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=eS(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext={spanId:ev(),...e},this}getPropagationContext(){return this._propagationContext}captureException(e,t){let n=t&&t.event_id?t.event_id:ep();if(!this._client)return Q.warn("No client configured on scope - will not capture exception!"),n;let r=Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:r,...t,event_id:n},this),n}captureMessage(e,t,n){let r=n&&n.event_id?n.event_id:ep();if(!this._client)return Q.warn("No client configured on scope - will not capture message!"),r;let i=Error(e);return this._client.captureMessage(e,t,{originalException:e,syntheticException:i,...n,event_id:r},this),r}captureEvent(e,t){let n=t&&t.event_id?t.event_id:ep();return this._client?this._client.captureEvent(e,{...t,event_id:n},this):Q.warn("No client configured on scope - will not capture event!"),n}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}let ek=ew;class eT{constructor(e,t){let n,r;n=e||new ek,r=t||new ek,this._stack=[{scope:n}],this._isolationScope=r}withScope(e){let t,n=this._pushScope();try{t=e(n)}catch(e){throw this._popScope(),e}return z(t)?t.then(e=>(this._popScope(),e),e=>{throw this._popScope(),e}):(this._popScope(),t)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){let e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function ex(){let e=O(R());return e.stack=e.stack||new eT(C("defaultCurrentScope",()=>new ek),C("defaultIsolationScope",()=>new ek))}function eI(e){return ex().withScope(e)}function eC(e,t){let n=ex();return n.withScope(()=>(n.getStackTop().scope=e,t(e)))}function eM(e){return ex().withScope(()=>e(ex().getIsolationScope()))}function eA(e){let t=O(e);return t.acs?t.acs:{withIsolationScope:eM,withScope:eI,withSetScope:eC,withSetIsolationScope:(e,t)=>eM(t),getCurrentScope:()=>ex().getScope(),getIsolationScope:()=>ex().getIsolationScope()}}function eR(){return eA(R()).getCurrentScope()}function eO(){return eA(R()).getIsolationScope()}function eD(...e){let t=eA(R());if(2===e.length){let[n,r]=e;return n?t.withSetScope(n,r):t.withScope(r)}return t.withScope(e[0])}function eN(){return eR().getClient()}function eL(e){return new e$(t=>{t(e)})}function eP(e){return new e$((t,n)=>{n(e)})}!function(e){e[e.PENDING=0]="PENDING",e[e.RESOLVED=1]="RESOLVED",e[e.REJECTED=2]="REJECTED"}(nQ||(nQ={}));class e${constructor(e){e$.prototype.__init.call(this),e$.prototype.__init2.call(this),e$.prototype.__init3.call(this),e$.prototype.__init4.call(this),this._state=nQ.PENDING,this._handlers=[];try{e(this._resolve,this._reject)}catch(e){this._reject(e)}}then(e,t){return new e$((n,r)=>{this._handlers.push([!1,t=>{if(e)try{n(e(t))}catch(e){r(e)}else n(t)},e=>{if(t)try{n(t(e))}catch(e){r(e)}else r(e)}]),this._executeHandlers()})}catch(e){return this.then(e=>e,e)}finally(e){return new e$((t,n)=>{let r,i;return this.then(t=>{i=!1,r=t,e&&e()},t=>{i=!0,r=t,e&&e()}).then(()=>{if(i)return void n(r);t(r)})})}__init(){this._resolve=e=>{this._setResult(nQ.RESOLVED,e)}}__init2(){this._reject=e=>{this._setResult(nQ.REJECTED,e)}}__init3(){this._setResult=(e,t)=>{if(this._state===nQ.PENDING){if(z(t))return void t.then(this._resolve,this._reject);this._state=e,this._value=t,this._executeHandlers()}}}__init4(){this._executeHandlers=()=>{if(this._state===nQ.PENDING)return;let e=this._handlers.slice();this._handlers=[],e.forEach(e=>{e[0]||(this._state===nQ.RESOLVED&&e[1](this._value),this._state===nQ.REJECTED&&e[2](this._value),e[0]=!0)})}}}let ej=/\(error: (.*)\)/,eF=/captureMessage|captureException/;function eU(...e){let t=e.sort((e,t)=>e[0]-t[0]).map(e=>e[1]);return(e,n=0,r=0)=>{let i=[],s=e.split("\n");for(let e=n;e<s.length;e++){let n=s[e];if(n.length>1024)continue;let a=ej.test(n)?n.replace(ej,"$1"):n;if(!a.match(/\S*Error: /)){for(let e of t){let t=e(a);if(t){i.push(t);break}}if(i.length>=50+r)break}}var a=i.slice(r);if(!a.length)return[];let o=Array.from(a);return/sentryWrapped/.test(eB(o).function||"")&&o.pop(),o.reverse(),eF.test(eB(o).function||"")&&(o.pop(),eF.test(eB(o).function||"")&&o.pop()),o.slice(0,50).map(e=>({...e,filename:e.filename||eB(o).filename,function:e.function||"?"}))}}function eB(e){return e[e.length-1]||{}}let eW="<anonymous>";function ez(e){try{if(!e||"function"!=typeof e)return eW;return e.name||eW}catch(e){return eW}}function eH(e){let t=e.exception;if(t){let e=[];try{return t.values.forEach(t=>{t.stacktrace.frames&&e.push(...t.stacktrace.frames)}),e}catch(e){}}}function eq(e,t=100,n=Infinity){try{return function e(t,n,r=Infinity,i=Infinity,s=function(){let e="function"==typeof WeakSet,t=e?new WeakSet:[];return[function(n){if(e)return!!t.has(n)||(t.add(n),!1);for(let e=0;e<t.length;e++)if(t[e]===n)return!0;return t.push(n),!1},function(n){if(e)t.delete(n);else for(let e=0;e<t.length;e++)if(t[e]===n){t.splice(e,1);break}}]}()){let[a,o]=s;if(null==n||["boolean","string"].includes(typeof n)||"number"==typeof n&&Number.isFinite(n))return n;let l=function(e,t){try{if("domain"===e&&t&&"object"==typeof t&&t._events)return"[Domain]";if("domainEmitter"===e)return"[DomainEmitter]";if("undefined"!=typeof global&&t===global)return"[Global]";if("undefined"!=typeof window&&t===window)return"[Window]";if("undefined"!=typeof document&&t===document)return"[Document]";if(q(t))return"[VueViewModel]";if(B(t)&&"nativeEvent"in t&&"preventDefault"in t&&"stopPropagation"in t)return"[SyntheticEvent]";if("number"==typeof t&&!Number.isFinite(t))return`[${t}]`;if("function"==typeof t)return`[Function: ${ez(t)}]`;if("symbol"==typeof t)return`[${String(t)}]`;if("bigint"==typeof t)return`[BigInt: ${String(t)}]`;let n=function(e){let t=Object.getPrototypeOf(e);return t?t.constructor.name:"null prototype"}(t);if(/^HTML(\w*)Element$/.test(n))return`[HTMLElement: ${n}]`;return`[object ${n}]`}catch(e){return`**non-serializable** (${e})`}}(t,n);if(!l.startsWith("[object "))return l;if(n.__sentry_skip_normalization__)return n;let c="number"==typeof n.__sentry_override_normalization_depth__?n.__sentry_override_normalization_depth__:r;if(0===c)return l.replace("object ","");if(a(n))return"[Circular ~]";if(n&&"function"==typeof n.toJSON)try{let t=n.toJSON();return e("",t,c-1,i,s)}catch(e){}let u=Array.isArray(n)?[]:{},d=0,h=ea(n);for(let t in h){if(!Object.prototype.hasOwnProperty.call(h,t))continue;if(d>=i){u[t]="[MaxProperties ~]";break}let n=h[t];u[t]=e(t,n,c-1,i,s),d++}return o(n),u}("",e,t,n)}catch(e){return{ERROR:`**non-serializable** (${e})`}}}let eV="sentry.source",eY="sentry.sample_rate",eJ="sentry.op",eG="sentry.origin",eK="sentry.idle_span_finish_reason",eX="sentry.measurement_unit",eQ="sentry.measurement_value",eZ="sentry.exclusive_time",e0="sentry-",e1=/^sentry-/;function e2(e){let t=e3(e);if(!t)return;let n=Object.entries(t).reduce((e,[t,n])=>(t.match(e1)&&(e[t.slice(e0.length)]=n),e),{});return Object.keys(n).length>0?n:void 0}function e3(e){if(e&&(j(e)||Array.isArray(e)))return Array.isArray(e)?e.reduce((e,t)=>(Object.entries(e5(t)).forEach(([t,n])=>{e[t]=n}),e),{}):e5(e)}function e5(e){return e.split(",").map(e=>e.split("=").map(e=>decodeURIComponent(e.trim()))).reduce((e,[t,n])=>(t&&n&&(e[t]=n),e),{})}function e4(e){if("boolean"==typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;let t=eN(),n=e||t&&t.getOptions();return!!n&&(n.enableTracing||"tracesSampleRate"in n||"tracesSampler"in n)}function e8(e){let t=e._sentryMetrics;if(!t)return;let n={};for(let[,[e,r]]of t)(n[e]||(n[e]=[])).push(ec(r));return n}function e6(e,t){e.setAttribute("http.response.status_code",t);let n=function(e){if(e<400&&e>=100)return{code:1};if(e>=400&&e<500)switch(e){case 401:return{code:2,message:"unauthenticated"};case 403:return{code:2,message:"permission_denied"};case 404:return{code:2,message:"not_found"};case 409:return{code:2,message:"already_exists"};case 413:return{code:2,message:"failed_precondition"};case 429:return{code:2,message:"resource_exhausted"};case 499:return{code:2,message:"cancelled"};default:return{code:2,message:"invalid_argument"}}if(e>=500&&e<600)switch(e){case 501:return{code:2,message:"unimplemented"};case 503:return{code:2,message:"unavailable"};case 504:return{code:2,message:"deadline_exceeded"};default:return{code:2,message:"internal_error"}}return{code:2,message:"unknown_error"}}(t);"unknown_error"!==n.message&&e.setStatus(n)}let e7=RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function e9(e=ep(),t=ev(),n){let r="";return void 0!==n&&(r=n?"-1":"-0"),`${e}-${t}${r}`}let te=!1;function tt(e){return"number"==typeof e?tn(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?tn(e.getTime()):ed()}function tn(e){return e>0x2540be3ff?e/1e3:e}function tr(e){if("function"==typeof e.getSpanJSON)return e.getSpanJSON();try{var t;let{spanId:n,traceId:r}=e.spanContext();if((t=e).attributes&&t.startTime&&t.name&&t.endTime&&t.status){let{attributes:t,startTime:i,name:s,endTime:a,parentSpanId:o,status:l}=e;return ec({span_id:n,trace_id:r,data:t,description:s,parent_span_id:o,start_timestamp:tt(i),timestamp:tt(a)||void 0,status:ts(l),op:t[eJ],origin:t[eG],_metrics_summary:e8(e)})}return{span_id:n,trace_id:r}}catch(e){return{}}}function ti(e){let{traceFlags:t}=e.spanContext();return 1===t}function ts(e){if(e&&0!==e.code)return 1===e.code?"ok":e.message||"unknown_error"}let ta="_sentryChildSpans",to="_sentryRootSpan";function tl(e,t){let n=e[to]||e;er(t,to,n),e[ta]?e[ta].add(t):er(e,ta,new Set([t]))}function tc(e){let t=new Set;return!function e(n){if(!t.has(n)&&ti(n))for(let r of(t.add(n),n[ta]?Array.from(n[ta]):[]))e(r)}(e),Array.from(t)}function tu(e){return e[to]||e}function td(){let e=eA(R());return e.getActiveSpan?e.getActiveSpan():eR()[eb]}function th(){te||(X(()=>{console.warn("[Sentry] Deprecation warning: Returning null from `beforeSendSpan` will be disallowed from SDK version 9.0.0 onwards. The callback will only support mutating spans. To drop certain spans, configure the respective integrations directly.")}),te=!0)}let tp="_frozenDsc";function tf(e,t){let n=t.getOptions(),{publicKey:r}=t.getDsn()||{},i=ec({environment:n.environment||A,release:n.release,public_key:r,trace_id:e});return t.emit("createDsc",i),i}function tm(e,t){let n=t.getPropagationContext();return n.dsc||tf(n.traceId,e)}function tg(e){let t=eN();if(!t)return{};let n=tu(e),r=n[tp];if(r)return r;let i=n.spanContext().traceState,s=i&&i.get("sentry.dsc"),a=s&&e2(s);if(a)return a;let o=tf(e.spanContext().traceId,t),l=tr(n),c=l.data||{},u=c[eY];null!=u&&(o.sample_rate=`${u}`);let d=c[eV],h=l.description;return"url"!==d&&h&&(o.transaction=h),e4()&&(o.sampled=String(ti(n))),t.emit("createDsc",o,n),o}function ty(e,t){let{extra:n,tags:r,user:i,contexts:s,level:a,sdkProcessingMetadata:o,breadcrumbs:l,fingerprint:c,eventProcessors:u,attachments:d,propagationContext:h,transactionName:p,span:f}=t;t_(e,"extra",n),t_(e,"tags",r),t_(e,"user",i),t_(e,"contexts",s),e.sdkProcessingMetadata=eS(e.sdkProcessingMetadata,o,2),a&&(e.level=a),p&&(e.transactionName=p),f&&(e.span=f),l.length&&(e.breadcrumbs=[...e.breadcrumbs,...l]),c.length&&(e.fingerprint=[...e.fingerprint,...c]),u.length&&(e.eventProcessors=[...e.eventProcessors,...u]),d.length&&(e.attachments=[...e.attachments,...d]),e.propagationContext={...e.propagationContext,...h}}function t_(e,t,n){e[t]=eS(e[t],n,1)}function tv(e,t,n,a,o,l){var c,u,d,h,p,f;let{normalizeDepth:m=3,normalizeMaxBreadth:g=1e3}=e,y={...t,event_id:t.event_id||n.event_id||ep(),timestamp:t.timestamp||eu()},_=n.integrations||e.integrations.map(e=>e.name);(function(e,t){let{environment:n,release:r,dist:i,maxValueLength:s=250}=t;e.environment=e.environment||n||A,!e.release&&r&&(e.release=r),!e.dist&&i&&(e.dist=i),e.message&&(e.message=Z(e.message,s));let a=e.exception&&e.exception.values&&e.exception.values[0];a&&a.value&&(a.value=Z(a.value,s));let o=e.request;o&&o.url&&(o.url=Z(o.url,s))})(y,e),c=y,(u=_).length>0&&(c.sdk=c.sdk||{},c.sdk.integrations=[...c.sdk.integrations||[],...u]),o&&o.emit("applyFrameMetadata",t),void 0===t.type&&function(e,t){let n=function(e){let t=I._sentryDebugIds;if(!t)return{};let n=Object.keys(t);return s&&n.length===i?s:(i=n.length,s=n.reduce((n,i)=>{r||(r={});let s=r[i];if(s)n[s[0]]=s[1];else{let s=e(i);for(let e=s.length-1;e>=0;e--){let a=s[e],o=a&&a.filename,l=t[i];if(o&&l){n[o]=l,r[i]=[o,l];break}}}return n},{}))}(t);try{e.exception.values.forEach(e=>{e.stacktrace.frames.forEach(e=>{n&&e.filename&&(e.debug_id=n[e.filename])})})}catch(e){}}(y,e.stackParser);let v=function(e,t){if(!t)return e;let n=e?e.clone():new ek;return n.update(t),n}(a,n.captureContext);n.mechanism&&eg(y,n.mechanism);let S=o?o.getEventProcessors():[],b=C("globalScope",()=>new ek).getScopeData();l&&ty(b,l.getScopeData()),v&&ty(b,v.getScopeData());let E=[...n.attachments||[],...b.attachments];E.length&&(n.attachments=E);let{fingerprint:w,span:k,breadcrumbs:T,sdkProcessingMetadata:x}=b;return function(e,t){let{extra:n,tags:r,user:i,contexts:s,level:a,transactionName:o}=t,l=ec(n);l&&Object.keys(l).length&&(e.extra={...l,...e.extra});let c=ec(r);c&&Object.keys(c).length&&(e.tags={...c,...e.tags});let u=ec(i);u&&Object.keys(u).length&&(e.user={...u,...e.user});let d=ec(s);d&&Object.keys(d).length&&(e.contexts={...d,...e.contexts}),a&&(e.level=a),o&&"transaction"!==e.type&&(e.transaction=o)}(y,b),k&&function(e,t){e.contexts={trace:function(e){let{spanId:t,traceId:n,isRemote:r}=e.spanContext();return ec({parent_span_id:r?t:tr(e).parent_span_id,span_id:r?ev():t,trace_id:n})}(t),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:tg(t),...e.sdkProcessingMetadata};let n=tr(tu(t)).description;n&&!e.transaction&&"transaction"===e.type&&(e.transaction=n)}(y,k),d=y,h=w,d.fingerprint=d.fingerprint?Array.isArray(d.fingerprint)?d.fingerprint:[d.fingerprint]:[],h&&(d.fingerprint=d.fingerprint.concat(h)),d.fingerprint&&!d.fingerprint.length&&delete d.fingerprint,function(e,t){let n=[...e.breadcrumbs||[],...t];e.breadcrumbs=n.length?n:void 0}(y,T),p=y,f=x,p.sdkProcessingMetadata={...p.sdkProcessingMetadata,...f},(function e(t,n,r,i=0){return new e$((s,a)=>{let o=t[i];if(null===n||"function"!=typeof o)s(n);else{let l=o({...n},r);z(l)?l.then(n=>e(t,n,r,i+1).then(s)).then(null,a):e(t,l,r,i+1).then(s).then(null,a)}})})([...S,...b.eventProcessors],y,n).then(e=>(e&&function(e){let t={};try{e.exception.values.forEach(e=>{e.stacktrace.frames.forEach(e=>{e.debug_id&&(e.abs_path?t[e.abs_path]=e.debug_id:e.filename&&(t[e.filename]=e.debug_id),delete e.debug_id)})})}catch(e){}if(0===Object.keys(t).length)return;e.debug_meta=e.debug_meta||{},e.debug_meta.images=e.debug_meta.images||[];let n=e.debug_meta.images;Object.entries(t).forEach(([e,t])=>{n.push({type:"sourcemap",code_file:e,debug_id:t})})}(e),"number"==typeof m&&m>0)?function(e,t,n){if(!e)return null;let r={...e,...e.breadcrumbs&&{breadcrumbs:e.breadcrumbs.map(e=>({...e,...e.data&&{data:eq(e.data,t,n)}}))},...e.user&&{user:eq(e.user,t,n)},...e.contexts&&{contexts:eq(e.contexts,t,n)},...e.extra&&{extra:eq(e.extra,t,n)}};return e.contexts&&e.contexts.trace&&r.contexts&&(r.contexts.trace=e.contexts.trace,e.contexts.trace.data&&(r.contexts.trace.data=eq(e.contexts.trace.data,t,n))),e.spans&&(r.spans=e.spans.map(e=>({...e,...e.data&&{data:eq(e.data,t,n)}}))),e.contexts&&e.contexts.flags&&r.contexts&&(r.contexts.flags=eq(e.contexts.flags,3,n)),r}(e,m,g):e)}let tS=["user","level","extra","contexts","tags","fingerprint","requestSession","propagationContext"];function tb(e,t){return eR().captureEvent(e,t)}function tE(e,t){eO().setContext(e,t)}function tw(e){eO().addEventProcessor(e)}function tk(e){let t=eN(),n=eO(),r=eR(),{release:i,environment:s=A}=t&&t.getOptions()||{},{userAgent:a}=I.navigator||{},o=function(e){let t=ed(),n={sid:ep(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>{var e;return e=n,ec({sid:`${e.sid}`,init:e.init,started:new Date(1e3*e.started).toISOString(),timestamp:new Date(1e3*e.timestamp).toISOString(),status:e.status,errors:e.errors,did:"number"==typeof e.did||"string"==typeof e.did?`${e.did}`:void 0,duration:e.duration,abnormal_mechanism:e.abnormal_mechanism,attrs:{release:e.release,environment:e.environment,ip_address:e.ipAddress,user_agent:e.userAgent}})}};return e&&e_(n,e),n}({release:i,environment:s,user:r.getUser()||n.getUser(),...a&&{userAgent:a},...e}),l=n.getSession();return l&&"ok"===l.status&&e_(l,{status:"exited"}),tT(),n.setSession(o),r.setSession(o),o}function tT(){let e,t=eO(),n=eR(),r=n.getSession()||t.getSession();r&&(e={},"ok"===r.status&&(e={status:"exited"}),e_(r,e)),tx(),t.setSession(),n.setSession()}function tx(){let e=eO(),t=eR(),n=eN(),r=t.getSession()||e.getSession();r&&n&&n.captureSession(r)}function tI(e=!1){if(e)return void tT();tx()}let tC=[];function tM(e,t){for(let n of t)n&&n.afterAllSetup&&n.afterAllSetup(e)}function tA(e,t,n){if(!n[t.name]){if(n[t.name]=t,-1===tC.indexOf(t.name)&&"function"==typeof t.setupOnce&&(t.setupOnce(),tC.push(t.name)),t.setup&&"function"==typeof t.setup&&t.setup(e),"function"==typeof t.preprocessEvent){let n=t.preprocessEvent.bind(t);e.on("preprocessEvent",(t,r)=>n(t,r,e))}if("function"==typeof t.processEvent){let n=t.processEvent.bind(t),r=Object.assign((t,r)=>n(t,r,e),{id:t.name});e.addEventProcessor(r)}}}let tR=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,"undefined is not an object (evaluating 'a.L')",'can\'t redefine non-configurable property "solana"',"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/];function tO(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch(e){}return t?function(e=[]){for(let t=e.length-1;t>=0;t--){let n=e[t];if(n&&"<anonymous>"!==n.filename&&"[native code]"!==n.filename)return n.filename||null}return null}(t):null}catch(e){return null}}let tD=new WeakMap;function tN(e,t){let n=eH(e),r=eH(t);if(!n&&!r)return!0;if(n&&!r||!n&&r||r.length!==n.length)return!1;for(let e=0;e<r.length;e++){let t=r[e],i=n[e];if(t.filename!==i.filename||t.lineno!==i.lineno||t.colno!==i.colno||t.function!==i.function)return!1}return!0}function tL(e,t){let n=e.fingerprint,r=t.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return n.join("")===r.join("")}catch(e){return!1}}function tP(e){return e.exception&&e.exception.values&&e.exception.values[0]}function t$(e){return e&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}let tj=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function tF(e,t=!1){let{host:n,path:r,pass:i,port:s,projectId:a,protocol:o,publicKey:l}=e;return`${o}://${l}${t&&i?`:${i}`:""}@${n}${s?`:${s}`:""}/${r?`${r}/`:r}${a}`}function tU(e){let t=tj.exec(e);if(!t)return void X(()=>{console.error(`Invalid Sentry Dsn: ${e}`)});let[n,r,i="",s="",a="",o=""]=t.slice(1),l="",c=o,u=c.split("/");if(u.length>1&&(l=u.slice(0,-1).join("/"),c=u.pop()),c){let e=c.match(/^\d+/);e&&(c=e[0])}return tB({host:s,pass:i,path:l,projectId:c,port:a,protocol:n,publicKey:r})}function tB(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function tW(e,t=[]){return[e,t]}function tz(e,t){for(let n of e[1]){let e=n[0].type;if(t(n,e))return!0}return!1}function tH(e){return I.__SENTRY__&&I.__SENTRY__.encodePolyfill?I.__SENTRY__.encodePolyfill(e):new TextEncoder().encode(e)}let tq={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket",raw_security:"security"};function tV(e){if(!e||!e.sdk)return;let{name:t,version:n}=e.sdk;return{name:t,version:n}}function tY(e,t,n,r){let i=e.sdkProcessingMetadata&&e.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:e.event_id,sent_at:new Date().toISOString(),...t&&{sdk:t},...!!n&&r&&{dsn:tF(r)},...i&&{trace:ec({...i})}}}class tJ extends Error{constructor(e,t="warn"){super(e),this.message=e,this.name=new.target.prototype.constructor.name,Object.setPrototypeOf(this,new.target.prototype),this.logLevel=t}}function tG(e){if("boolean"==typeof e)return Number(e);let t="string"==typeof e?parseFloat(e):e;if(!("number"!=typeof t||isNaN(t))&&!(t<0)&&!(t>1))return t}class tK{constructor(e){if(this._options=e,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn&&(this._dsn=function(e){let t="string"==typeof e?tU(e):tB(e);if(t&&1)return t}(e.dsn)),this._dsn){let t=function(e,t,n){return t||`${function(e){let t=e.protocol?`${e.protocol}:`:"",n=e.port?`:${e.port}`:"";return`${t}//${e.host}${n}${e.path?`/${e.path}`:""}/api/`}(e)}${e.projectId}/envelope/?${function(e,t){let n={sentry_version:"7"};return e.publicKey&&(n.sentry_key=e.publicKey),t&&(n.sentry_client=`${t.name}/${t.version}`),new URLSearchParams(n).toString()}(e,n)}`}(this._dsn,e.tunnel,e._metadata?e._metadata.sdk:void 0);this._transport=e.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:t})}let t=["enableTracing","tracesSampleRate","tracesSampler"].find(t=>t in e&&void 0==e[t]);t&&X(()=>{console.warn(`[Sentry] Deprecation warning: \`${t}\` is set to undefined, which leads to tracing being enabled. In v9, a value of \`undefined\` will result in tracing being disabled.`)})}captureException(e,t,n){let r=ep();if(ey(e))return r;let i={event_id:r,...t};return this._process(this.eventFromException(e,i).then(e=>this._captureEvent(e,i,n))),i.event_id}captureMessage(e,t,n,r){let i={event_id:ep(),...n},s=F(e)?e:String(e),a=U(e)?this.eventFromMessage(s,t,i):this.eventFromException(e,i);return this._process(a.then(e=>this._captureEvent(e,i,r))),i.event_id}captureEvent(e,t,n){let r=ep();if(t&&t.originalException&&ey(t.originalException))return r;let i={event_id:r,...t},s=(e.sdkProcessingMetadata||{}).capturedSpanScope;return this._process(this._captureEvent(e,i,s||n)),i.event_id}captureSession(e){"string"==typeof e.release&&(this.sendSession(e),e_(e,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){let t=this._transport;return t?(this.emit("flush"),this._isClientDoneProcessing(e).then(n=>t.flush(e).then(e=>n&&e))):eL(!0)}close(e){return this.flush(e).then(e=>(this.getOptions().enabled=!1,this.emit("close"),e))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}init(){(this._isEnabled()||this._options.integrations.some(({name:e})=>e.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(e){return this._integrations[e]}addIntegration(e){let t=this._integrations[e.name];tA(this,e,this._integrations),t||tM(this,[e])}sendEvent(e,t={}){this.emit("beforeSendEvent",e,t);let n=function(e,t,n,r){var i;let s=tV(n),a=e.type&&"replay_event"!==e.type?e.type:"event";(i=n&&n.sdk)&&(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||i.name,e.sdk.version=e.sdk.version||i.version,e.sdk.integrations=[...e.sdk.integrations||[],...i.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...i.packages||[]]);let o=tY(e,s,r,t);return delete e.sdkProcessingMetadata,tW(o,[[{type:a},e]])}(e,this._dsn,this._options._metadata,this._options.tunnel);for(let e of t.attachments||[])n=function(e,t){let[n,r]=e;return[n,[...r,t]]}(n,function(e){let t="string"==typeof e.data?tH(e.data):e.data;return[ec({type:"attachment",length:t.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType}),t]}(e));let r=this.sendEnvelope(n);r&&r.then(t=>this.emit("afterSendEvent",e,t),null)}sendSession(e){let t=function(e,t,n,r){let i=tV(n);return tW({sent_at:new Date().toISOString(),...i&&{sdk:i},...!!r&&t&&{dsn:tF(t)}},["aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e.toJSON()]])}(e,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(t)}recordDroppedEvent(e,t,n){if(this._options.sendClientReports){let r=`${e}:${t}`;this._outcomes[r]=(this._outcomes[r]||0)+("number"==typeof n?n:1)}}on(e,t){let n=this._hooks[e]=this._hooks[e]||[];return n.push(t),()=>{let e=n.indexOf(t);e>-1&&n.splice(e,1)}}emit(e,...t){let n=this._hooks[e];n&&n.forEach(e=>e(...t))}sendEnvelope(e){return(this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport)?this._transport.send(e).then(null,e=>e):eL({})}_setupIntegrations(){let{integrations:e}=this._options;this._integrations=function(e,t){let n={};return t.forEach(t=>{t&&tA(e,t,n)}),n}(this,e),tM(this,e)}_updateSessionFromEvent(e,t){let n=!1,r=!1,i=t.exception&&t.exception.values;if(i)for(let e of(r=!0,i)){let t=e.mechanism;if(t&&!1===t.handled){n=!0;break}}let s="ok"===e.status;(s&&0===e.errors||s&&n)&&(e_(e,{...n&&{status:"crashed"},errors:e.errors||Number(r||n)}),this.captureSession(e))}_isClientDoneProcessing(e){return new e$(t=>{let n=0,r=setInterval(()=>{0==this._numProcessing?(clearInterval(r),t(!0)):(n+=1,e&&n>=e&&(clearInterval(r),t(!1)))},1)})}_isEnabled(){return!1!==this.getOptions().enabled&&void 0!==this._transport}_prepareEvent(e,t,n=eR(),r=eO()){let i=this.getOptions(),s=Object.keys(this._integrations);return!t.integrations&&s.length>0&&(t.integrations=s),this.emit("preprocessEvent",e,t),e.type||r.setLastEventId(e.event_id||t.event_id),tv(i,e,t,n,this,r).then(e=>(null===e||(e.contexts={trace:function(e){let{traceId:t,spanId:n,parentSpanId:r}=e.getPropagationContext();return ec({trace_id:t,span_id:n,parent_span_id:r})}(n),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:tm(this,n),...e.sdkProcessingMetadata}),e))}_captureEvent(e,t={},n){return this._processEvent(e,t,n).then(e=>e.event_id,e=>{})}_processEvent(e,t,n){let r=this.getOptions(),{sampleRate:i}=r,s=tQ(e),a=tX(e),o=e.type||"error",l=`before send for type \`${o}\``,c=void 0===i?void 0:tG(i);if(a&&"number"==typeof c&&Math.random()>c)return this.recordDroppedEvent("sample_rate","error",e),eP(new tJ(`Discarding event because it's not included in the random sample (sampling rate = ${i})`,"log"));let u="replay_event"===o?"replay":o,d=(e.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this._prepareEvent(e,t,n,d).then(n=>{if(null===n)throw this.recordDroppedEvent("event_processor",u,e),new tJ("An event processor returned `null`, will not send event.","log");return t.data&&!0===t.data.__sentry__?n:function(e,t){let n=`${t} must return \`null\` or a valid event.`;if(z(e))return e.then(e=>{if(!B(e)&&null!==e)throw new tJ(n);return e},e=>{throw new tJ(`${t} rejected with ${e}`)});if(!B(e)&&null!==e)throw new tJ(n);return e}(function(e,t,n,r){let{beforeSend:i,beforeSendTransaction:s,beforeSendSpan:a}=t;if(tX(n)&&i)return i(n,r);if(tQ(n)){if(n.spans&&a){let t=[];for(let r of n.spans){let n=a(r);n?t.push(n):(th(),e.recordDroppedEvent("before_send","span"))}n.spans=t}if(s){if(n.spans){let e=n.spans.length;n.sdkProcessingMetadata={...n.sdkProcessingMetadata,spanCountBeforeProcessing:e}}return s(n,r)}}return n}(this,r,n,t),l)}).then(r=>{if(null===r){if(this.recordDroppedEvent("before_send",u,e),s){let t=1+(e.spans||[]).length;this.recordDroppedEvent("before_send","span",t)}throw new tJ(`${l} returned \`null\`, will not send event.`,"log")}let i=n&&n.getSession();if(!s&&i&&this._updateSessionFromEvent(i,r),s){let e=(r.sdkProcessingMetadata&&r.sdkProcessingMetadata.spanCountBeforeProcessing||0)-(r.spans?r.spans.length:0);e>0&&this.recordDroppedEvent("before_send","span",e)}let a=r.transaction_info;return s&&a&&r.transaction!==e.transaction&&(r.transaction_info={...a,source:"custom"}),this.sendEvent(r,t),r}).then(null,e=>{if(e instanceof tJ)throw e;throw this.captureException(e,{data:{__sentry__:!0},originalException:e}),new tJ(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${e}`)})}_process(e){this._numProcessing++,e.then(e=>(this._numProcessing--,e),e=>(this._numProcessing--,e))}_clearOutcomes(){let e=this._outcomes;return this._outcomes={},Object.entries(e).map(([e,t])=>{let[n,r]=e.split(":");return{reason:n,category:r,quantity:t}})}_flushOutcomes(){var e;let t=this._clearOutcomes();if(0===t.length||!this._dsn)return;let n=tW((e=this._options.tunnel&&tF(this._dsn))?{dsn:e}:{},[[{type:"client_report"},{timestamp:eu(),discarded_events:t}]]);this.sendEnvelope(n)}}function tX(e){return void 0===e.type}function tQ(e){return"transaction"===e.type}function tZ(e,t){let n=t1(e,t),r={type:function(e){let t=e&&e.name;return!t&&t3(e)?e.message&&Array.isArray(e.message)&&2==e.message.length?e.message[0]:"WebAssembly.Exception":t}(t),value:function(e){let t=e&&e.message;return t?t.error&&"string"==typeof t.error.message?t.error.message:t3(e)&&Array.isArray(e.message)&&2==e.message.length?e.message[1]:t:"No error message"}(t)};return n.length&&(r.stacktrace={frames:n}),void 0===r.type&&""===r.value&&(r.value="Unrecoverable error caught"),r}function t0(e,t){return{exception:{values:[tZ(e,t)]}}}function t1(e,t){var n,r;let i=t.stacktrace||t.stack||"",s=(n=t)&&t2.test(n.message)?1:0,a="number"==typeof(r=t).framesToPop?r.framesToPop:0;try{return e(i,s,a)}catch(e){}return[]}let t2=/Minified React error #\d+;/i;function t3(e){return"undefined"!=typeof WebAssembly&&void 0!==WebAssembly.Exception&&e instanceof WebAssembly.Exception}function t5(e,t,n,r,i){let s;if(P(t)&&t.error)return t0(e,t.error);if($(t)||L(t,"DOMException")){if("stack"in t)s=t0(e,t);else{let i=t.name||($(t)?"DOMError":"DOMException"),a=t.message?`${i}: ${t.message}`:i;em(s=t4(e,a,n,r),a)}return"code"in t&&(s.tags={...s.tags,"DOMException.code":`${t.code}`}),s}return N(t)?t0(e,t):(B(t)||W(t)?eg(s=function(e,t,n,r){let i=eN(),s=i&&i.getOptions().normalizeDepth,a=function(e){for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t)){let n=e[t];if(n instanceof Error)return n}}(t),o={__serialized__:function e(t,n=3,r=102400){let i=eq(t,n);return~-encodeURI(JSON.stringify(i)).split(/%..|./).length>r?e(t,n-1,r):i}(t,s)};if(a)return{exception:{values:[tZ(e,a)]},extra:o};let l={exception:{values:[{type:W(t)?t.constructor.name:r?"UnhandledRejection":"Error",value:function(e,{isUnhandledRejection:t}){let n=function(e,t=40){let n=Object.keys(ea(e));n.sort();let r=n[0];if(!r)return"[object has no keys]";if(r.length>=t)return Z(r,t);for(let e=n.length;e>0;e--){let r=n.slice(0,e).join(", ");if(!(r.length>t)){if(e===n.length)return r;return Z(r,t)}}return""}(e),r=t?"promise rejection":"exception";if(P(e))return`Event \`ErrorEvent\` captured as ${r} with message \`${e.message}\``;if(W(e)){let t=function(e){try{let t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch(e){}}(e);return`Event \`${t}\` (type=${e.type}) captured as ${r}`}return`Object captured as ${r} with keys: ${n}`}(t,{isUnhandledRejection:r})}]},extra:o};if(n){let t=t1(e,n);t.length&&(l.exception.values[0].stacktrace={frames:t})}return l}(e,t,n,i),{synthetic:!0}):(em(s=t4(e,t,n,r),`${t}`,void 0),eg(s,{synthetic:!0})),s)}function t4(e,t,n,r){let i={};if(r&&n){let r=t1(e,n);r.length&&(i.exception={values:[{value:t,stacktrace:{frames:r}}]}),eg(i,{synthetic:!0})}if(F(t)){let{__sentry_template_string__:e,__sentry_template_values__:n}=t;return i.logentry={message:e,params:n},i}return i.message=t,i}let t8=0;function t6(e,t={}){if("function"!=typeof e)return e;try{let t=e.__sentry_wrapped__;if(t)if("function"==typeof t)return t;else return e;if(es(e))return e}catch(t){return e}let n=function(...n){try{let r=n.map(e=>t6(e,t));return e.apply(this,r)}catch(e){throw t8++,setTimeout(()=>{t8--}),eD(r=>{var i;r.addEventProcessor(e=>(t.mechanism&&(em(e,void 0,void 0),eg(e,t.mechanism)),e.extra={...e.extra,arguments:n},e)),i=e,eR().captureException(i,function(e){if(e){var t;return(t=e)instanceof ek||"function"==typeof t||Object.keys(e).some(e=>tS.includes(e))?{captureContext:e}:e}}(void 0))}),e}};try{for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[t]=e[t])}catch(e){}ei(n,e),er(e,"__sentry_wrapped__",n);try{Object.getOwnPropertyDescriptor(n,"name").configurable&&Object.defineProperty(n,"name",{get:()=>e.name})}catch(e){}return n}class t7 extends tK{constructor(e){let t={parentSpanIsAlwaysRootSpan:!0,...e};M(t,"browser",["browser"],I.SENTRY_SDK_SOURCE||"npm"),super(t),t.sendClientReports&&I.document&&I.document.addEventListener("visibilitychange",()=>{"hidden"===I.document.visibilityState&&this._flushOutcomes()})}eventFromException(e,t){return function(e,t,n,r){let i=t5(e,t,n&&n.syntheticException||void 0,r);return eg(i),i.level="error",n&&n.event_id&&(i.event_id=n.event_id),eL(i)}(this._options.stackParser,e,t,this._options.attachStacktrace)}eventFromMessage(e,t="info",n){return function(e,t,n="info",r,i){let s=t4(e,t,r&&r.syntheticException||void 0,i);return s.level=n,r&&r.event_id&&(s.event_id=r.event_id),eL(s)}(this._options.stackParser,e,t,n,this._options.attachStacktrace)}captureUserFeedback(e){if(!this._isEnabled())return;let t=function(e,{metadata:t,tunnel:n,dsn:r}){return tW({event_id:e.event_id,sent_at:new Date().toISOString(),...t&&t.sdk&&{sdk:{name:t.sdk.name,version:t.sdk.version}},...!!n&&!!r&&{dsn:tF(r)}},[[{type:"user_report"},e]])}(e,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this.sendEnvelope(t)}_prepareEvent(e,t,n){return e.platform=e.platform||"javascript",super._prepareEvent(e,t,n)}}let t9={},ne={};function nt(e,t){t9[e]=t9[e]||[],t9[e].push(t)}function nn(e,t){if(!ne[e]){ne[e]=!0;try{t()}catch(e){}}}function nr(e,t){let n=e&&t9[e];if(n)for(let e of n)try{e(t)}catch(e){}}function ni(e){nt("dom",e),nn("dom",ns)}function ns(){if(!I.document)return;let e=nr.bind(null,"dom"),t=na(e,!0);I.document.addEventListener("click",t,!1),I.document.addEventListener("keypress",t,!1),["EventTarget","Node"].forEach(t=>{let n=I[t],r=n&&n.prototype;r&&r.hasOwnProperty&&r.hasOwnProperty("addEventListener")&&(en(r,"addEventListener",function(t){return function(n,r,i){if("click"===n||"keypress"==n)try{let r=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},s=r[n]=r[n]||{refCount:0};if(!s.handler){let r=na(e);s.handler=r,t.call(this,n,r,i)}s.refCount++}catch(e){}return t.call(this,n,r,i)}}),en(r,"removeEventListener",function(e){return function(t,n,r){if("click"===t||"keypress"==t)try{let n=this.__sentry_instrumentation_handlers__||{},i=n[t];i&&(i.refCount--,i.refCount<=0&&(e.call(this,t,i.handler,r),i.handler=void 0,delete n[t]),0===Object.keys(n).length&&delete this.__sentry_instrumentation_handlers__)}catch(e){}return e.call(this,t,n,r)}}))})}function na(e,t=!1){return n=>{var r;if(!n||n._sentryCaptured)return;let i=function(e){try{return e.target}catch(e){return null}}(n);if(r=n.type,"keypress"===r&&(!i||!i.tagName||"INPUT"!==i.tagName&&"TEXTAREA"!==i.tagName&&!i.isContentEditable&&1))return;er(n,"_sentryCaptured",!0),i&&!i._sentryId&&er(i,"_sentryId",ep());let s="keypress"===n.type?"input":n.type;!function(e){if(e.type!==l)return!1;try{if(!e.target||e.target._sentryId!==c)return!1}catch(e){}return!0}(n)&&(e({event:n,name:s,global:t}),l=n.type,c=i?i._sentryId:void 0),clearTimeout(o),o=I.setTimeout(()=>{c=void 0,l=void 0},1e3)}}let no="__sentry_xhr_v3__";function nl(e){nt("xhr",e),nn("xhr",nc)}function nc(){if(!I.XMLHttpRequest)return;let e=XMLHttpRequest.prototype;e.open=new Proxy(e.open,{apply(e,t,n){let r=Error(),i=1e3*ed(),s=j(n[0])?n[0].toUpperCase():void 0,a=function(e){if(j(e))return e;try{return e.toString()}catch(e){}}(n[1]);if(!s||!a)return e.apply(t,n);t[no]={method:s,url:a,request_headers:{}},"POST"===s&&a.match(/sentry_key/)&&(t.__sentry_own_request__=!0);let o=()=>{let e=t[no];if(e&&4===t.readyState){try{e.status_code=t.status}catch(e){}nr("xhr",{endTimestamp:1e3*ed(),startTimestamp:i,xhr:t,virtualError:r})}};return"onreadystatechange"in t&&"function"==typeof t.onreadystatechange?t.onreadystatechange=new Proxy(t.onreadystatechange,{apply:(e,t,n)=>(o(),e.apply(t,n))}):t.addEventListener("readystatechange",o),t.setRequestHeader=new Proxy(t.setRequestHeader,{apply(e,t,n){let[r,i]=n,s=t[no];return s&&j(r)&&j(i)&&(s.request_headers[r.toLowerCase()]=i),e.apply(t,n)}}),e.apply(t,n)}}),e.send=new Proxy(e.send,{apply(e,t,n){let r=t[no];return r&&(void 0!==n[0]&&(r.body=n[0]),nr("xhr",{startTimestamp:1e3*ed(),xhr:t})),e.apply(t,n)}})}function nu(e){let t="history";nt(t,e),nn(t,nd)}function nd(){if(!function(){let e=I.chrome,t=e&&e.app&&e.app.runtime,n="history"in I&&!!I.history.pushState&&!!I.history.replaceState;return!t&&n}())return;let e=I.onpopstate;function t(e){return function(...t){let n=t.length>2?t[2]:void 0;if(n){let e=u,t=String(n);u=t,nr("history",{from:e,to:t})}return e.apply(this,t)}}I.onpopstate=function(...t){let n=I.location.href,r=u;if(u=n,nr("history",{from:r,to:n}),e)try{return e.apply(this,t)}catch(e){}},en(I.history,"pushState",t),en(I.history,"replaceState",t)}function nh(){"console"in I&&G.forEach(function(e){e in I.console&&en(I.console,e,function(t){return K[e]=t,function(...t){nr("console",{args:t,level:e});let n=K[e];n&&n.apply(I.console,t)}})})}function np(e,t){let n="fetch";nt(n,e),nn(n,()=>nf(void 0,t))}function nf(e,t=!1){(!t||function(){if("string"==typeof EdgeRuntime)return!0;if(!function(){if(!("fetch"in I))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch(e){return!1}}())return!1;if(t$(I.fetch))return!0;let e=!1,t=I.document;if(t&&"function"==typeof t.createElement)try{let n=t.createElement("iframe");n.hidden=!0,t.head.appendChild(n),n.contentWindow&&n.contentWindow.fetch&&(e=t$(n.contentWindow.fetch)),t.head.removeChild(n)}catch(e){}return e}())&&en(I,"fetch",function(t){return function(...n){let r=Error(),{method:i,url:s}=function(e){if(0===e.length)return{method:"GET",url:""};if(2===e.length){let[t,n]=e;return{url:n_(t),method:ny(n,"method")?String(n.method).toUpperCase():"GET"}}let t=e[0];return{url:n_(t),method:ny(t,"method")?String(t.method).toUpperCase():"GET"}}(n),a={args:n,fetchData:{method:i,url:s},startTimestamp:1e3*ed(),virtualError:r};return e||nr("fetch",{...a}),t.apply(I,n).then(async t=>(e?e(t):nr("fetch",{...a,endTimestamp:1e3*ed(),response:t}),t),e=>{throw nr("fetch",{...a,endTimestamp:1e3*ed(),error:e}),N(e)&&void 0===e.stack&&(e.stack=r.stack,er(e,"framesToPop",1)),e})}})}async function nm(e,t){if(e&&e.body){let n=e.body,r=n.getReader(),i=setTimeout(()=>{n.cancel().then(null,()=>{})},9e4),s=!0;for(;s;){let e;try{e=setTimeout(()=>{n.cancel().then(null,()=>{})},5e3);let{done:i}=await r.read();clearTimeout(e),i&&(t(),s=!1)}catch(e){s=!1}finally{clearTimeout(e)}}clearTimeout(i),r.releaseLock(),n.cancel().then(null,()=>{})}}function ng(e){let t;try{t=e.clone()}catch(e){return}nm(t,()=>{nr("fetch-body-resolved",{endTimestamp:1e3*ed(),response:e})})}function ny(e,t){return!!e&&"object"==typeof e&&!!e[t]}function n_(e){return"string"==typeof e?e:e?ny(e,"url")?e.url:e.toString?e.toString():"":""}function nv(e,t){let n=eN(),r=eO();if(!n)return;let{beforeBreadcrumb:i=null,maxBreadcrumbs:s=100}=n.getOptions();if(s<=0)return;let a={timestamp:eu(),...e},o=i?X(()=>i(a,t)):a;null!==o&&(n.emit&&n.emit("beforeAddBreadcrumb",o,t),r.addBreadcrumb(o,s))}function nS(e){if(void 0!==e)return e>=400&&e<500?"warning":e>=500?"error":void 0}function nb(e){if(!e)return{};let t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};let n=t[6]||"",r=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:n,hash:r,relative:t[5]+n+r}}let nE=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"];function nw(e){return function(...t){let n=t[0];return t[0]=t6(n,{mechanism:{data:{function:ez(e)},handled:!1,type:"instrument"}}),e.apply(this,t)}}function nk(e){return function(t){return e.apply(this,[t6(t,{mechanism:{data:{function:"requestAnimationFrame",handler:ez(e)},handled:!1,type:"instrument"}})])}}function nT(e){return function(...t){let n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(e=>{e in n&&"function"==typeof n[e]&&en(n,e,function(t){let n={mechanism:{data:{function:e,handler:ez(t)},handled:!1,type:"instrument"}},r=es(t);return r&&(n.mechanism.data.handler=ez(r)),t6(t,n)})}),e.apply(this,t)}}function nx(e){let t=I[e],n=t&&t.prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&(en(n,"addEventListener",function(t){return function(n,r,i){try{var s;s=r,"function"==typeof s.handleEvent&&(r.handleEvent=t6(r.handleEvent,{mechanism:{data:{function:"handleEvent",handler:ez(r),target:e},handled:!1,type:"instrument"}}))}catch(e){}return t.apply(this,[n,t6(r,{mechanism:{data:{function:"addEventListener",handler:ez(r),target:e},handled:!1,type:"instrument"}}),i])}}),en(n,"removeEventListener",function(e){return function(t,n,r){try{let i=n.__sentry_wrapped__;i&&e.call(this,t,i,r)}catch(e){}return e.call(this,t,n,r)}}))}let nI=null;function nC(e){let t="error";nt(t,e),nn(t,nM)}function nM(){nI=I.onerror,I.onerror=function(e,t,n,r,i){return nr("error",{column:r,error:i,line:n,msg:e,url:t}),!!nI&&nI.apply(this,arguments)},I.onerror.__SENTRY_INSTRUMENTED__=!0}let nA=null;function nR(e){let t="unhandledrejection";nt(t,e),nn(t,nO)}function nO(){nA=I.onunhandledrejection,I.onunhandledrejection=function(e){return nr("unhandledrejection",e),!nA||nA.apply(this,arguments)},I.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}function nD(){let e=eN();return e&&e.getOptions()||{stackParser:()=>[],attachStacktrace:!1}}function nN(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,..."AggregateError"===e.type&&{is_exception_group:!0},exception_id:t}}function nL(e,t,n,r){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,type:"chained",source:t,exception_id:n,parent_id:r}}function nP(e,t,n,r){let i={filename:e,function:"<anonymous>"===t?"?":t,in_app:!0};return void 0!==n&&(i.lineno=n),void 0!==r&&(i.colno=r),i}let n$=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,nj=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,nF=/\((\S*)(?::(\d+))(?::(\d+))\)/,nU=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,nB=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,nW=eU([30,e=>{let t=n$.exec(e);if(t){let[,e,n,r]=t;return nP(e,"?",+n,+r)}let n=nj.exec(e);if(n){if(n[2]&&0===n[2].indexOf("eval")){let e=nF.exec(n[2]);e&&(n[2]=e[1],n[3]=e[2],n[4]=e[3])}let[e,t]=nz(n[1]||"?",n[2]);return nP(t,e,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],[50,e=>{let t=nU.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){let e=nB.exec(t[3]);e&&(t[1]=t[1]||"eval",t[3]=e[1],t[4]=e[2],t[5]="")}let e=t[3],n=t[1]||"?";return[n,e]=nz(n,e),nP(e,n,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}}]),nz=(e,t)=>{let n=-1!==e.indexOf("safari-extension"),r=-1!==e.indexOf("safari-web-extension");return n||r?[-1!==e.indexOf("@")?e.split("@")[0]:"?",n?`safari-extension:${t}`:`safari-web-extension:${t}`]:[e,t]},nH={};function nq(e){let t=nH[e];if(t)return t;let n=I[e];if(t$(n))return nH[e]=n.bind(I);let r=I.document;if(r&&"function"==typeof r.createElement)try{let t=r.createElement("iframe");t.hidden=!0,r.head.appendChild(t);let i=t.contentWindow;i&&i[e]&&(n=i[e]),r.head.removeChild(t)}catch(e){}return n?nH[e]=n.bind(I):n}function nV(...e){return nq("setTimeout")(...e)}function nY(e,t,n=Date.now()){return(e[t]||e.all||0)>n}function nJ(e,{statusCode:t,headers:n},r=Date.now()){let i={...e},s=n&&n["x-sentry-rate-limits"],a=n&&n["retry-after"];if(s)for(let e of s.trim().split(",")){let[t,n,,,s]=e.split(":",5),a=parseInt(t,10),o=(isNaN(a)?60:a)*1e3;if(n)for(let e of n.split(";"))"metric_bucket"===e?(!s||s.split(";").includes("custom"))&&(i[e]=r+o):i[e]=r+o;else i.all=r+o}else a?i.all=r+function(e,t=Date.now()){let n=parseInt(`${e}`,10);if(!isNaN(n))return 1e3*n;let r=Date.parse(`${e}`);return isNaN(r)?6e4:r-t}(a,r):429===t&&(i.all=r+6e4);return i}function nG(e,t){if("event"===t||"transaction"===t)return Array.isArray(e)?e[1]:void 0}function nK(e,t=nq("fetch")){let n=0,r=0;return function(e,t,n=function(e){let t=[];function n(e){return t.splice(t.indexOf(e),1)[0]||Promise.resolve(void 0)}return{$:t,add:function(r){if(!(void 0===e||t.length<e))return eP(new tJ("Not adding Promise because buffer limit was reached."));let i=r();return -1===t.indexOf(i)&&t.push(i),i.then(()=>n(i)).then(null,()=>n(i).then(null,()=>{})),i},drain:function(e){return new e$((n,r)=>{let i=t.length;if(!i)return n(!0);let s=setTimeout(()=>{e&&e>0&&n(!1)},e);t.forEach(e=>{eL(e).then(()=>{--i||(clearTimeout(s),n(!0))},r)})})}}}(e.bufferSize||64)){let r={};return{send:function(i){let s=[];if(tz(i,(t,n)=>{let i=tq[n];if(nY(r,i)){let r=nG(t,n);e.recordDroppedEvent("ratelimit_backoff",i,r)}else s.push(t)}),0===s.length)return eL({});let a=tW(i[0],s),o=t=>{tz(a,(n,r)=>{let i=nG(n,r);e.recordDroppedEvent(t,tq[r],i)})};return n.add(()=>t({body:function(e){let[t,n]=e,r=JSON.stringify(t);function i(e){"string"==typeof r?r="string"==typeof e?r+e:[tH(r),e]:r.push("string"==typeof e?tH(e):e)}for(let e of n){let[t,n]=e;if(i(`
${JSON.stringify(t)}
`),"string"==typeof n||n instanceof Uint8Array)i(n);else{let e;try{e=JSON.stringify(n)}catch(t){e=JSON.stringify(eq(n))}i(e)}}return"string"==typeof r?r:function(e){let t=new Uint8Array(e.reduce((e,t)=>e+t.length,0)),n=0;for(let r of e)t.set(r,n),n+=r.length;return t}(r)}(a)}).then(e=>(void 0!==e.statusCode&&(e.statusCode<200||e.statusCode),r=nJ(r,e),e),e=>{throw o("network_error"),e})).then(e=>e,e=>{if(e instanceof tJ)return o("queue_overflow"),eL({});throw e})},flush:e=>n.drain(e)}}(e,function(i){let s=i.body.length;n+=s,r++;let a={body:i.body,method:"POST",referrerPolicy:"origin",headers:e.headers,keepalive:n<=6e4&&r<15,...e.fetchOptions};if(!t)return nH.fetch=void 0,eP("No fetch implementation available");try{return t(e.url,a).then(e=>(n-=s,r--,{statusCode:e.status,headers:{"x-sentry-rate-limits":e.headers.get("X-Sentry-Rate-Limits"),"retry-after":e.headers.get("Retry-After")}}))}catch(e){return nH.fetch=void 0,n-=s,r--,eP(e)}})}function nX(e){let t,n=[((e={})=>({name:"InboundFilters",processEvent:(t,n,r)=>{var i,s,a,o,l;return(i=t,(s=function(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...e.disableErrorDefaults?[]:tR],ignoreTransactions:[...e.ignoreTransactions||[],...t.ignoreTransactions||[]],ignoreInternal:void 0===e.ignoreInternal||e.ignoreInternal}}(e,r.getOptions())).ignoreInternal&&function(e){try{return"SentryError"===e.exception.values[0].type}catch(e){}return!1}(i)||(a=i,o=s.ignoreErrors,!a.type&&o&&o.length&&(function(e){let t,n=[];e.message&&n.push(e.message);try{t=e.exception.values[e.exception.values.length-1]}catch(e){}return t&&t.value&&(n.push(t.value),t.type&&n.push(`${t.type}: ${t.value}`)),n})(a).some(e=>et(e,o))||!(l=i).type&&l.exception&&l.exception.values&&0!==l.exception.values.length&&!l.message&&!l.exception.values.some(e=>e.stacktrace||e.type&&"Error"!==e.type||e.value)||function(e,t){if("transaction"!==e.type||!t||!t.length)return!1;let n=e.transaction;return!!n&&et(n,t)}(i,s.ignoreTransactions)||function(e,t){if(!t||!t.length)return!1;let n=tO(e);return!!n&&et(n,t)}(i,s.denyUrls))||!function(e,t){if(!t||!t.length)return!0;let n=tO(e);return!n||et(n,t)}(i,s.allowUrls))?null:t}}))(),{name:"FunctionToString",setupOnce(){a=Function.prototype.toString;try{Function.prototype.toString=function(...e){let t=es(this),n=tD.has(eN())&&void 0!==t?t:this;return a.apply(n,e)}}catch(e){}},setup(e){tD.set(e,!0)}},((e={})=>{let t={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...e};return{name:"BrowserApiErrors",setupOnce(){t.setTimeout&&en(I,"setTimeout",nw),t.setInterval&&en(I,"setInterval",nw),t.requestAnimationFrame&&en(I,"requestAnimationFrame",nk),t.XMLHttpRequest&&"XMLHttpRequest"in I&&en(XMLHttpRequest.prototype,"send",nT);let e=t.eventTarget;e&&(Array.isArray(e)?e:nE).forEach(nx)}}})(),((e={})=>{let t={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...e};return{name:"Breadcrumbs",setup(e){var n,r,i,s,a,o,l;t.console&&function(e){let t="console";nt(t,e),nn(t,nh)}((n=e,function(e){var t;if(eN()!==n)return;let r={category:"console",data:{arguments:e.args,logger:"console"},level:"warn"===(t=e.level)?"warning":["fatal","error","warning","log","info","debug"].includes(t)?t:"log",message:ee(e.args," ")};if("assert"===e.level)if(!1!==e.args[0])return;else r.message=`Assertion failed: ${ee(e.args.slice(1)," ")||"console.assert"}`,r.data.arguments=e.args.slice(1);nv(r,{input:e.args,level:e.level})})),t.dom&&ni((r=e,i=t.dom,function(e){let t,n;if(eN()!==r)return;let s="object"==typeof i?i.serializeAttribute:void 0,a="object"==typeof i&&"number"==typeof i.maxStringLength?i.maxStringLength:void 0;a&&a>1024&&(a=1024),"string"==typeof s&&(s=[s]);try{var o;let r=e.event,i=(o=r)&&o.target?r.target:r;t=V(i,{keyAttrs:s,maxStringLength:a}),n=J(i)}catch(e){t="<unknown>"}if(0===t.length)return;let l={category:`ui.${e.name}`,message:t};n&&(l.data={"ui.component_name":n}),nv(l,{event:e.event,name:e.name,global:e.global})})),t.xhr&&nl((s=e,function(e){if(eN()!==s)return;let{startTimestamp:t,endTimestamp:n}=e,r=e.xhr[no];if(!t||!n||!r)return;let{method:i,url:a,status_code:o,body:l}=r,c={xhr:e.xhr,input:l,startTimestamp:t,endTimestamp:n};nv({category:"xhr",data:{method:i,url:a,status_code:o},type:"http",level:nS(o)},c)})),t.fetch&&np((a=e,function(e){if(eN()!==a)return;let{startTimestamp:t,endTimestamp:n}=e;if(n&&(!e.fetchData.url.match(/sentry_key/)||"POST"!==e.fetchData.method))if(e.error)nv({category:"fetch",data:e.fetchData,level:"error",type:"http"},{data:e.error,input:e.args,startTimestamp:t,endTimestamp:n});else{let r=e.response,i={...e.fetchData,status_code:r&&r.status},s={input:e.args,response:r,startTimestamp:t,endTimestamp:n},a=nS(i.status_code);nv({category:"fetch",data:i,type:"http",level:a},s)}})),t.history&&nu((o=e,function(e){if(eN()!==o)return;let t=e.from,n=e.to,r=nb(I.location.href),i=t?nb(t):void 0,s=nb(n);i&&i.path||(i=r),r.protocol===s.protocol&&r.host===s.host&&(n=s.relative),r.protocol===i.protocol&&r.host===i.host&&(t=i.relative),nv({category:"navigation",data:{from:t,to:n}})})),t.sentry&&e.on("beforeSendEvent",(l=e,function(e){eN()===l&&nv({category:`sentry.${"transaction"===e.type?"transaction":"event"}`,event_id:e.event_id,level:e.level,message:function(e){let{message:t,event_id:n}=e;if(t)return t;let r=ef(e);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}(e)},{event:e})}))}}})(),((e={})=>{let t={onerror:!0,onunhandledrejection:!0,...e};return{name:"GlobalHandlers",setupOnce(){Error.stackTraceLimit=50},setup(e){var n,r;t.onerror&&(n=e,nC(e=>{let{stackParser:t,attachStacktrace:r}=nD();if(eN()!==n||t8>0)return;let{msg:i,url:s,line:a,column:o,error:l}=e,c=function(e,t,n,r){let i=e.exception=e.exception||{},s=i.values=i.values||[],a=s[0]=s[0]||{},o=a.stacktrace=a.stacktrace||{},l=o.frames=o.frames||[],c=j(t)&&t.length>0?t:Y();return 0===l.length&&l.push({colno:r,filename:c,function:"?",in_app:!0,lineno:n}),e}(t5(t,l||i,void 0,r,!1),s,a,o);c.level="error",tb(c,{originalException:l,mechanism:{handled:!1,type:"onerror"}})})),t.onunhandledrejection&&(r=e,nR(e=>{var t;let{stackParser:n,attachStacktrace:i}=nD();if(eN()!==r||t8>0)return;let s=function(e){if(U(e))return e;try{if("reason"in e)return e.reason;if("detail"in e&&"reason"in e.detail)return e.detail.reason}catch(e){}return e}(e),a=U(s)?(t=s,{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(t)}`}]}}):t5(n,s,void 0,i,!0);a.level="error",tb(a,{originalException:s,mechanism:{handled:!1,type:"onunhandledrejection"}})}))}}})(),((e={})=>{let t=e.limit||5,n=e.key||"cause";return{name:"LinkedErrors",preprocessEvent(e,r,i){let s=i.getOptions();!function(e,t,n=250,r,i,s,a){var o,l;if(!s.exception||!s.exception.values||!a||!H(a.originalException,Error))return;let c=s.exception.values.length>0?s.exception.values[s.exception.values.length-1]:void 0;c&&(s.exception.values=(o=function e(t,n,r,i,s,a,o,l){if(a.length>=r+1)return a;let c=[...a];if(H(i[s],Error)){nN(o,l);let a=t(n,i[s]),u=c.length;nL(a,s,u,l),c=e(t,n,r,i[s],s,[a,...c],a,u)}return Array.isArray(i.errors)&&i.errors.forEach((i,a)=>{if(H(i,Error)){nN(o,l);let u=t(n,i),d=c.length;nL(u,`errors[${a}]`,d,l),c=e(t,n,r,i,s,[u,...c],u,d)}}),c}(e,t,i,a.originalException,r,s.exception.values,c,0),l=n,o.map(e=>(e.value&&(e.value=Z(e.value,l)),e))))}(tZ,s.stackParser,s.maxValueLength,n,t,e,r)}}})(),{name:"Dedupe",processEvent(e){if(e.type)return e;try{var n,r;if(n=e,(r=t)&&(function(e,t){let n=e.message,r=t.message;return(!!n||!!r)&&(!n||!!r)&&(!!n||!r)&&n===r&&!!tL(e,t)&&!!tN(e,t)&&!0}(n,r)||function(e,t){let n=tP(t),r=tP(e);return!!n&&!!r&&n.type===r.type&&n.value===r.value&&!!tL(e,t)&&!!tN(e,t)}(n,r)))return null}catch(e){}return t=e}},{name:"HttpContext",preprocessEvent(e){if(!I.navigator&&!I.location&&!I.document)return;let t=e.request&&e.request.url||I.location&&I.location.href,{referrer:n}=I.document||{},{userAgent:r}=I.navigator||{},i={...e.request&&e.request.headers,...n&&{Referer:n},...r&&{"User-Agent":r}},s={...e.request,...t&&{url:t},headers:i};e.request=s}}];return!1!==e.autoSessionTracking&&n.push({name:"BrowserSession",setupOnce(){void 0!==I.document&&(tk({ignoreDuration:!0}),tI(),nu(({from:e,to:t})=>{void 0!==e&&e!==t&&(tk({ignoreDuration:!0}),tI())}))}}),n}var nQ,nZ,n0,n1,n2,n3=n(148),n5=n(6953);function n4(e){if(!e||0===e.length)return;let t={};return e.forEach(e=>{let n=e.attributes||{},r=n[eX],i=n[eQ];"string"==typeof r&&"number"==typeof i&&(t[e.name]={value:i,unit:r})}),t}let n8=(e,t,n,r)=>{let i,s;return a=>{t.value>=0&&(a||r)&&((s=t.value-(i||0))||void 0===i)&&(i=t.value,t.delta=s,t.rating=((e,t)=>e>t[1]?"poor":e>t[0]?"needs-improvement":"good")(t.value,n),e(t))}},n6=(e=!0)=>{let t=I.performance&&I.performance.getEntriesByType&&I.performance.getEntriesByType("navigation")[0];if(!e||t&&t.responseStart>0&&t.responseStart<performance.now())return t},n7=()=>{let e=n6();return e&&e.activationStart||0},n9=(e,t)=>{let n=n6(),r="navigate";return n&&(I.document&&I.document.prerendering||n7()>0?r="prerender":I.document&&I.document.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:`v4-${Date.now()}-${Math.floor(Math.random()*(9e12-1))+1e12}`,navigationType:r}},re=(e,t,n)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){let r=new PerformanceObserver(e=>{Promise.resolve().then(()=>{t(e.getEntries())})});return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(e){}},rt=e=>{let t=t=>{("pagehide"===t.type||I.document&&"hidden"===I.document.visibilityState)&&e(t)};I.document&&(addEventListener("visibilitychange",t,!0),addEventListener("pagehide",t,!0))},rn=e=>{let t=!1;return()=>{t||(e(),t=!0)}},rr=-1,ri=e=>{"hidden"===I.document.visibilityState&&rr>-1&&(rr="visibilitychange"===e.type?e.timeStamp:0,rs())},rs=()=>{removeEventListener("visibilitychange",ri,!0),removeEventListener("prerenderingchange",ri,!0)},ra=()=>(I.document&&rr<0&&(rr="hidden"!==I.document.visibilityState||I.document.prerendering?1/0:0,addEventListener("visibilitychange",ri,!0),addEventListener("prerenderingchange",ri,!0)),{get firstHiddenTime(){return rr}}),ro=e=>{I.document&&I.document.prerendering?addEventListener("prerenderingchange",()=>e(),!0):e()},rl=[1800,3e3],rc=[.1,.25],ru=[100,300],rd=0,rh=1/0,rp=0,rf=e=>{e.forEach(e=>{e.interactionId&&(rh=Math.min(rh,e.interactionId),rd=(rp=Math.max(rp,e.interactionId))?(rp-rh)/7+1:0)})},rm=[],rg=new Map,ry=[],r_=e=>{if(ry.forEach(t=>t(e)),!(e.interactionId||"first-input"===e.entryType))return;let t=rm[rm.length-1],n=rg.get(e.interactionId);if(n||rm.length<10||t&&e.duration>t.latency){if(n)e.duration>n.latency?(n.entries=[e],n.latency=e.duration):e.duration===n.latency&&e.startTime===(n.entries[0]&&n.entries[0].startTime)&&n.entries.push(e);else{let t={id:e.interactionId,latency:e.duration,entries:[e]};rg.set(t.id,t),rm.push(t)}rm.sort((e,t)=>t.latency-e.latency),rm.length>10&&rm.splice(10).forEach(e=>rg.delete(e.id))}},rv=e=>{let t=I.requestIdleCallback||I.setTimeout,n=-1;return e=rn(e),I.document&&"hidden"===I.document.visibilityState?e():(n=t(e),rt(e)),n},rS=[200,500],rb=[2500,4e3],rE={},rw=[800,1800],rk=e=>{I.document&&I.document.prerendering?ro(()=>rk(e)):I.document&&"complete"!==I.document.readyState?addEventListener("load",()=>rk(e),!0):setTimeout(e,0)},rT={},rx={};function rI(e,t=!1){return rj("cls",e,rD,h,t)}function rC(e,t=!1){return rj("lcp",e,rL,f,t)}function rM(e){return rj("fid",e,rN,p)}function rA(e){return rj("inp",e,r$,g)}function rR(e,t){return rF(e,t),rx[e]||(function(e){let t={};"event"===e&&(t.durationThreshold=0),re(e,t=>{rO(e,{entries:t})},t)}(e),rx[e]=!0),rU(e,t)}function rO(e,t){let n=rT[e];if(n&&n.length)for(let e of n)try{e(t)}catch(e){}}function rD(){return((e,t={})=>{((e,t={})=>{ro(()=>{let n,r=ra(),i=n9("FCP"),s=re("paint",e=>{e.forEach(e=>{"first-contentful-paint"===e.name&&(s.disconnect(),e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-n7(),0),i.entries.push(e),n(!0)))})});s&&(n=n8(e,i,rl,t.reportAllChanges))})})(rn(()=>{let n,r=n9("CLS",0),i=0,s=[],a=e=>{e.forEach(e=>{if(!e.hadRecentInput){let t=s[0],n=s[s.length-1];i&&t&&n&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(i+=e.value,s.push(e)):(i=e.value,s=[e])}}),i>r.value&&(r.value=i,r.entries=s,n())},o=re("layout-shift",a);o&&(n=n8(e,r,rc,t.reportAllChanges),rt(()=>{a(o.takeRecords()),n(!0)}),setTimeout(n,0))}))})(e=>{rO("cls",{metric:e}),h=e},{reportAllChanges:!0})}function rN(){return((e,t={})=>{ro(()=>{let n,r=ra(),i=n9("FID"),s=e=>{e.startTime<r.firstHiddenTime&&(i.value=e.processingStart-e.startTime,i.entries.push(e),n(!0))},a=e=>{e.forEach(s)},o=re("first-input",a);n=n8(e,i,ru,t.reportAllChanges),o&&rt(rn(()=>{a(o.takeRecords()),o.disconnect()}))})})(e=>{rO("fid",{metric:e}),p=e})}function rL(){return((e,t={})=>{ro(()=>{let n,r=ra(),i=n9("LCP"),s=e=>{t.reportAllChanges||(e=e.slice(-1)),e.forEach(e=>{e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-n7(),0),i.entries=[e],n())})},a=re("largest-contentful-paint",s);if(a){n=n8(e,i,rb,t.reportAllChanges);let r=rn(()=>{rE[i.id]||(s(a.takeRecords()),a.disconnect(),rE[i.id]=!0,n(!0))});["keydown","click"].forEach(e=>{I.document&&addEventListener(e,()=>rv(r),{once:!0,capture:!0})}),rt(r)}})})(e=>{rO("lcp",{metric:e}),f=e},{reportAllChanges:!0})}function rP(){return((e,t={})=>{let n=n9("TTFB"),r=n8(e,n,rw,t.reportAllChanges);rk(()=>{let e=n6();e&&(n.value=Math.max(e.responseStart-n7(),0),n.entries=[e],r(!0))})})(e=>{rO("ttfb",{metric:e}),m=e})}function r$(){return((e,t={})=>{"PerformanceEventTiming"in I&&"interactionId"in PerformanceEventTiming.prototype&&ro(()=>{let n;"interactionCount"in performance||d||(d=re("event",rf,{type:"event",buffered:!0,durationThreshold:0}));let r=n9("INP"),i=e=>{rv(()=>{e.forEach(r_);let t=(()=>{let e=Math.min(rm.length-1,Math.floor(((d?rd:performance.interactionCount||0)-0)/50));return rm[e]})();t&&t.latency!==r.value&&(r.value=t.latency,r.entries=t.entries,n())})},s=re("event",i,{durationThreshold:null!=t.durationThreshold?t.durationThreshold:40});n=n8(e,r,rS,t.reportAllChanges),s&&(s.observe({type:"first-input",buffered:!0}),rt(()=>{i(s.takeRecords()),n(!0)}))})})(e=>{rO("inp",{metric:e}),g=e})}function rj(e,t,n,r,i=!1){let s;return rF(e,t),rx[e]||(s=n(),rx[e]=!0),r&&t({metric:r}),rU(e,t,i?s:void 0)}function rF(e,t){rT[e]=rT[e]||[],rT[e].push(t)}function rU(e,t,n){return()=>{n&&n();let r=rT[e];if(!r)return;let i=r.indexOf(t);-1!==i&&r.splice(i,1)}}class rB{constructor(e={}){this._traceId=e.traceId||ep(),this._spanId=e.spanId||ev()}spanContext(){return{spanId:this._spanId,traceId:this._traceId,traceFlags:0}}end(e){}setAttribute(e,t){return this}setAttributes(e){return this}setStatus(e){return this}updateName(e){return this}isRecording(){return!1}addEvent(e,t,n){return this}addLink(e){return this}addLinks(e){return this}recordException(e,t){}}let rW="_sentryScope",rz="_sentryIsolationScope";function rH(e){return{scope:e[rW],isolationScope:e[rz]}}class rq{constructor(e={}){this._traceId=e.traceId||ep(),this._spanId=e.spanId||ev(),this._startTime=e.startTimestamp||ed(),this._attributes={},this.setAttributes({[eG]:"manual",[eJ]:e.op,...e.attributes}),this._name=e.name,e.parentSpanId&&(this._parentSpanId=e.parentSpanId),"sampled"in e&&(this._sampled=e.sampled),e.endTimestamp&&(this._endTime=e.endTimestamp),this._events=[],this._isStandaloneSpan=e.isStandalone,this._endTime&&this._onSpanEnded()}addLink(e){return this}addLinks(e){return this}recordException(e,t){}spanContext(){let{_spanId:e,_traceId:t,_sampled:n}=this;return{spanId:e,traceId:t,traceFlags:+!!n}}setAttribute(e,t){return void 0===t?delete this._attributes[e]:this._attributes[e]=t,this}setAttributes(e){return Object.keys(e).forEach(t=>this.setAttribute(t,e[t])),this}updateStartTime(e){this._startTime=tt(e)}setStatus(e){return this._status=e,this}updateName(e){return this._name=e,this.setAttribute(eV,"custom"),this}end(e){this._endTime||(this._endTime=tt(e),this._onSpanEnded())}getSpanJSON(){return ec({data:this._attributes,description:this._name,op:this._attributes[eJ],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:ts(this._status),timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[eG],_metrics_summary:e8(this),profile_id:this._attributes["sentry.profile_id"],exclusive_time:this._attributes[eZ],measurements:n4(this._events),is_segment:this._isStandaloneSpan&&tu(this)===this||void 0,segment_id:this._isStandaloneSpan?tu(this).spanContext().spanId:void 0})}isRecording(){return!this._endTime&&!!this._sampled}addEvent(e,t,n){let r=rV(t)?t:n||ed(),i=rV(t)?{}:t||{},s={name:e,time:tt(r),attributes:i};return this._events.push(s),this}isStandaloneSpan(){return!!this._isStandaloneSpan}_onSpanEnded(){let e=eN();if(e&&e.emit("spanEnd",this),!(this._isStandaloneSpan||this===tu(this)))return;if(this._isStandaloneSpan)return void(this._sampled?function(e){let t=eN();if(!t)return;let n=e[1];if(!n||0===n.length)return t.recordDroppedEvent("before_send","span");t.sendEnvelope(e)}(function(e,t){let n=tg(e[0]),r=t&&t.getDsn(),i=t&&t.getOptions().tunnel,s={sent_at:new Date().toISOString(),...!!n.trace_id&&!!n.public_key&&{trace:n},...!!i&&r&&{dsn:tF(r)}},a=t&&t.getOptions().beforeSendSpan,o=a?e=>{let t=a(tr(e));return t||th(),t}:e=>tr(e),l=[];for(let t of e){let e=o(t);e&&l.push([{type:"span"},e])}return tW(s,l)}([this],e)):e&&e.recordDroppedEvent("sample_rate","span"));let t=this._convertSpanToTransaction();t&&(rH(this).scope||eR()).captureEvent(t)}_convertSpanToTransaction(){if(!rY(tr(this)))return;this._name||(this._name="<unlabeled transaction>");let{scope:e,isolationScope:t}=rH(this),n=(e||eR()).getClient()||eN();if(!0!==this._sampled){n&&n.recordDroppedEvent("sample_rate","transaction");return}let r=tc(this).filter(e=>{var t;return e!==this&&!((t=e)instanceof rq&&t.isStandaloneSpan())}).map(e=>tr(e)).filter(rY),i=this._attributes[eV],s={contexts:{trace:function(e){let{spanId:t,traceId:n}=e.spanContext(),{data:r,op:i,parent_span_id:s,status:a,origin:o}=tr(e);return ec({parent_span_id:s,span_id:t,trace_id:n,data:r,op:i,status:a,origin:o})}(this)},spans:r.length>1e3?r.sort((e,t)=>e.start_timestamp-t.start_timestamp).slice(0,1e3):r,start_timestamp:this._startTime,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:e,capturedSpanIsolationScope:t,...ec({dynamicSamplingContext:tg(this)})},_metrics_summary:e8(this),...i&&{transaction_info:{source:i}}},a=n4(this._events);return a&&Object.keys(a).length&&(s.measurements=a),s}}function rV(e){return e&&"number"==typeof e||e instanceof Date||Array.isArray(e)}function rY(e){return!!e.start_timestamp&&!!e.timestamp&&!!e.span_id&&!!e.trace_id}let rJ="__SENTRY_SUPPRESS_TRACING__";function rG(e){let t=eA(R());if(t.startInactiveSpan)return t.startInactiveSpan(e);let n=function(e){let t={isStandalone:(e.experimental||{}).standalone,...e};if(e.startTime){let n={...t};return n.startTimestamp=tt(e.startTime),delete n.startTime,n}return t}(e),{forceTransaction:r,parentSpan:i}=e;return(e.scope?t=>eD(e.scope,t):void 0!==i?e=>rK(i,e):e=>e())(()=>{let t=eR(),i=function(e){let t=e[eb];if(!t)return;let n=eN();return(n?n.getOptions():{}).parentSpanIsAlwaysRootSpan?tu(t):t}(t);return e.onlyIfParent&&!i?new rB:function({parentSpan:e,spanArguments:t,forceTransaction:n,scope:r}){var i;let s;if(!e4())return new rB;let a=eO();if(e&&!n)s=function(e,t,n){let{spanId:r,traceId:i}=e.spanContext(),s=!t.getScopeData().sdkProcessingMetadata[rJ]&&ti(e),a=s?new rq({...n,parentSpanId:r,traceId:i,sampled:s}):new rB({traceId:i});tl(e,a);let o=eN();return o&&(o.emit("spanStart",a),n.endTimestamp&&o.emit("spanEnd",a)),a}(e,r,t),tl(e,s);else if(e){let n=tg(e),{traceId:i,spanId:a}=e.spanContext(),o=ti(e);er(s=rX({traceId:i,parentSpanId:a,...t},r,o),tp,n)}else{let{traceId:e,dsc:n,parentSpanId:i,sampled:o}={...a.getPropagationContext(),...r.getPropagationContext()};s=rX({traceId:e,parentSpanId:i,...t},r,o),n&&er(s,tp,n)}return(i=s)&&(er(i,rz,a),er(i,rW,r)),s}({parentSpan:i,spanArguments:n,forceTransaction:r,scope:t})})}function rK(e,t){let n=eA(R());return n.withActiveSpan?n.withActiveSpan(e,t):eD(n=>(eE(n,e||void 0),t(n)))}function rX(e,t,n){let r=eN(),i=r&&r.getOptions()||{},{name:s="",attributes:a}=e,[o,l]=t.getScopeData().sdkProcessingMetadata[rJ]?[!1]:function(e,t){if(!e4(e))return[!1];let n=tG("function"==typeof e.tracesSampler?e.tracesSampler(t):void 0!==t.parentSampled?t.parentSampled:void 0!==e.tracesSampleRate?e.tracesSampleRate:1);return void 0===n?[!1]:n&&Math.random()<n?[!0,n]:[!1,n]}(i,{name:s,parentSampled:n,attributes:a,transactionContext:{name:s,parentSampled:n}}),c=new rq({...e,attributes:{[eV]:"custom",...e.attributes},sampled:o});return void 0!==l&&c.setAttribute(eY,l),r&&r.emit("spanStart",c),c}function rQ(e){return"number"==typeof e&&isFinite(e)}function rZ(e,t,n,{...r}){let i=tr(e).start_timestamp;return i&&i>t&&"function"==typeof e.updateStartTime&&e.updateStartTime(t),rK(e,()=>{let e=rG({startTime:t,...r});return e&&e.end(n),e})}function r0(e){let t,n=eN();if(!n)return;let{name:r,transaction:i,attributes:s,startTime:a}=e,{release:o,environment:l}=n.getOptions(),c=n.getIntegrationByName("Replay"),u=c&&c.getReplayId(),d=eR(),h=d.getUser(),p=void 0!==h?h.email||h.id||h.ip_address:void 0;try{t=d.getScopeData().contexts.profile.profile_id}catch(e){}return rG({name:r,attributes:{release:o,environment:l,user:p||void 0,profile_id:t||void 0,replay_id:u||void 0,transaction:i,"user_agent.original":I.navigator&&I.navigator.userAgent,...s},startTime:a,experimental:{standalone:!0}})}function r1(){return I&&I.addEventListener&&I.performance}function r2(e){return e/1e3}let r3=0,r5={};function r4(e,t,n,r,i=n){var s;let a=t["secureConnection"===(s=n)?"connectEnd":"fetch"===s?"domainLookupStart":`${s}End`],o=t[`${n}Start`];o&&a&&rZ(e,r+r2(o),r+r2(a),{op:`browser.${i}`,name:t.name,attributes:{[eG]:"auto.ui.browser.metrics"}})}function r8(e,t,n,r){let i=t[n];null!=i&&i<0x7fffffff&&(e[r]=i)}let r6=[],r7=new Map,r9={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"},ie={idleTimeout:1e3,finalTimeout:3e4,childSpanTimeout:15e3};function it(e,t={}){let n,r=new Map,i=!1,s="externalFinish",a=!t.disableAutoFinish,o=[],{idleTimeout:l=ie.idleTimeout,finalTimeout:c=ie.finalTimeout,childSpanTimeout:u=ie.childSpanTimeout,beforeSpanEnd:d}=t,h=eN();if(!h||!e4())return new rB;let p=eR(),f=td(),m=function(e){let t=rG(e);return eE(eR(),t),t}(e);function g(){n&&(clearTimeout(n),n=void 0)}function y(e){g(),n=setTimeout(()=>{!i&&0===r.size&&a&&(s="idleTimeout",m.end(e))},l)}function _(e){n=setTimeout(()=>{!i&&a&&(s="heartbeatFailed",m.end(e))},u)}function v(e){i=!0,r.clear(),o.forEach(e=>e()),eE(p,f);let t=tr(m),{start_timestamp:n}=t;if(!n)return;(t.data||{})[eK]||m.setAttribute(eK,s),Q.log(`[Tracing] Idle span "${t.op}" finished`);let a=tc(m).filter(e=>e!==m),u=0;a.forEach(t=>{t.isRecording()&&(t.setStatus({code:2,message:"cancelled"}),t.end(e));let{timestamp:n=0,start_timestamp:r=0}=tr(t),i=r<=e;(!(n-r<=(c+l)/1e3)||!i)&&(m[ta]&&m[ta].delete(t),u++)}),u>0&&m.setAttribute("sentry.idle_span_discarded_spans",u)}return m.end=new Proxy(m.end,{apply(e,t,n){d&&d(m);let[r,...i]=n,s=tt(r||ed()),a=tc(m).filter(e=>e!==m);if(!a.length)return v(s),Reflect.apply(e,t,[s,...i]);let o=a.map(e=>tr(e).timestamp).filter(e=>!!e),l=o.length?Math.max(...o):void 0,u=tr(m).start_timestamp,h=Math.min(u?u+c/1e3:1/0,Math.max(u||-1/0,Math.min(s,l||1/0)));return v(h),Reflect.apply(e,t,[h,...i])}}),o.push(h.on("spanStart",e=>{var t;i||e===m||tr(e).timestamp||tc(m).includes(e)&&(t=e.spanContext().spanId,g(),r.set(t,!0),_(ed()+u/1e3))})),o.push(h.on("spanEnd",e=>{if(!i){var t;t=e.spanContext().spanId,r.has(t)&&r.delete(t),0===r.size&&y(ed()+l/1e3)}})),o.push(h.on("idleSpanEnableAutoFinish",e=>{e===m&&(a=!0,y(),r.size&&_())})),t.disableAutoFinish||y(),setTimeout(()=>{i||(m.setStatus({code:2,message:"deadline_exceeded"}),s="finalTimeout",m.end())},c),m}let ir=!1;function ii(){let e=td(),t=e&&tu(e);t&&t.setStatus({code:2,message:"internal_error"})}function is(e={}){let t=eN();if(!function(){let e=eN();return!!e&&!1!==e.getOptions().enabled&&!!e.getTransport()}()||!t)return{};let n=eA(R());if(n.getTraceData)return n.getTraceData(e);let r=eR(),i=e.span||td(),s=i?function(e){let{traceId:t,spanId:n}=e.spanContext();return e9(t,n,ti(e))}(i):function(e){let{traceId:t,sampled:n,spanId:r}=e.getPropagationContext();return e9(t,r,n)}(r),a=function(e){if(e){var t=Object.entries(e).reduce((e,[t,n])=>(n&&(e[`${e0}${t}`]=n),e),{});return 0!==Object.keys(t).length?Object.entries(t).reduce((e,[t,n],r)=>{let i=`${encodeURIComponent(t)}=${encodeURIComponent(n)}`,s=0===r?i:`${e},${i}`;return s.length>8192?e:s},""):void 0}}(i?tg(i):tm(t,r));return e7.test(s)?{"sentry-trace":s,baggage:a}:(Q.warn("Invalid sentry-trace data. Cannot generate trace data"),{})}function ia(e){return e.split(",").filter(e=>!e.split("=")[0].startsWith(e0)).join(",")}ii.tag="sentry_tracingErrorCallback";let io=new WeakMap,il=new Map,ic={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,trackFetchStreamPerformance:!1};function iu(e){let{url:t}=tr(e).data||{};if(!t||"string"!=typeof t)return;let n=rR("resource",({entries:r})=>{r.forEach(r=>{"resource"===r.entryType&&"initiatorType"in r&&"string"==typeof r.nextHopProtocol&&("fetch"===r.initiatorType||"xmlhttprequest"===r.initiatorType)&&r.name.endsWith(t)&&((function(e){let{name:t,version:n}=function(e){let t="unknown",n="unknown",r="";for(let i of e){if("/"===i){[t,n]=e.split("/");break}if(!isNaN(Number(i))){t="h"===r?"http":r,n=e.split(r)[1];break}r+=i}return r===e&&(t=r),{name:t,version:n}}(e.nextHopProtocol),r=[];return(r.push(["network.protocol.version",n],["network.protocol.name",t]),eh)?[...r,["http.request.redirect_start",id(e.redirectStart)],["http.request.fetch_start",id(e.fetchStart)],["http.request.domain_lookup_start",id(e.domainLookupStart)],["http.request.domain_lookup_end",id(e.domainLookupEnd)],["http.request.connect_start",id(e.connectStart)],["http.request.secure_connection_start",id(e.secureConnectionStart)],["http.request.connection_end",id(e.connectEnd)],["http.request.request_start",id(e.requestStart)],["http.request.response_start",id(e.responseStart)],["http.request.response_end",id(e.responseEnd)]]:r})(r).forEach(t=>e.setAttribute(...t)),setTimeout(n))})})}function id(e=0){return((eh||performance.timeOrigin)+e)/1e3}function ih(e){try{return new URL(e,I.location.origin).href}catch(e){return}}let ip={...ie,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableLongAnimationFrame:!0,enableInp:!0,_experiments:{},...ic};function im(e,t,n){e.emit("startPageLoadSpan",t,n),eR().setTransactionName(t.name);let r=td();return"pageload"===(r&&tr(r).op)?r:void 0}function ig(e,t){eO().setPropagationContext({traceId:ep()}),eR().setPropagationContext({traceId:ep()}),e.emit("startNavigationSpan",t),eR().setTransactionName(t.name);let n=td();return"navigation"===(n&&tr(n).op)?n:void 0}function iy(e){var t;let n=(t=`meta[name=${e}]`,I.document&&I.document.querySelector?I.document.querySelector(t):null);return n?n.getAttribute("content"):void 0}let i_="incomplete-app-router-transaction";function iv(e){try{return new URL(e,"http://example.com/").pathname}catch(e){return"/"}}var iS=n(4998);let ib=iS.events?iS:iS.default,iE=/^(\S+:\\|\/?)([\s\S]*?)((?:\.{1,2}|[^/\\]+?|)(\.[^./\\]*|))(?:[/\\]*)$/;function iw(...e){let t="",n=!1;for(let r=e.length-1;r>=-1&&!n;r--){let i=r>=0?e[r]:"/";i&&(t=`${i}/${t}`,n="/"===i.charAt(0))}return t=(function(e,t){let n=0;for(let t=e.length-1;t>=0;t--){let r=e[t];"."===r?e.splice(t,1):".."===r?(e.splice(t,1),n++):n&&(e.splice(t,1),n--)}if(t)for(;n--;)e.unshift("..");return e})(t.split("/").filter(e=>!!e),!n).join("/"),(n?"/":"")+t||"."}function ik(e){let t=0;for(;t<e.length&&""===e[t];t++);let n=e.length-1;for(;n>=0&&""===e[n];n--);return t>n?[]:e.slice(t,n-t+1)}var iT=n(6953);function ix(e){return"/"===e[e.length-1]?e.slice(0,-1):e}var iI=n(6953);function iC(){return"undefined"!=typeof window&&(!(!("undefined"!=typeof __SENTRY_BROWSER_BUNDLE__&&__SENTRY_BROWSER_BUNDLE__)&&"[object process]"===Object.prototype.toString.call(void 0!==iI?iI:0))||function(){let e=I.process;return!!e&&"renderer"===e.type}())}let iM="sentryReplaySession",iA="Unable to send Replay";function iR(e,t){return null!=e?e:t()}function iO(e){let t,n=e[0],r=1;for(;r<e.length;){let i=e[r],s=e[r+1];if(r+=2,("optionalAccess"===i||"optionalCall"===i)&&null==n)return;"access"===i||"optionalAccess"===i?(t=n,n=s(n)):("call"===i||"optionalCall"===i)&&(n=s((...e)=>n.call(t,...e)),t=void 0)}return n}function iD(e){let t=iO([e,"optionalAccess",e=>e.host]);return iO([t,"optionalAccess",e=>e.shadowRoot])===e}function iN(e){return"[object ShadowRoot]"===Object.prototype.toString.call(e)}function iL(e){try{var t;let n=e.rules||e.cssRules;return n?((t=Array.from(n,iP).join("")).includes(" background-clip: text;")&&!t.includes(" -webkit-background-clip: text;")&&(t=t.replace(/\sbackground-clip:\s*text;/g," -webkit-background-clip: text; background-clip: text;")),t):null}catch(e){return null}}function iP(e){let t;if("styleSheet"in e)try{t=iL(e.styleSheet)||function(e){let{cssText:t}=e;if(t.split('"').length<3)return t;let n=["@import",`url(${JSON.stringify(e.href)})`];return""===e.layerName?n.push("layer"):e.layerName&&n.push(`layer(${e.layerName})`),e.supportsText&&n.push(`supports(${e.supportsText})`),e.media.length&&n.push(e.media.mediaText),n.join(" ")+";"}(e)}catch(e){}else if("selectorText"in e){let t=e.cssText,n=e.selectorText.includes(":"),r="string"==typeof e.style.all&&e.style.all;if(r&&(t=function(e){let t="";for(let n=0;n<e.style.length;n++){let r=e.style,i=r[n],s=r.getPropertyPriority(i);t+=`${i}:${r.getPropertyValue(i)}${s?" !important":""};`}return`${e.selectorText} { ${t} }`}(e)),n&&(t=t.replace(/(\[(?:[\w-]+)[^\\])(:(?:[\w-]+)\])/gm,"$1\\$2")),n||r)return t}return t||e.cssText}!function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"}(nZ||(nZ={}));class i${constructor(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}getId(e){return e?iR(iO([this,"access",e=>e.getMeta,"call",t=>t(e),"optionalAccess",e=>e.id]),()=>-1):-1}getNode(e){return this.idNodeMap.get(e)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(e){return this.nodeMetaMap.get(e)||null}removeNodeFromMap(e){let t=this.getId(e);this.idNodeMap.delete(t),e.childNodes&&e.childNodes.forEach(e=>this.removeNodeFromMap(e))}has(e){return this.idNodeMap.has(e)}hasNode(e){return this.nodeMetaMap.has(e)}add(e,t){let n=t.id;this.idNodeMap.set(n,e),this.nodeMetaMap.set(e,t)}replace(e,t){let n=this.getNode(e);if(n){let e=this.nodeMetaMap.get(n);e&&this.nodeMetaMap.set(t,e)}this.idNodeMap.set(e,t)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function ij({maskInputOptions:e,tagName:t,type:n}){return"OPTION"===t&&(t="SELECT"),!!(e[t.toLowerCase()]||n&&e[n]||"password"===n||"INPUT"===t&&!n&&e.text)}function iF({isMasked:e,element:t,value:n,maskInputFn:r}){let i=n||"";return e?(r&&(i=r(i,t)),"*".repeat(i.length)):i}function iU(e){return e.toLowerCase()}function iB(e){return e.toUpperCase()}let iW="__rrweb_original__";function iz(e){let t=e.type;return e.hasAttribute("data-rr-is-password")?"password":t?iU(t):null}function iH(e,t,n){return"INPUT"===t&&("radio"===n||"checkbox"===n)?e.getAttribute("value")||"":e.value}function iq(e,t){let n;try{n=new URL(e,iR(t,()=>window.location.href))}catch(e){return null}return iR(iO([n.pathname.match(/\.([0-9a-z]+)(?:$)/i),"optionalAccess",e=>e[1]]),()=>null)}let iV={};function iY(e){let t=iV[e];if(t)return t;let n=window.document,r=window[e];if(n&&"function"==typeof n.createElement)try{let t=n.createElement("iframe");t.hidden=!0,n.head.appendChild(t);let i=t.contentWindow;i&&i[e]&&(r=i[e]),n.head.removeChild(t)}catch(e){}return iV[e]=r.bind(window)}function iJ(...e){return iY("setTimeout")(...e)}function iG(...e){return iY("clearTimeout")(...e)}function iK(e){try{return e.contentDocument}catch(e){}}let iX=1,iQ=RegExp("[^a-z0-9-_:]");function iZ(){return iX++}let i0=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,i1=/^(?:[a-z+]+:)?\/\//i,i2=/^www\..*/i,i3=/^(data:)([^,]*),(.*)/i;function i5(e,t){return(e||"").replace(i0,(e,n,r,i,s,a)=>{let o=r||s||a,l=n||i||"";if(!o)return e;if(i1.test(o)||i2.test(o)||i3.test(o))return`url(${l}${o}${l})`;if("/"===o[0])return`url(${l}${(t.indexOf("//")>-1?t.split("/").slice(0,3).join("/"):t.split("/")[0]).split("?")[0]+o}${l})`;let c=t.split("/"),u=o.split("/");for(let e of(c.pop(),u))if("."===e)continue;else".."===e?c.pop():c.push(e);return`url(${l}${c.join("/")}${l})`})}let i4=/^[^ \t\n\r\u000c]+/,i8=/^[, \t\n\r\u000c]+/,i6=new WeakMap;function i7(e,t){return t&&""!==t.trim()?i9(e,t):t}function i9(e,t){let n=i6.get(e);if(n||(n=e.createElement("a"),i6.set(e,n)),t){if(t.startsWith("blob:")||t.startsWith("data:"))return t}else t="";return n.setAttribute("href",t),n.href}function se(e,t,n,r,i,s){if(!r)return r;if("src"===n||"href"===n&&("use"!==t||"#"!==r[0])||"xlink:href"===n&&"#"!==r[0])return i7(e,r);if("background"===n&&("table"===t||"td"===t||"th"===t))return i7(e,r);if("srcset"===n)return function(e,t){if(""===t.trim())return t;let n=0;function r(e){let r,i=e.exec(t.substring(n));return i?(r=i[0],n+=r.length,r):""}let i=[];for(;r(i8),!(n>=t.length);){let s=r(i4);if(","===s.slice(-1))s=i7(e,s.substring(0,s.length-1)),i.push(s);else{let r="";s=i7(e,s);let a=!1;for(;;){let e=t.charAt(n);if(""===e){i.push((s+r).trim());break}if(a)")"===e&&(a=!1);else if(","===e){n+=1,i.push((s+r).trim());break}else"("===e&&(a=!0);r+=e,n+=1}}}return i.join(", ")}(e,r);if("style"===n)return i5(r,i9(e));else if("object"===t&&"data"===n)return i7(e,r);return"function"==typeof s?s(n,r,i):r}function st(e,t,n){return("video"===e||"audio"===e)&&"autoplay"===t}function sn(e,t,n=1/0,r=0){return!e||e.nodeType!==e.ELEMENT_NODE||r>n?-1:t(e)?r:sn(e.parentNode,t,n,r+1)}function sr(e,t){return n=>{if(null===n)return!1;try{if(e){if("string"==typeof e){if(n.matches(`.${e}`))return!0}else if(function(e,t){for(let n=e.classList.length;n--;){let r=e.classList[n];if(t.test(r))return!0}return!1}(n,e))return!0}if(t&&n.matches(t))return!0;return!1}catch(e){return!1}}}function si(e,t,n,r,i,s){try{let a=e.nodeType===e.ELEMENT_NODE?e:e.parentElement;if(null===a)return!1;if("INPUT"===a.tagName){let e=a.getAttribute("autocomplete");if(["current-password","new-password","cc-number","cc-exp","cc-exp-month","cc-exp-year","cc-csc"].includes(e))return!0}let o=-1,l=-1;if(s){if((l=sn(a,sr(r,i)))<0)return!0;o=sn(a,sr(t,n),l>=0?l:1/0)}else{if((o=sn(a,sr(t,n)))<0)return!1;l=sn(a,sr(r,i),o>=0?o:1/0)}return o>=0?!(l>=0)||o<=l:!(l>=0)&&!!s}catch(e){}return!!s}function ss(e){return null==e?"":e.toLowerCase()}function sa(e,t){let n,{doc:r,mirror:i,blockClass:s,blockSelector:a,unblockSelector:o,maskAllText:l,maskTextClass:c,unmaskTextClass:u,maskTextSelector:d,unmaskTextSelector:h,skipChild:p=!1,inlineStylesheet:f=!0,maskInputOptions:m={},maskAttributeFn:g,maskTextFn:y,maskInputFn:_,slimDOMOptions:b,dataURLOptions:E={},inlineImages:w=!1,recordCanvas:k=!1,onSerialize:T,onIframeLoad:x,iframeLoadTimeout:I=5e3,onStylesheetLoad:C,stylesheetLoadTimeout:M=5e3,keepIframeSrcFn:A=()=>!1,newlyAddedElement:R=!1}=t,{preserveWhiteSpace:O=!0}=t,D=function(e,t){let{doc:n,mirror:r,blockClass:i,blockSelector:s,unblockSelector:a,maskAllText:o,maskAttributeFn:l,maskTextClass:c,unmaskTextClass:u,maskTextSelector:d,unmaskTextSelector:h,inlineStylesheet:p,maskInputOptions:f={},maskTextFn:m,maskInputFn:g,dataURLOptions:y={},inlineImages:_,recordCanvas:b,keepIframeSrcFn:E,newlyAddedElement:w=!1}=t,k=function(e,t){if(!t.hasNode(e))return;let n=t.getId(e);return 1===n?void 0:n}(n,r);switch(e.nodeType){case e.DOCUMENT_NODE:if("CSS1Compat"!==e.compatMode)return{type:nZ.Document,childNodes:[],compatMode:e.compatMode};return{type:nZ.Document,childNodes:[]};case e.DOCUMENT_TYPE_NODE:return{type:nZ.DocumentType,name:e.name,publicId:e.publicId,systemId:e.systemId,rootId:k};case e.ELEMENT_NODE:return function(e,t){let n,{doc:r,blockClass:i,blockSelector:s,unblockSelector:a,inlineStylesheet:o,maskInputOptions:l={},maskAttributeFn:c,maskInputFn:u,dataURLOptions:d={},inlineImages:h,recordCanvas:p,keepIframeSrcFn:f,newlyAddedElement:m=!1,rootId:g,maskAllText:y,maskTextClass:_,unmaskTextClass:b,maskTextSelector:E,unmaskTextSelector:w}=t,k=function(e,t,n,r){try{if(r&&e.matches(r))return!1;if("string"==typeof t){if(e.classList.contains(t))return!0}else for(let n=e.classList.length;n--;){let r=e.classList[n];if(t.test(r))return!0}if(n)return e.matches(n)}catch(e){}return!1}(e,i,s,a),T=function(e){if(e instanceof HTMLFormElement)return"form";let t=iU(e.tagName);return iQ.test(t)?"div":t}(e),x={},I=e.attributes.length;for(let t=0;t<I;t++){let n=e.attributes[t];n.name&&!st(T,n.name,n.value)&&(x[n.name]=se(r,T,iU(n.name),n.value,e,c))}if("link"===T&&o){let t=Array.from(r.styleSheets).find(t=>t.href===e.href),n=null;t&&(n=iL(t)),n&&(x.rel=null,x.href=null,x.crossorigin=null,x._cssText=i5(n,t.href))}if("style"===T&&e.sheet&&!(e.innerText||e.textContent||"").trim().length){let t=iL(e.sheet);t&&(x._cssText=i5(t,i9(r)))}if("input"===T||"textarea"===T||"select"===T||"option"===T){let t=iz(e),n=iH(e,iB(T),t),r=e.checked;if("submit"!==t&&"button"!==t&&n){let r=si(e,_,E,b,w,ij({type:t,tagName:iB(T),maskInputOptions:l}));x.value=iF({isMasked:r,element:e,value:n,maskInputFn:u})}r&&(x.checked=r)}if("option"===T&&(e.selected&&!l.select?x.selected=!0:delete x.selected),"canvas"===T&&p){if("2d"===e.__context)!function(e){let t=e.getContext("2d");if(!t)return!0;for(let n=0;n<e.width;n+=50)for(let r=0;r<e.height;r+=50){let i=t.getImageData;if(new Uint32Array((iW in i?i[iW]:i).call(t,n,r,Math.min(50,e.width-n),Math.min(50,e.height-r)).data.buffer).some(e=>0!==e))return!1}return!0}(e)&&(x.rr_dataURL=e.toDataURL(d.type,d.quality));else if(!("__context"in e)){let t=e.toDataURL(d.type,d.quality),n=r.createElement("canvas");n.width=e.width,n.height=e.height,t!==n.toDataURL(d.type,d.quality)&&(x.rr_dataURL=t)}}if("img"===T&&h){v||(S=(v=r.createElement("canvas")).getContext("2d"));let t=e.currentSrc||e.getAttribute("src")||"<unknown-src>",n=e.crossOrigin,i=()=>{e.removeEventListener("load",i);try{v.width=e.naturalWidth,v.height=e.naturalHeight,S.drawImage(e,0,0),x.rr_dataURL=v.toDataURL(d.type,d.quality)}catch(n){if("anonymous"!==e.crossOrigin){e.crossOrigin="anonymous",e.complete&&0!==e.naturalWidth?i():e.addEventListener("load",i);return}console.warn(`Cannot inline img src=${t}! Error: ${n}`)}"anonymous"===e.crossOrigin&&(n?x.crossOrigin=n:e.removeAttribute("crossorigin"))};e.complete&&0!==e.naturalWidth?i():e.addEventListener("load",i)}if(("audio"===T||"video"===T)&&(x.rr_mediaState=e.paused?"paused":"played",x.rr_mediaCurrentTime=e.currentTime),!m&&(e.scrollLeft&&(x.rr_scrollLeft=e.scrollLeft),e.scrollTop&&(x.rr_scrollTop=e.scrollTop)),k){let{width:t,height:n}=e.getBoundingClientRect();x={class:x.class,rr_width:`${t}px`,rr_height:`${n}px`}}"iframe"!==T||f(x.src)||(k||iK(e)||(x.rr_src=x.src),delete x.src);try{customElements.get(T)&&(n=!0)}catch(e){}return{type:nZ.Element,tagName:T,attributes:x,childNodes:[],isSVG:!!("svg"===e.tagName||e.ownerSVGElement)||void 0,needBlock:k,rootId:g,isCustom:n}}(e,{doc:n,blockClass:i,blockSelector:s,unblockSelector:a,inlineStylesheet:p,maskAttributeFn:l,maskInputOptions:f,maskInputFn:g,dataURLOptions:y,inlineImages:_,recordCanvas:b,keepIframeSrcFn:E,newlyAddedElement:w,rootId:k,maskAllText:o,maskTextClass:c,unmaskTextClass:u,maskTextSelector:d,unmaskTextSelector:h});case e.TEXT_NODE:return function(e,t){let{maskAllText:n,maskTextClass:r,unmaskTextClass:i,maskTextSelector:s,unmaskTextSelector:a,maskTextFn:o,maskInputOptions:l,maskInputFn:c,rootId:u}=t,d=e.parentNode&&e.parentNode.tagName,h=e.textContent,p="STYLE"===d||void 0,f="SCRIPT"===d||void 0,m="TEXTAREA"===d||void 0;if(p&&h){try{e.nextSibling||e.previousSibling||iO([e,"access",e=>e.parentNode,"access",e=>e.sheet,"optionalAccess",e=>e.cssRules])&&(h=iL(e.parentNode.sheet))}catch(t){console.warn(`Cannot get CSS styles from text's parentNode. Error: ${t}`,e)}h=i5(h,i9(t.doc))}f&&(h="SCRIPT_PLACEHOLDER");let g=si(e,r,s,i,a,n);return p||f||m||!h||!g||(h=o?o(h,e.parentElement):h.replace(/[\S]/g,"*")),m&&h&&(l.textarea||g)&&(h=c?c(h,e.parentNode):h.replace(/[\S]/g,"*")),"OPTION"===d&&h&&(h=iF({isMasked:si(e,r,s,i,a,ij({type:null,tagName:d,maskInputOptions:l})),element:e,value:h,maskInputFn:c})),{type:nZ.Text,textContent:h||"",isStyle:p,rootId:u}}(e,{doc:n,maskAllText:o,maskTextClass:c,unmaskTextClass:u,maskTextSelector:d,unmaskTextSelector:h,maskTextFn:m,maskInputOptions:f,maskInputFn:g,rootId:k});case e.CDATA_SECTION_NODE:return{type:nZ.CDATA,textContent:"",rootId:k};case e.COMMENT_NODE:return{type:nZ.Comment,textContent:e.textContent||"",rootId:k};default:return!1}}(e,{doc:r,mirror:i,blockClass:s,blockSelector:a,maskAllText:l,unblockSelector:o,maskTextClass:c,unmaskTextClass:u,maskTextSelector:d,unmaskTextSelector:h,inlineStylesheet:f,maskInputOptions:m,maskAttributeFn:g,maskTextFn:y,maskInputFn:_,dataURLOptions:E,inlineImages:w,recordCanvas:k,keepIframeSrcFn:A,newlyAddedElement:R});if(!D)return console.warn(e,"not serialized"),null;n=i.hasNode(e)?i.getId(e):!function(e,t){if(t.comment&&e.type===nZ.Comment)return!0;if(e.type===nZ.Element){if(t.script&&("script"===e.tagName||"link"===e.tagName&&("preload"===e.attributes.rel||"modulepreload"===e.attributes.rel)||"link"===e.tagName&&"prefetch"===e.attributes.rel&&"string"==typeof e.attributes.href&&"js"===iq(e.attributes.href)))return!0;else if(t.headFavicon&&("link"===e.tagName&&"shortcut icon"===e.attributes.rel||"meta"===e.tagName&&(ss(e.attributes.name).match(/^msapplication-tile(image|color)$/)||"application-name"===ss(e.attributes.name)||"icon"===ss(e.attributes.rel)||"apple-touch-icon"===ss(e.attributes.rel)||"shortcut icon"===ss(e.attributes.rel))))return!0;else if("meta"===e.tagName){if(t.headMetaDescKeywords&&ss(e.attributes.name).match(/^description|keywords$/))return!0;else if(t.headMetaSocial&&(ss(e.attributes.property).match(/^(og|twitter|fb):/)||ss(e.attributes.name).match(/^(og|twitter):/)||"pinterest"===ss(e.attributes.name)))return!0;else if(t.headMetaRobots&&("robots"===ss(e.attributes.name)||"googlebot"===ss(e.attributes.name)||"bingbot"===ss(e.attributes.name)))return!0;else if(t.headMetaHttpEquiv&&void 0!==e.attributes["http-equiv"])return!0;else if(t.headMetaAuthorship&&("author"===ss(e.attributes.name)||"generator"===ss(e.attributes.name)||"framework"===ss(e.attributes.name)||"publisher"===ss(e.attributes.name)||"progid"===ss(e.attributes.name)||ss(e.attributes.property).match(/^article:/)||ss(e.attributes.property).match(/^product:/)))return!0;else if(t.headMetaVerification&&("google-site-verification"===ss(e.attributes.name)||"yandex-verification"===ss(e.attributes.name)||"csrf-token"===ss(e.attributes.name)||"p:domain_verify"===ss(e.attributes.name)||"verify-v1"===ss(e.attributes.name)||"verification"===ss(e.attributes.name)||"shopify-checkout-api-token"===ss(e.attributes.name)))return!0}}return!1}(D,b)&&(O||D.type!==nZ.Text||D.isStyle||D.textContent.replace(/^\s+|\s+$/gm,"").length)?iZ():-2;let N=Object.assign(D,{id:n});if(i.add(e,N),-2===n)return null;T&&T(e);let L=!p;if(N.type===nZ.Element){L=L&&!N.needBlock,delete N.needBlock;let t=e.shadowRoot;t&&iN(t)&&(N.isShadowHost=!0)}if((N.type===nZ.Document||N.type===nZ.Element)&&L){b.headWhitespace&&N.type===nZ.Element&&"head"===N.tagName&&(O=!1);let t={doc:r,mirror:i,blockClass:s,blockSelector:a,maskAllText:l,unblockSelector:o,maskTextClass:c,unmaskTextClass:u,maskTextSelector:d,unmaskTextSelector:h,skipChild:p,inlineStylesheet:f,maskInputOptions:m,maskAttributeFn:g,maskTextFn:y,maskInputFn:_,slimDOMOptions:b,dataURLOptions:E,inlineImages:w,recordCanvas:k,preserveWhiteSpace:O,onSerialize:T,onIframeLoad:x,iframeLoadTimeout:I,onStylesheetLoad:C,stylesheetLoadTimeout:M,keepIframeSrcFn:A};for(let n of Array.from(e.childNodes)){let e=sa(n,t);e&&N.childNodes.push(e)}if(e.nodeType===e.ELEMENT_NODE&&e.shadowRoot)for(let n of Array.from(e.shadowRoot.childNodes)){let r=sa(n,t);r&&(iN(e.shadowRoot)&&(r.isShadow=!0),N.childNodes.push(r))}}return e.parentNode&&iD(e.parentNode)&&iN(e.parentNode)&&(N.isShadow=!0),N.type===nZ.Element&&"iframe"===N.tagName&&function(e,t,n){let r,i=e.contentWindow;if(!i)return;let s=!1;try{r=i.document.readyState}catch(e){return}if("complete"!==r){let r=iJ(()=>{s||(t(),s=!0)},n);e.addEventListener("load",()=>{iG(r),s=!0,t()});return}let a="about:blank";if(i.location.href!==a||e.src===a||""===e.src)return iJ(t,0),e.addEventListener("load",t);e.addEventListener("load",t)}(e,()=>{let t=iK(e);if(t&&x){let n=sa(t,{doc:t,mirror:i,blockClass:s,blockSelector:a,unblockSelector:o,maskAllText:l,maskTextClass:c,unmaskTextClass:u,maskTextSelector:d,unmaskTextSelector:h,skipChild:!1,inlineStylesheet:f,maskInputOptions:m,maskAttributeFn:g,maskTextFn:y,maskInputFn:_,slimDOMOptions:b,dataURLOptions:E,inlineImages:w,recordCanvas:k,preserveWhiteSpace:O,onSerialize:T,onIframeLoad:x,iframeLoadTimeout:I,onStylesheetLoad:C,stylesheetLoadTimeout:M,keepIframeSrcFn:A});n&&x(e,n)}},I),N.type===nZ.Element&&"link"===N.tagName&&"string"==typeof N.attributes.rel&&("stylesheet"===N.attributes.rel||"preload"===N.attributes.rel&&"string"==typeof N.attributes.href&&"css"===iq(N.attributes.href))&&function(e,t,n){let r,i=!1;try{r=e.sheet}catch(e){return}if(r)return;let s=iJ(()=>{i||(t(),i=!0)},n);e.addEventListener("load",()=>{iG(s),i=!0,t()})}(e,()=>{if(C){let t=sa(e,{doc:r,mirror:i,blockClass:s,blockSelector:a,unblockSelector:o,maskAllText:l,maskTextClass:c,unmaskTextClass:u,maskTextSelector:d,unmaskTextSelector:h,skipChild:!1,inlineStylesheet:f,maskInputOptions:m,maskAttributeFn:g,maskTextFn:y,maskInputFn:_,slimDOMOptions:b,dataURLOptions:E,inlineImages:w,recordCanvas:k,preserveWhiteSpace:O,onSerialize:T,onIframeLoad:x,iframeLoadTimeout:I,onStylesheetLoad:C,stylesheetLoadTimeout:M,keepIframeSrcFn:A});t&&C(e,t)}},M),N}function so(e){let t,n=e[0],r=1;for(;r<e.length;){let i=e[r],s=e[r+1];if(r+=2,("optionalAccess"===i||"optionalCall"===i)&&null==n)return;"access"===i||"optionalAccess"===i?(t=n,n=s(n)):("call"===i||"optionalCall"===i)&&(n=s((...e)=>n.call(t,...e)),t=void 0)}return n}function sl(e,t,n=document){let r={capture:!0,passive:!0};return n.addEventListener(e,t,r),()=>n.removeEventListener(e,t,r)}let sc="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.",su={map:{},getId:()=>(console.error(sc),-1),getNode:()=>(console.error(sc),null),removeNodeFromMap(){console.error(sc)},has:()=>(console.error(sc),!1),reset(){console.error(sc)}};function sd(e,t,n={}){let r=null,i=0;return function(...s){let a=Date.now();i||!1!==n.leading||(i=a);let o=t-(a-i),l=this;o<=0||o>t?(r&&(function(...e){sC("clearTimeout")(...e)}(r),r=null),i=a,e.apply(l,s)):r||!1===n.trailing||(r=sM(()=>{i=!1===n.leading?0:Date.now(),r=null,e.apply(l,s)},o))}}function sh(e,t,n){try{if(!(t in e))return()=>{};let r=e[t],i=n(r);return"function"==typeof i&&(i.prototype=i.prototype||{},Object.defineProperties(i,{__rrweb_original__:{enumerable:!1,value:r}})),e[t]=i,()=>{e[t]=r}}catch(e){return()=>{}}}"undefined"!=typeof window&&window.Proxy&&window.Reflect&&(su=new Proxy(su,{get:(e,t,n)=>("map"===t&&console.error(sc),Reflect.get(e,t,n))}));let sp=Date.now;function sf(e){let t=e.document;return{left:t.scrollingElement?t.scrollingElement.scrollLeft:void 0!==e.pageXOffset?e.pageXOffset:so([t,"optionalAccess",e=>e.documentElement,"access",e=>e.scrollLeft])||so([t,"optionalAccess",e=>e.body,"optionalAccess",e=>e.parentElement,"optionalAccess",e=>e.scrollLeft])||so([t,"optionalAccess",e=>e.body,"optionalAccess",e=>e.scrollLeft])||0,top:t.scrollingElement?t.scrollingElement.scrollTop:void 0!==e.pageYOffset?e.pageYOffset:so([t,"optionalAccess",e=>e.documentElement,"access",e=>e.scrollTop])||so([t,"optionalAccess",e=>e.body,"optionalAccess",e=>e.parentElement,"optionalAccess",e=>e.scrollTop])||so([t,"optionalAccess",e=>e.body,"optionalAccess",e=>e.scrollTop])||0}}function sm(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function sg(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function sy(e){return e?e.nodeType===e.ELEMENT_NODE?e:e.parentElement:null}function s_(e,t,n,r,i){if(!e)return!1;let s=sy(e);if(!s)return!1;let a=sr(t,n);if(!i){let e=r&&s.matches(r);return a(s)&&!e}let o=sn(s,a),l=-1;return!(o<0)&&(r&&(l=sn(s,sr(null,r))),o>-1&&l<0||o<l)}function sv(e,t){return -2===t.getId(e)}function sS(e){return!!e.changedTouches}function sb(e,t){return!!("IFRAME"===e.nodeName&&t.getMeta(e))}function sE(e,t){return!!("LINK"===e.nodeName&&e.nodeType===e.ELEMENT_NODE&&e.getAttribute&&"stylesheet"===e.getAttribute("rel")&&t.getMeta(e))}function sw(e){return!!so([e,"optionalAccess",e=>e.shadowRoot])}/[1-9][0-9]{12}/.test(Date.now().toString())||(sp=()=>new Date().getTime());class sk{constructor(){this.id=1,this.styleIDMap=new WeakMap,this.idStyleMap=new Map}getId(e){var t,n;return t=this.styleIDMap.get(e),n=()=>-1,null!=t?t:n()}has(e){return this.styleIDMap.has(e)}add(e,t){let n;return this.has(e)?this.getId(e):(n=void 0===t?this.id++:t,this.styleIDMap.set(e,n),this.idStyleMap.set(n,e),n)}getStyle(e){return this.idStyleMap.get(e)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}}function sT(e){let t=null;return so([e,"access",e=>e.getRootNode,"optionalCall",e=>e(),"optionalAccess",e=>e.nodeType])===Node.DOCUMENT_FRAGMENT_NODE&&e.getRootNode().host&&(t=e.getRootNode().host),t}function sx(e){let t=e.ownerDocument;return!!t&&(t.contains(e)||function(e){let t=e.ownerDocument;if(!t)return!1;let n=function(e){let t,n=e;for(;t=sT(n);)n=t;return n}(e);return t.contains(n)}(e))}let sI={};function sC(e){let t=sI[e];if(t)return t;let n=window.document,r=window[e];if(n&&"function"==typeof n.createElement)try{let t=n.createElement("iframe");t.hidden=!0,n.head.appendChild(t);let i=t.contentWindow;i&&i[e]&&(r=i[e]),n.head.removeChild(t)}catch(e){}return sI[e]=r.bind(window)}function sM(...e){return sC("setTimeout")(...e)}var sA=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))(sA||{}),sR=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e[e.CustomElement=16]="CustomElement",e))(sR||{}),sO=(e=>(e[e.MouseUp=0]="MouseUp",e[e.MouseDown=1]="MouseDown",e[e.Click=2]="Click",e[e.ContextMenu=3]="ContextMenu",e[e.DblClick=4]="DblClick",e[e.Focus=5]="Focus",e[e.Blur=6]="Blur",e[e.TouchStart=7]="TouchStart",e[e.TouchMove_Departed=8]="TouchMove_Departed",e[e.TouchEnd=9]="TouchEnd",e[e.TouchCancel=10]="TouchCancel",e))(sO||{}),sD=(e=>(e[e.Mouse=0]="Mouse",e[e.Pen=1]="Pen",e[e.Touch=2]="Touch",e))(sD||{});function sN(e){try{return e.contentDocument}catch(e){}}!function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"}(n0||(n0={})),function(e){e[e.PLACEHOLDER=0]="PLACEHOLDER",e[e.ELEMENT_NODE=1]="ELEMENT_NODE",e[e.ATTRIBUTE_NODE=2]="ATTRIBUTE_NODE",e[e.TEXT_NODE=3]="TEXT_NODE",e[e.CDATA_SECTION_NODE=4]="CDATA_SECTION_NODE",e[e.ENTITY_REFERENCE_NODE=5]="ENTITY_REFERENCE_NODE",e[e.ENTITY_NODE=6]="ENTITY_NODE",e[e.PROCESSING_INSTRUCTION_NODE=7]="PROCESSING_INSTRUCTION_NODE",e[e.COMMENT_NODE=8]="COMMENT_NODE",e[e.DOCUMENT_NODE=9]="DOCUMENT_NODE",e[e.DOCUMENT_TYPE_NODE=10]="DOCUMENT_TYPE_NODE",e[e.DOCUMENT_FRAGMENT_NODE=11]="DOCUMENT_FRAGMENT_NODE"}(n1||(n1={}));class sL{constructor(){this.length=0,this.head=null,this.tail=null}get(e){if(e>=this.length)throw Error("Position outside of list range");let t=this.head;for(let n=0;n<e;n++)t=function(e){let t,n=e[0],r=1;for(;r<e.length;){let i=e[r],s=e[r+1];if(r+=2,("optionalAccess"===i||"optionalCall"===i)&&null==n)return;"access"===i||"optionalAccess"===i?(t=n,n=s(n)):("call"===i||"optionalCall"===i)&&(n=s((...e)=>n.call(t,...e)),t=void 0)}return n}([t,"optionalAccess",e=>e.next])||null;return t}addNode(e){let t={value:e,previous:null,next:null};if(e.__ln=t,e.previousSibling&&"__ln"in e.previousSibling){let n=e.previousSibling.__ln.next;t.next=n,t.previous=e.previousSibling.__ln,e.previousSibling.__ln.next=t,n&&(n.previous=t)}else if(e.nextSibling&&"__ln"in e.nextSibling&&e.nextSibling.__ln.previous){let n=e.nextSibling.__ln.previous;t.previous=n,t.next=e.nextSibling.__ln,e.nextSibling.__ln.previous=t,n&&(n.next=t)}else this.head&&(this.head.previous=t),t.next=this.head,this.head=t;null===t.next&&(this.tail=t),this.length++}removeNode(e){let t=e.__ln;this.head&&(t.previous?(t.previous.next=t.next,t.next?t.next.previous=t.previous:this.tail=t.previous):(this.head=t.next,this.head?this.head.previous=null:this.tail=null),e.__ln&&delete e.__ln,this.length--)}}let sP=(e,t)=>`${e}@${t}`;class s${constructor(){this.frozen=!1,this.locked=!1,this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.mapRemoves=[],this.movedMap={},this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.processMutations=e=>{e.forEach(this.processMutation),this.emit()},this.emit=()=>{if(this.frozen||this.locked)return;let e=[],t=new Set,n=new sL,r=e=>{let t=e,n=-2;for(;-2===n;)n=(t=t&&t.nextSibling)&&this.mirror.getId(t);return n},i=i=>{if(!i.parentNode||!sx(i))return;let s=iD(i.parentNode)?this.mirror.getId(sT(i)):this.mirror.getId(i.parentNode),a=r(i);if(-1===s||-1===a)return n.addNode(i);let o=sa(i,{doc:this.doc,mirror:this.mirror,blockClass:this.blockClass,blockSelector:this.blockSelector,maskAllText:this.maskAllText,unblockSelector:this.unblockSelector,maskTextClass:this.maskTextClass,unmaskTextClass:this.unmaskTextClass,maskTextSelector:this.maskTextSelector,unmaskTextSelector:this.unmaskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:this.inlineStylesheet,maskInputOptions:this.maskInputOptions,maskAttributeFn:this.maskAttributeFn,maskTextFn:this.maskTextFn,maskInputFn:this.maskInputFn,slimDOMOptions:this.slimDOMOptions,dataURLOptions:this.dataURLOptions,recordCanvas:this.recordCanvas,inlineImages:this.inlineImages,onSerialize:e=>{sb(e,this.mirror)&&!s_(e,this.blockClass,this.blockSelector,this.unblockSelector,!1)&&this.iframeManager.addIframe(e),sE(e,this.mirror)&&this.stylesheetManager.trackLinkElement(e),sw(i)&&this.shadowDomManager.addShadowRoot(i.shadowRoot,this.doc)},onIframeLoad:(e,t)=>{s_(e,this.blockClass,this.blockSelector,this.unblockSelector,!1)||(this.iframeManager.attachIframe(e,t),e.contentWindow&&this.canvasManager.addWindow(e.contentWindow),this.shadowDomManager.observeAttachShadow(e))},onStylesheetLoad:(e,t)=>{this.stylesheetManager.attachLinkElement(e,t)}});o&&(e.push({parentId:s,nextId:a,node:o}),t.add(o.id))};for(;this.mapRemoves.length;)this.mirror.removeNodeFromMap(this.mapRemoves.shift());for(let e of this.movedSet)(!sF(this.removes,e,this.mirror)||this.movedSet.has(e.parentNode))&&i(e);for(let e of this.addedSet)sU(this.droppedSet,e)||sF(this.removes,e,this.mirror)?sU(this.movedSet,e)?i(e):this.droppedSet.add(e):i(e);let s=null;for(;n.length;){let e=null;if(s){let t=this.mirror.getId(s.value.parentNode),n=r(s.value);-1!==t&&-1!==n&&(e=s)}if(!e){let t=n.tail;for(;t;){let n=t;if(t=t.previous,n){let t=this.mirror.getId(n.value.parentNode);if(-1===r(n.value))continue;if(-1!==t){e=n;break}{let t=n.value;if(t.parentNode&&t.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE){let r=t.parentNode.host;if(-1!==this.mirror.getId(r)){e=n;break}}}}}}if(!e){for(;n.head;)n.removeNode(n.head.value);break}s=e.previous,n.removeNode(e.value),i(e.value)}let a={texts:this.texts.map(e=>({id:this.mirror.getId(e.node),value:e.value})).filter(e=>!t.has(e.id)).filter(e=>this.mirror.has(e.id)),attributes:this.attributes.map(e=>{let{attributes:t}=e;if("string"==typeof t.style){let n=JSON.stringify(e.styleDiff),r=JSON.stringify(e._unchangedStyles);n.length<t.style.length&&(n+r).split("var(").length===t.style.split("var(").length&&(t.style=e.styleDiff)}return{id:this.mirror.getId(e.node),attributes:t}}).filter(e=>!t.has(e.id)).filter(e=>this.mirror.has(e.id)),removes:this.removes,adds:e};(a.texts.length||a.attributes.length||a.removes.length||a.adds.length)&&(this.texts=[],this.attributes=[],this.attributeMap=new WeakMap,this.removes=[],this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.movedMap={},this.mutationCb(a))},this.processMutation=e=>{if(!sv(e.target,this.mirror))switch(e.type){case"characterData":{let t=e.target.textContent;s_(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||t===e.oldValue||this.texts.push({value:si(e.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,this.maskAllText)&&t?this.maskTextFn?this.maskTextFn(t,sy(e.target)):t.replace(/[\S]/g,"*"):t,node:e.target});break}case"attributes":{let t=e.target,n=e.attributeName,r=e.target.getAttribute(n);if("value"===n){let n=iz(t),i=t.tagName;r=iH(t,i,n);let s=ij({maskInputOptions:this.maskInputOptions,tagName:i,type:n});r=iF({isMasked:si(e.target,this.maskTextClass,this.maskTextSelector,this.unmaskTextClass,this.unmaskTextSelector,s),element:t,value:r,maskInputFn:this.maskInputFn})}if(s_(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||r===e.oldValue)return;let i=this.attributeMap.get(e.target);if("IFRAME"===t.tagName&&"src"===n&&!this.keepIframeSrcFn(r)){if(sN(t))return;n="rr_src"}if(i||(i={node:e.target,attributes:{},styleDiff:{},_unchangedStyles:{}},this.attributes.push(i),this.attributeMap.set(e.target,i)),"type"===n&&"INPUT"===t.tagName&&"password"===(e.oldValue||"").toLowerCase()&&t.setAttribute("data-rr-is-password","true"),!st(t.tagName,n)&&(i.attributes[n]=se(this.doc,iU(t.tagName),iU(n),r,t,this.maskAttributeFn),"style"===n)){if(!this.unattachedDoc)try{this.unattachedDoc=document.implementation.createHTMLDocument()}catch(e){this.unattachedDoc=this.doc}let n=this.unattachedDoc.createElement("span");for(let r of(e.oldValue&&n.setAttribute("style",e.oldValue),Array.from(t.style))){let e=t.style.getPropertyValue(r),s=t.style.getPropertyPriority(r);e!==n.style.getPropertyValue(r)||s!==n.style.getPropertyPriority(r)?""===s?i.styleDiff[r]=e:i.styleDiff[r]=[e,s]:i._unchangedStyles[r]=[e,s]}for(let e of Array.from(n.style))""===t.style.getPropertyValue(e)&&(i.styleDiff[e]=!1)}break}case"childList":if(s_(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!0))return;e.addedNodes.forEach(t=>this.genAdds(t,e.target)),e.removedNodes.forEach(t=>{let n=this.mirror.getId(t),r=iD(e.target)?this.mirror.getId(e.target.host):this.mirror.getId(e.target);s_(e.target,this.blockClass,this.blockSelector,this.unblockSelector,!1)||sv(t,this.mirror)||-1===this.mirror.getId(t)||(this.addedSet.has(t)?(sj(this.addedSet,t),this.droppedSet.add(t)):this.addedSet.has(e.target)&&-1===n||function e(t,n){if(iD(t))return!1;let r=n.getId(t);return!n.has(r)||(!t.parentNode||t.parentNode.nodeType!==t.DOCUMENT_NODE)&&(!t.parentNode||e(t.parentNode,n))}(e.target,this.mirror)||(this.movedSet.has(t)&&this.movedMap[sP(n,r)]?sj(this.movedSet,t):this.removes.push({parentId:r,id:n,isShadow:!!(iD(e.target)&&iN(e.target))||void 0})),this.mapRemoves.push(t))})}},this.genAdds=(e,t)=>{if(!this.processedNodeManager.inOtherBuffer(e,this)&&!(this.addedSet.has(e)||this.movedSet.has(e))){if(this.mirror.hasNode(e)){if(sv(e,this.mirror))return;this.movedSet.add(e);let n=null;t&&this.mirror.hasNode(t)&&(n=this.mirror.getId(t)),n&&-1!==n&&(this.movedMap[sP(this.mirror.getId(e),n)]=!0)}else this.addedSet.add(e),this.droppedSet.delete(e);!s_(e,this.blockClass,this.blockSelector,this.unblockSelector,!1)&&(e.childNodes.forEach(e=>this.genAdds(e)),sw(e)&&e.shadowRoot.childNodes.forEach(t=>{this.processedNodeManager.add(t,this),this.genAdds(t,e)}))}}}init(e){["mutationCb","blockClass","blockSelector","unblockSelector","maskAllText","maskTextClass","unmaskTextClass","maskTextSelector","unmaskTextSelector","inlineStylesheet","maskInputOptions","maskAttributeFn","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager","processedNodeManager"].forEach(t=>{this[t]=e[t]})}freeze(){this.frozen=!0,this.canvasManager.freeze()}unfreeze(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}isFrozen(){return this.frozen}lock(){this.locked=!0,this.canvasManager.lock()}unlock(){this.locked=!1,this.canvasManager.unlock(),this.emit()}reset(){this.shadowDomManager.reset(),this.canvasManager.reset()}}function sj(e,t){e.delete(t),t.childNodes.forEach(t=>sj(e,t))}function sF(e,t,n){return 0!==e.length&&function(e,t,n){let r=t.parentNode;for(;r;){let t=n.getId(r);if(e.some(e=>e.id===t))return!0;r=r.parentNode}return!1}(e,t,n)}function sU(e,t){return 0!==e.size&&function e(t,n){let{parentNode:r}=n;return!!r&&(!!t.has(r)||e(t,r))}(e,t)}let sB=e=>b?(...t)=>{try{return e(...t)}catch(e){if(b&&!0===b(e))return()=>{};throw e}}:e;function sW(e){let t,n=e[0],r=1;for(;r<e.length;){let i=e[r],s=e[r+1];if(r+=2,("optionalAccess"===i||"optionalCall"===i)&&null==n)return;"access"===i||"optionalAccess"===i?(t=n,n=s(n)):("call"===i||"optionalCall"===i)&&(n=s((...e)=>n.call(t,...e)),t=void 0)}return n}let sz=[];function sH(e){try{if("composedPath"in e){let t=e.composedPath();if(t.length)return t[0]}else if("path"in e&&e.path.length)return e.path[0]}catch(e){}return e&&e.target}function sq(e,t){let n=new s$;sz.push(n),n.init(e);let r=window.MutationObserver||window.__rrMutationObserver,i=sW([window,"optionalAccess",e=>e.Zone,"optionalAccess",e=>e.__symbol__,"optionalCall",e=>e("MutationObserver")]);i&&window[i]&&(r=window[i]);let s=new r(sB(t=>{e.onMutation&&!1===e.onMutation(t)||n.processMutations.bind(n)(t)}));return s.observe(t,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),s}function sV({scrollCb:e,doc:t,mirror:n,blockClass:r,blockSelector:i,unblockSelector:s,sampling:a}){return sl("scroll",sB(sd(sB(a=>{let o=sH(a);if(!o||s_(o,r,i,s,!0))return;let l=n.getId(o);if(o===t&&t.defaultView){let n=sf(t.defaultView);e({id:l,x:n.left,y:n.top})}else e({id:l,x:o.scrollLeft,y:o.scrollTop})}),a.scroll||100)),t)}let sY=["INPUT","TEXTAREA","SELECT"],sJ=new WeakMap;function sG(e){var t=[];if(sZ("CSSGroupingRule")&&e.parentRule instanceof CSSGroupingRule||sZ("CSSMediaRule")&&e.parentRule instanceof CSSMediaRule||sZ("CSSSupportsRule")&&e.parentRule instanceof CSSSupportsRule||sZ("CSSConditionRule")&&e.parentRule instanceof CSSConditionRule){let n=Array.from(e.parentRule.cssRules).indexOf(e);t.unshift(n)}else if(e.parentStyleSheet){let n=Array.from(e.parentStyleSheet.cssRules).indexOf(e);t.unshift(n)}return t}function sK(e,t,n){let r,i;return e?(e.ownerNode?r=t.getId(e.ownerNode):i=n.getId(e),{styleId:i,id:r}):{}}function sX({mirror:e,stylesheetManager:t},n){let r=null;r="#document"===n.nodeName?e.getId(n):e.getId(n.host);let i="#document"===n.nodeName?sW([n,"access",e=>e.defaultView,"optionalAccess",e=>e.Document]):sW([n,"access",e=>e.ownerDocument,"optionalAccess",e=>e.defaultView,"optionalAccess",e=>e.ShadowRoot]),s=sW([i,"optionalAccess",e=>e.prototype])?Object.getOwnPropertyDescriptor(sW([i,"optionalAccess",e=>e.prototype]),"adoptedStyleSheets"):void 0;return null!==r&&-1!==r&&i&&s?(Object.defineProperty(n,"adoptedStyleSheets",{configurable:s.configurable,enumerable:s.enumerable,get(){return sW([s,"access",e=>e.get,"optionalAccess",e=>e.call,"call",e=>e(this)])},set(e){let n=sW([s,"access",e=>e.set,"optionalAccess",e=>e.call,"call",t=>t(this,e)]);if(null!==r&&-1!==r)try{t.adoptStyleSheets(e,r)}catch(e){}return n}}),sB(()=>{Object.defineProperty(n,"adoptedStyleSheets",{configurable:s.configurable,enumerable:s.enumerable,get:s.get,set:s.set})})):()=>{}}function sQ(e,t={}){let n,r=e.doc.defaultView;if(!r)return()=>{};e.recordDOM&&(n=sq(e,e.doc));let i=function({mousemoveCb:e,sampling:t,doc:n,mirror:r}){let i;if(!1===t.mousemove)return()=>{};let s="number"==typeof t.mousemove?t.mousemove:50,a="number"==typeof t.mousemoveCallback?t.mousemoveCallback:500,o=[],l=sd(sB(t=>{let n=Date.now()-i;e(o.map(e=>(e.timeOffset-=n,e)),t),o=[],i=null}),a),c=sB(sd(sB(e=>{let t=sH(e),{clientX:n,clientY:s}=sS(e)?e.changedTouches[0]:e;i||(i=sp()),o.push({x:n,y:s,id:r.getId(t),timeOffset:sp()-i}),l("undefined"!=typeof DragEvent&&e instanceof DragEvent?sR.Drag:e instanceof MouseEvent?sR.MouseMove:sR.TouchMove)}),s,{trailing:!1})),u=[sl("mousemove",c,n),sl("touchmove",c,n),sl("drag",c,n)];return sB(()=>{u.forEach(e=>e())})}(e),s=function({mouseInteractionCb:e,doc:t,mirror:n,blockClass:r,blockSelector:i,unblockSelector:s,sampling:a}){if(!1===a.mouseInteraction)return()=>{};let o=!0===a.mouseInteraction||void 0===a.mouseInteraction?{}:a.mouseInteraction,l=[],c=null;return Object.keys(sO).filter(e=>Number.isNaN(Number(e))&&!e.endsWith("_Departed")&&!1!==o[e]).forEach(a=>{let o=iU(a),u=t=>{let o=sH(t);if(s_(o,r,i,s,!0))return;let l=null,u=a;if("pointerType"in t){switch(t.pointerType){case"mouse":l=sD.Mouse;break;case"touch":l=sD.Touch;break;case"pen":l=sD.Pen}l===sD.Touch?sO[a]===sO.MouseDown?u="TouchStart":sO[a]===sO.MouseUp&&(u="TouchEnd"):sD.Pen}else sS(t)&&(l=sD.Touch);null!==l?(c=l,(u.startsWith("Touch")&&l===sD.Touch||u.startsWith("Mouse")&&l===sD.Mouse)&&(l=null)):sO[a]===sO.Click&&(l=c,c=null);let d=sS(t)?t.changedTouches[0]:t;if(!d)return;let h=n.getId(o),{clientX:p,clientY:f}=d;sB(e)({type:sO[u],id:h,x:p,y:f,...null!==l&&{pointerType:l}})};if(window.PointerEvent)switch(sO[a]){case sO.MouseDown:case sO.MouseUp:o=o.replace("mouse","pointer");break;case sO.TouchStart:case sO.TouchEnd:return}l.push(sl(o,u,t))}),sB(()=>{l.forEach(e=>e())})}(e),a=sV(e),o=function({viewportResizeCb:e},{win:t}){let n=-1,r=-1;return sl("resize",sB(sd(sB(()=>{let t=sm(),i=sg();(n!==t||r!==i)&&(e({width:Number(i),height:Number(t)}),n=t,r=i)}),200)),t)}(e,{win:r}),l=function({inputCb:e,doc:t,mirror:n,blockClass:r,blockSelector:i,unblockSelector:s,ignoreClass:a,ignoreSelector:o,maskInputOptions:l,maskInputFn:c,sampling:u,userTriggeredOnInput:d,maskTextClass:h,unmaskTextClass:p,maskTextSelector:f,unmaskTextSelector:m}){function g(e){let n=sH(e),u=e.isTrusted,g=n&&iB(n.tagName);if("OPTION"===g&&(n=n.parentElement),!n||!g||0>sY.indexOf(g)||s_(n,r,i,s,!0))return;let _=n;if(_.classList.contains(a)||o&&_.matches(o))return;let v=iz(n),S=iH(_,g,v),b=!1,E=ij({maskInputOptions:l,tagName:g,type:v}),w=si(n,h,f,p,m,E);("radio"===v||"checkbox"===v)&&(b=n.checked),S=iF({isMasked:w,element:n,value:S,maskInputFn:c}),y(n,d?{text:S,isChecked:b,userTriggered:u}:{text:S,isChecked:b});let k=n.name;"radio"===v&&k&&b&&t.querySelectorAll(`input[type="radio"][name="${k}"]`).forEach(e=>{if(e!==n){let t=iF({isMasked:w,element:e,value:iH(e,g,v),maskInputFn:c});y(e,d?{text:t,isChecked:!b,userTriggered:!1}:{text:t,isChecked:!b})}})}function y(t,r){let i=sJ.get(t);if(!i||i.text!==r.text||i.isChecked!==r.isChecked){sJ.set(t,r);let i=n.getId(t);sB(e)({...r,id:i})}}let _=("last"===u.input?["change"]:["input","change"]).map(e=>sl(e,sB(g),t)),v=t.defaultView;if(!v)return()=>{_.forEach(e=>e())};let S=v.Object.getOwnPropertyDescriptor(v.HTMLInputElement.prototype,"value"),b=[[v.HTMLInputElement.prototype,"value"],[v.HTMLInputElement.prototype,"checked"],[v.HTMLSelectElement.prototype,"value"],[v.HTMLTextAreaElement.prototype,"value"],[v.HTMLSelectElement.prototype,"selectedIndex"],[v.HTMLOptionElement.prototype,"selected"]];return S&&S.set&&_.push(...b.map(e=>(function e(t,n,r,i,s=window){let a=s.Object.getOwnPropertyDescriptor(t,n);return s.Object.defineProperty(t,n,i?r:{set(e){sM(()=>{r.set.call(this,e)},0),a&&a.set&&a.set.call(this,e)}}),()=>e(t,n,a||{},!0)})(e[0],e[1],{set(){sB(g)({target:this,isTrusted:!1})}},!1,v))),sB(()=>{_.forEach(e=>e())})}(e),c=function({mediaInteractionCb:e,blockClass:t,blockSelector:n,unblockSelector:r,mirror:i,sampling:s,doc:a}){let o=sB(a=>sd(sB(s=>{let o=sH(s);if(!o||s_(o,t,n,r,!0))return;let{currentTime:l,volume:c,muted:u,playbackRate:d}=o;e({type:a,id:i.getId(o),currentTime:l,volume:c,muted:u,playbackRate:d})}),s.media||500)),l=[sl("play",o(0),a),sl("pause",o(1),a),sl("seeked",o(2),a),sl("volumechange",o(3),a),sl("ratechange",o(4),a)];return sB(()=>{l.forEach(e=>e())})}(e),u=()=>{},d=()=>{},h=()=>{},p=()=>{};e.recordDOM&&(u=function({styleSheetRuleCb:e,mirror:t,stylesheetManager:n},{win:r}){let i,s;if(!r.CSSStyleSheet||!r.CSSStyleSheet.prototype)return()=>{};let a=r.CSSStyleSheet.prototype.insertRule;r.CSSStyleSheet.prototype.insertRule=new Proxy(a,{apply:sB((r,i,s)=>{let[a,o]=s,{id:l,styleId:c}=sK(i,t,n.styleMirror);return(l&&-1!==l||c&&-1!==c)&&e({id:l,styleId:c,adds:[{rule:a,index:o}]}),r.apply(i,s)})});let o=r.CSSStyleSheet.prototype.deleteRule;r.CSSStyleSheet.prototype.deleteRule=new Proxy(o,{apply:sB((r,i,s)=>{let[a]=s,{id:o,styleId:l}=sK(i,t,n.styleMirror);return(o&&-1!==o||l&&-1!==l)&&e({id:o,styleId:l,removes:[{index:a}]}),r.apply(i,s)})}),r.CSSStyleSheet.prototype.replace&&(i=r.CSSStyleSheet.prototype.replace,r.CSSStyleSheet.prototype.replace=new Proxy(i,{apply:sB((r,i,s)=>{let[a]=s,{id:o,styleId:l}=sK(i,t,n.styleMirror);return(o&&-1!==o||l&&-1!==l)&&e({id:o,styleId:l,replace:a}),r.apply(i,s)})})),r.CSSStyleSheet.prototype.replaceSync&&(s=r.CSSStyleSheet.prototype.replaceSync,r.CSSStyleSheet.prototype.replaceSync=new Proxy(s,{apply:sB((r,i,s)=>{let[a]=s,{id:o,styleId:l}=sK(i,t,n.styleMirror);return(o&&-1!==o||l&&-1!==l)&&e({id:o,styleId:l,replaceSync:a}),r.apply(i,s)})}));let l={};s0("CSSGroupingRule")?l.CSSGroupingRule=r.CSSGroupingRule:(s0("CSSMediaRule")&&(l.CSSMediaRule=r.CSSMediaRule),s0("CSSConditionRule")&&(l.CSSConditionRule=r.CSSConditionRule),s0("CSSSupportsRule")&&(l.CSSSupportsRule=r.CSSSupportsRule));let c={};return Object.entries(l).forEach(([r,i])=>{c[r]={insertRule:i.prototype.insertRule,deleteRule:i.prototype.deleteRule},i.prototype.insertRule=new Proxy(c[r].insertRule,{apply:sB((r,i,s)=>{let[a,o]=s,{id:l,styleId:c}=sK(i.parentStyleSheet,t,n.styleMirror);return(l&&-1!==l||c&&-1!==c)&&e({id:l,styleId:c,adds:[{rule:a,index:[...sG(i),o||0]}]}),r.apply(i,s)})}),i.prototype.deleteRule=new Proxy(c[r].deleteRule,{apply:sB((r,i,s)=>{let[a]=s,{id:o,styleId:l}=sK(i.parentStyleSheet,t,n.styleMirror);return(o&&-1!==o||l&&-1!==l)&&e({id:o,styleId:l,removes:[{index:[...sG(i),a]}]}),r.apply(i,s)})})}),sB(()=>{r.CSSStyleSheet.prototype.insertRule=a,r.CSSStyleSheet.prototype.deleteRule=o,i&&(r.CSSStyleSheet.prototype.replace=i),s&&(r.CSSStyleSheet.prototype.replaceSync=s),Object.entries(l).forEach(([e,t])=>{t.prototype.insertRule=c[e].insertRule,t.prototype.deleteRule=c[e].deleteRule})})}(e,{win:r}),d=sX(e,e.doc),h=function({styleDeclarationCb:e,mirror:t,ignoreCSSAttributes:n,stylesheetManager:r},{win:i}){let s=i.CSSStyleDeclaration.prototype.setProperty;i.CSSStyleDeclaration.prototype.setProperty=new Proxy(s,{apply:sB((i,a,o)=>{let[l,c,u]=o;if(n.has(l))return s.apply(a,[l,c,u]);let{id:d,styleId:h}=sK(sW([a,"access",e=>e.parentRule,"optionalAccess",e=>e.parentStyleSheet]),t,r.styleMirror);return(d&&-1!==d||h&&-1!==h)&&e({id:d,styleId:h,set:{property:l,value:c,priority:u},index:sG(a.parentRule)}),i.apply(a,o)})});let a=i.CSSStyleDeclaration.prototype.removeProperty;return i.CSSStyleDeclaration.prototype.removeProperty=new Proxy(a,{apply:sB((i,s,o)=>{let[l]=o;if(n.has(l))return a.apply(s,[l]);let{id:c,styleId:u}=sK(sW([s,"access",e=>e.parentRule,"optionalAccess",e=>e.parentStyleSheet]),t,r.styleMirror);return(c&&-1!==c||u&&-1!==u)&&e({id:c,styleId:u,remove:{property:l},index:sG(s.parentRule)}),i.apply(s,o)})}),sB(()=>{i.CSSStyleDeclaration.prototype.setProperty=s,i.CSSStyleDeclaration.prototype.removeProperty=a})}(e,{win:r}),e.collectFonts&&(p=function({fontCb:e,doc:t}){let n=t.defaultView;if(!n)return()=>{};let r=[],i=new WeakMap,s=n.FontFace;n.FontFace=function(e,t,n){let r=new s(e,t,n);return i.set(r,{family:e,buffer:"string"!=typeof t,descriptors:n,fontSource:"string"==typeof t?t:JSON.stringify(Array.from(new Uint8Array(t)))}),r};let a=sh(t.fonts,"add",function(t){return function(n){return sM(sB(()=>{let t=i.get(n);t&&(e(t),i.delete(n))}),0),t.apply(this,[n])}});return r.push(()=>{n.FontFace=s}),r.push(a),sB(()=>{r.forEach(e=>e())})}(e)));let f=function(e){let{doc:t,mirror:n,blockClass:r,blockSelector:i,unblockSelector:s,selectionCb:a}=e,o=!0,l=sB(()=>{let e=t.getSelection();if(!e||o&&sW([e,"optionalAccess",e=>e.isCollapsed]))return;o=e.isCollapsed||!1;let l=[],c=e.rangeCount||0;for(let t=0;t<c;t++){let{startContainer:a,startOffset:o,endContainer:c,endOffset:u}=e.getRangeAt(t);s_(a,r,i,s,!0)||s_(c,r,i,s,!0)||l.push({start:n.getId(a),startOffset:o,end:n.getId(c),endOffset:u})}a({ranges:l})});return l(),sl("selectionchange",l)}(e),m=function({doc:e,customElementCb:t}){let n=e.defaultView;return n&&n.customElements?sh(n.customElements,"define",function(e){return function(n,r,i){try{t({define:{name:n}})}catch(e){}return e.apply(this,[n,r,i])}}):()=>{}}(e),g=[];for(let t of e.plugins)g.push(t.observer(t.callback,r,t.options));return sB(()=>{sz.forEach(e=>e.reset()),sW([n,"optionalAccess",e=>e.disconnect,"call",e=>e()]),i(),s(),a(),o(),l(),c(),u(),d(),h(),p(),f(),m(),g.forEach(e=>e())})}function sZ(e){return void 0!==window[e]}function s0(e){return!!(void 0!==window[e]&&window[e].prototype&&"insertRule"in window[e].prototype&&"deleteRule"in window[e].prototype)}class s1{constructor(e){this.generateIdFn=e,this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap}getId(e,t,n,r){let i=n||this.getIdToRemoteIdMap(e),s=r||this.getRemoteIdToIdMap(e),a=i.get(t);return a||(a=this.generateIdFn(),i.set(t,a),s.set(a,t)),a}getIds(e,t){let n=this.getIdToRemoteIdMap(e),r=this.getRemoteIdToIdMap(e);return t.map(t=>this.getId(e,t,n,r))}getRemoteId(e,t,n){let r=n||this.getRemoteIdToIdMap(e);if("number"!=typeof t)return t;let i=r.get(t);return i||-1}getRemoteIds(e,t){let n=this.getRemoteIdToIdMap(e);return t.map(t=>this.getRemoteId(e,t,n))}reset(e){if(!e){this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap;return}this.iframeIdToRemoteIdMap.delete(e),this.iframeRemoteIdToIdMap.delete(e)}getIdToRemoteIdMap(e){let t=this.iframeIdToRemoteIdMap.get(e);return t||(t=new Map,this.iframeIdToRemoteIdMap.set(e,t)),t}getRemoteIdToIdMap(e){let t=this.iframeRemoteIdToIdMap.get(e);return t||(t=new Map,this.iframeRemoteIdToIdMap.set(e,t)),t}}function s2(e){let t,n=e[0],r=1;for(;r<e.length;){let i=e[r],s=e[r+1];if(r+=2,("optionalAccess"===i||"optionalCall"===i)&&null==n)return;"access"===i||"optionalAccess"===i?(t=n,n=s(n)):("call"===i||"optionalCall"===i)&&(n=s((...e)=>n.call(t,...e)),t=void 0)}return n}class s3{constructor(){this.crossOriginIframeMirror=new s1(iZ),this.crossOriginIframeRootIdMap=new WeakMap}addIframe(){}addLoadListener(){}attachIframe(){}}class s5{constructor(e){this.iframes=new WeakMap,this.crossOriginIframeMap=new WeakMap,this.crossOriginIframeMirror=new s1(iZ),this.crossOriginIframeRootIdMap=new WeakMap,this.mutationCb=e.mutationCb,this.wrappedEmit=e.wrappedEmit,this.stylesheetManager=e.stylesheetManager,this.recordCrossOriginIframes=e.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new s1(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=e.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}addIframe(e){this.iframes.set(e,!0),e.contentWindow&&this.crossOriginIframeMap.set(e.contentWindow,e)}addLoadListener(e){this.loadListener=e}attachIframe(e,t){this.mutationCb({adds:[{parentId:this.mirror.getId(e),nextId:null,node:t}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),s2([this,"access",e=>e.loadListener,"optionalCall",t=>t(e)]);let n=sN(e);n&&n.adoptedStyleSheets&&n.adoptedStyleSheets.length>0&&this.stylesheetManager.adoptStyleSheets(n.adoptedStyleSheets,this.mirror.getId(n))}handleMessage(e){if("rrweb"!==e.data.type||e.origin!==e.data.origin||!e.source)return;let t=this.crossOriginIframeMap.get(e.source);if(!t)return;let n=this.transformCrossOriginEvent(t,e.data.event);n&&this.wrappedEmit(n,e.data.isCheckout)}transformCrossOriginEvent(e,t){switch(t.type){case sA.FullSnapshot:{this.crossOriginIframeMirror.reset(e),this.crossOriginIframeStyleMirror.reset(e),this.replaceIdOnNode(t.data.node,e);let n=t.data.node.id;return this.crossOriginIframeRootIdMap.set(e,n),this.patchRootIdOnNode(t.data.node,n),{timestamp:t.timestamp,type:sA.IncrementalSnapshot,data:{source:sR.Mutation,adds:[{parentId:this.mirror.getId(e),nextId:null,node:t.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}}}case sA.Meta:case sA.Load:case sA.DomContentLoaded:break;case sA.Plugin:return t;case sA.Custom:return this.replaceIds(t.data.payload,e,["id","parentId","previousId","nextId"]),t;case sA.IncrementalSnapshot:switch(t.data.source){case sR.Mutation:return t.data.adds.forEach(t=>{this.replaceIds(t,e,["parentId","nextId","previousId"]),this.replaceIdOnNode(t.node,e);let n=this.crossOriginIframeRootIdMap.get(e);n&&this.patchRootIdOnNode(t.node,n)}),t.data.removes.forEach(t=>{this.replaceIds(t,e,["parentId","id"])}),t.data.attributes.forEach(t=>{this.replaceIds(t,e,["id"])}),t.data.texts.forEach(t=>{this.replaceIds(t,e,["id"])}),t;case sR.Drag:case sR.TouchMove:case sR.MouseMove:return t.data.positions.forEach(t=>{this.replaceIds(t,e,["id"])}),t;case sR.ViewportResize:return!1;case sR.MediaInteraction:case sR.MouseInteraction:case sR.Scroll:case sR.CanvasMutation:case sR.Input:return this.replaceIds(t.data,e,["id"]),t;case sR.StyleSheetRule:case sR.StyleDeclaration:return this.replaceIds(t.data,e,["id"]),this.replaceStyleIds(t.data,e,["styleId"]),t;case sR.Font:return t;case sR.Selection:return t.data.ranges.forEach(t=>{this.replaceIds(t,e,["start","end"])}),t;case sR.AdoptedStyleSheet:return this.replaceIds(t.data,e,["id"]),this.replaceStyleIds(t.data,e,["styleIds"]),s2([t,"access",e=>e.data,"access",e=>e.styles,"optionalAccess",e=>e.forEach,"call",t=>t(t=>{this.replaceStyleIds(t,e,["styleId"])})]),t}}return!1}replace(e,t,n,r){for(let i of r)(Array.isArray(t[i])||"number"==typeof t[i])&&(Array.isArray(t[i])?t[i]=e.getIds(n,t[i]):t[i]=e.getId(n,t[i]));return t}replaceIds(e,t,n){return this.replace(this.crossOriginIframeMirror,e,t,n)}replaceStyleIds(e,t,n){return this.replace(this.crossOriginIframeStyleMirror,e,t,n)}replaceIdOnNode(e,t){this.replaceIds(e,t,["id","rootId"]),"childNodes"in e&&e.childNodes.forEach(e=>{this.replaceIdOnNode(e,t)})}patchRootIdOnNode(e,t){e.type===nZ.Document||e.rootId||(e.rootId=t),"childNodes"in e&&e.childNodes.forEach(e=>{this.patchRootIdOnNode(e,t)})}}class s4{init(){}addShadowRoot(){}observeAttachShadow(){}reset(){}}class s8{constructor(e){this.shadowDoms=new WeakSet,this.restoreHandlers=[],this.mutationCb=e.mutationCb,this.scrollCb=e.scrollCb,this.bypassOptions=e.bypassOptions,this.mirror=e.mirror,this.init()}init(){this.reset(),this.patchAttachShadow(Element,document)}addShadowRoot(e,t){if(!iN(e)||this.shadowDoms.has(e))return;this.shadowDoms.add(e),this.bypassOptions.canvasManager.addShadowRoot(e);let n=sq({...this.bypassOptions,doc:t,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this},e);this.restoreHandlers.push(()=>n.disconnect()),this.restoreHandlers.push(sV({...this.bypassOptions,scrollCb:this.scrollCb,doc:e,mirror:this.mirror})),sM(()=>{e.adoptedStyleSheets&&e.adoptedStyleSheets.length>0&&this.bypassOptions.stylesheetManager.adoptStyleSheets(e.adoptedStyleSheets,this.mirror.getId(e.host)),this.restoreHandlers.push(sX({mirror:this.mirror,stylesheetManager:this.bypassOptions.stylesheetManager},e))},0)}observeAttachShadow(e){let t=sN(e),n=function(e){try{return e.contentWindow}catch(e){}}(e);t&&n&&this.patchAttachShadow(n.Element,t)}patchAttachShadow(e,t){let n=this;this.restoreHandlers.push(sh(e.prototype,"attachShadow",function(e){return function(r){let i=e.call(this,r);return this.shadowRoot&&sx(this)&&n.addShadowRoot(this.shadowRoot,t),i}}))}reset(){this.restoreHandlers.forEach(e=>{try{e()}catch(e){}}),this.restoreHandlers=[],this.shadowDoms=new WeakSet,this.bypassOptions.canvasManager.resetShadowRoots()}}class s6{reset(){}freeze(){}unfreeze(){}lock(){}unlock(){}snapshot(){}addWindow(){}addShadowRoot(){}resetShadowRoots(){}}class s7{constructor(e){this.trackedLinkElements=new WeakSet,this.styleMirror=new sk,this.mutationCb=e.mutationCb,this.adoptedStyleSheetCb=e.adoptedStyleSheetCb}attachLinkElement(e,t){"_cssText"in t.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:t.id,attributes:t.attributes}]}),this.trackLinkElement(e)}trackLinkElement(e){this.trackedLinkElements.has(e)||(this.trackedLinkElements.add(e),this.trackStylesheetInLinkElement(e))}adoptStyleSheets(e,t){if(0===e.length)return;let n={id:t,styleIds:[]},r=[];for(let t of e){let e;this.styleMirror.has(t)?e=this.styleMirror.getId(t):(e=this.styleMirror.add(t),r.push({styleId:e,rules:Array.from(t.rules||CSSRule,(e,t)=>({rule:iP(e),index:t}))})),n.styleIds.push(e)}r.length>0&&(n.styles=r),this.adoptedStyleSheetCb(n)}reset(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}trackStylesheetInLinkElement(e){}}class s9{constructor(){this.nodeMap=new WeakMap,this.active=!1}inOtherBuffer(e,t){let n=this.nodeMap.get(e);return n&&Array.from(n).some(e=>e!==t)}add(e,t){this.active||(this.active=!0,function(...e){sC("requestAnimationFrame")(...e)}(()=>{this.nodeMap=new WeakMap,this.active=!1})),this.nodeMap.set(e,(this.nodeMap.get(e)||new Set).add(t))}destroy(){}}try{if(2!==Array.from([1],e=>2*e)[0]){let e=document.createElement("iframe");document.body.appendChild(e),Array.from=T([e,"access",e=>e.contentWindow,"optionalAccess",e=>e.Array,"access",e=>e.from])||Array.from,document.body.removeChild(e)}}catch(e){console.debug("Unable to override Array.from",e)}let ae=new i$;function at(e={}){let t,{emit:n,checkoutEveryNms:r,checkoutEveryNth:i,blockClass:s="rr-block",blockSelector:a=null,unblockSelector:o=null,ignoreClass:l="rr-ignore",ignoreSelector:c=null,maskAllText:u=!1,maskTextClass:d="rr-mask",unmaskTextClass:h=null,maskTextSelector:p=null,unmaskTextSelector:f=null,inlineStylesheet:m=!0,maskAllInputs:g,maskInputOptions:y,slimDOMOptions:_,maskAttributeFn:v,maskInputFn:S,maskTextFn:k,maxCanvasSize:x=null,packFn:I,sampling:C={},dataURLOptions:M={},mousemoveWait:A,recordDOM:R=!0,recordCanvas:O=!1,recordCrossOriginIframes:D=!1,recordAfter:N="DOMContentLoaded"===e.recordAfter?e.recordAfter:"load",userTriggeredOnInput:L=!1,collectFonts:P=!1,inlineImages:$=!1,plugins:j,keepIframeSrcFn:F=()=>!1,ignoreCSSAttributes:U=new Set([]),errorHandler:B,onMutation:W,getCanvasManager:z}=e;b=B;let H=!D||window.parent===window,q=!1;if(!H)try{window.parent.document&&(q=!1)}catch(e){q=!0}if(H&&!n)throw Error("emit function is required");if(!H&&!q)return()=>{};void 0!==A&&void 0===C.mousemove&&(C.mousemove=A),ae.reset();let V=!0===g?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,radio:!0,checkbox:!0}:void 0!==y?y:{},Y=!0===_||"all"===_?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:"all"===_,headMetaDescKeywords:"all"===_}:_||{};!function(e=window){"NodeList"in e&&!e.NodeList.prototype.forEach&&(e.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in e&&!e.DOMTokenList.prototype.forEach&&(e.DOMTokenList.prototype.forEach=Array.prototype.forEach),Node.prototype.contains||(Node.prototype.contains=(...e)=>{let t=e[0];if(!(0 in e))throw TypeError("1 argument is required");do if(this===t)return!0;while(t=t&&t.parentNode);return!1})}();let J=0,G=e=>{for(let t of j||[])t.eventProcessor&&(e=t.eventProcessor(e));return I&&!q&&(e=I(e)),e};E=(e,s)=>{if(e.timestamp=sp(),T([sz,"access",e=>e[0],"optionalAccess",e=>e.isFrozen,"call",e=>e()])&&e.type!==sA.FullSnapshot&&(e.type!==sA.IncrementalSnapshot||e.data.source!==sR.Mutation)&&sz.forEach(e=>e.unfreeze()),H)T([n,"optionalCall",t=>t(G(e),s)]);else if(q){let t={type:"rrweb",event:G(e),origin:window.location.origin,isCheckout:s};window.parent.postMessage(t,"*")}if(e.type===sA.FullSnapshot)t=e,J=0;else if(e.type===sA.IncrementalSnapshot){if(e.data.source===sR.Mutation&&e.data.isAttachIframe)return;J++;let n=i&&J>=i,s=r&&t&&e.timestamp-t.timestamp>r;(n||s)&&ei(!0)}};let K=e=>{E({type:sA.IncrementalSnapshot,data:{source:sR.Mutation,...e}})},X=e=>E({type:sA.IncrementalSnapshot,data:{source:sR.Scroll,...e}}),Q=e=>E({type:sA.IncrementalSnapshot,data:{source:sR.CanvasMutation,...e}}),Z=new s7({mutationCb:K,adoptedStyleSheetCb:e=>E({type:sA.IncrementalSnapshot,data:{source:sR.AdoptedStyleSheet,...e}})}),ee="boolean"==typeof __RRWEB_EXCLUDE_IFRAME__&&__RRWEB_EXCLUDE_IFRAME__?new s3:new s5({mirror:ae,mutationCb:K,stylesheetManager:Z,recordCrossOriginIframes:D,wrappedEmit:E});for(let e of j||[])e.getMirror&&e.getMirror({nodeMirror:ae,crossOriginIframeMirror:ee.crossOriginIframeMirror,crossOriginIframeStyleMirror:ee.crossOriginIframeStyleMirror});let et=new s9,en=function(e,t){try{return e?e(t):new s6}catch(e){return console.warn("Unable to initialize CanvasManager"),new s6}}(z,{mirror:ae,win:window,mutationCb:e=>E({type:sA.IncrementalSnapshot,data:{source:sR.CanvasMutation,...e}}),recordCanvas:O,blockClass:s,blockSelector:a,unblockSelector:o,maxCanvasSize:x,sampling:C.canvas,dataURLOptions:M,errorHandler:B}),er="boolean"==typeof __RRWEB_EXCLUDE_SHADOW_DOM__&&__RRWEB_EXCLUDE_SHADOW_DOM__?new s4:new s8({mutationCb:K,scrollCb:X,bypassOptions:{onMutation:W,blockClass:s,blockSelector:a,unblockSelector:o,maskAllText:u,maskTextClass:d,unmaskTextClass:h,maskTextSelector:p,unmaskTextSelector:f,inlineStylesheet:m,maskInputOptions:V,dataURLOptions:M,maskAttributeFn:v,maskTextFn:k,maskInputFn:S,recordCanvas:O,inlineImages:$,sampling:C,slimDOMOptions:Y,iframeManager:ee,stylesheetManager:Z,canvasManager:en,keepIframeSrcFn:F,processedNodeManager:et},mirror:ae}),ei=(e=!1)=>{if(!R)return;E({type:sA.Meta,data:{href:window.location.href,width:sg(),height:sm()}},e),Z.reset(),er.init(),sz.forEach(e=>e.lock());let t=function(e,t){let{mirror:n=new i$,blockClass:r="rr-block",blockSelector:i=null,unblockSelector:s=null,maskAllText:a=!1,maskTextClass:o="rr-mask",unmaskTextClass:l=null,maskTextSelector:c=null,unmaskTextSelector:u=null,inlineStylesheet:d=!0,inlineImages:h=!1,recordCanvas:p=!1,maskAllInputs:f=!1,maskAttributeFn:m,maskTextFn:g,maskInputFn:y,slimDOM:_=!1,dataURLOptions:v,preserveWhiteSpace:S,onSerialize:b,onIframeLoad:E,iframeLoadTimeout:w,onStylesheetLoad:k,stylesheetLoadTimeout:T,keepIframeSrcFn:x=()=>!1}=t||{};return sa(e,{doc:e,mirror:n,blockClass:r,blockSelector:i,unblockSelector:s,maskAllText:a,maskTextClass:o,unmaskTextClass:l,maskTextSelector:c,unmaskTextSelector:u,skipChild:!1,inlineStylesheet:d,maskInputOptions:!0===f?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0}:!1===f?{}:f,maskAttributeFn:m,maskTextFn:g,maskInputFn:y,slimDOMOptions:!0===_||"all"===_?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:"all"===_,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:!1===_?{}:_,dataURLOptions:v,inlineImages:h,recordCanvas:p,preserveWhiteSpace:S,onSerialize:b,onIframeLoad:E,iframeLoadTimeout:w,onStylesheetLoad:k,stylesheetLoadTimeout:T,keepIframeSrcFn:x,newlyAddedElement:!1})}(document,{mirror:ae,blockClass:s,blockSelector:a,unblockSelector:o,maskAllText:u,maskTextClass:d,unmaskTextClass:h,maskTextSelector:p,unmaskTextSelector:f,inlineStylesheet:m,maskAllInputs:V,maskAttributeFn:v,maskInputFn:S,maskTextFn:k,slimDOM:Y,dataURLOptions:M,recordCanvas:O,inlineImages:$,onSerialize:e=>{sb(e,ae)&&ee.addIframe(e),sE(e,ae)&&Z.trackLinkElement(e),sw(e)&&er.addShadowRoot(e.shadowRoot,document)},onIframeLoad:(e,t)=>{ee.attachIframe(e,t),e.contentWindow&&en.addWindow(e.contentWindow),er.observeAttachShadow(e)},onStylesheetLoad:(e,t)=>{Z.attachLinkElement(e,t)},keepIframeSrcFn:F});if(!t)return console.warn("Failed to snapshot the document");E({type:sA.FullSnapshot,data:{node:t,initialOffset:sf(window)}}),sz.forEach(e=>e.unlock()),document.adoptedStyleSheets&&document.adoptedStyleSheets.length>0&&Z.adoptStyleSheets(document.adoptedStyleSheets,ae.getId(document))};w=ei;try{let e=[],t=e=>sB(sQ)({onMutation:W,mutationCb:K,mousemoveCb:(e,t)=>E({type:sA.IncrementalSnapshot,data:{source:t,positions:e}}),mouseInteractionCb:e=>E({type:sA.IncrementalSnapshot,data:{source:sR.MouseInteraction,...e}}),scrollCb:X,viewportResizeCb:e=>E({type:sA.IncrementalSnapshot,data:{source:sR.ViewportResize,...e}}),inputCb:e=>E({type:sA.IncrementalSnapshot,data:{source:sR.Input,...e}}),mediaInteractionCb:e=>E({type:sA.IncrementalSnapshot,data:{source:sR.MediaInteraction,...e}}),styleSheetRuleCb:e=>E({type:sA.IncrementalSnapshot,data:{source:sR.StyleSheetRule,...e}}),styleDeclarationCb:e=>E({type:sA.IncrementalSnapshot,data:{source:sR.StyleDeclaration,...e}}),canvasMutationCb:Q,fontCb:e=>E({type:sA.IncrementalSnapshot,data:{source:sR.Font,...e}}),selectionCb:e=>{E({type:sA.IncrementalSnapshot,data:{source:sR.Selection,...e}})},customElementCb:e=>{E({type:sA.IncrementalSnapshot,data:{source:sR.CustomElement,...e}})},blockClass:s,ignoreClass:l,ignoreSelector:c,maskAllText:u,maskTextClass:d,unmaskTextClass:h,maskTextSelector:p,unmaskTextSelector:f,maskInputOptions:V,inlineStylesheet:m,sampling:C,recordDOM:R,recordCanvas:O,inlineImages:$,userTriggeredOnInput:L,collectFonts:P,doc:e,maskAttributeFn:v,maskInputFn:S,maskTextFn:k,keepIframeSrcFn:F,blockSelector:a,unblockSelector:o,slimDOMOptions:Y,dataURLOptions:M,mirror:ae,iframeManager:ee,stylesheetManager:Z,shadowDomManager:er,processedNodeManager:et,canvasManager:en,ignoreCSSAttributes:U,plugins:T([j,"optionalAccess",e=>e.filter,"call",e=>e(e=>e.observer),"optionalAccess",e=>e.map,"call",e=>e(e=>({observer:e.observer,options:e.options,callback:t=>E({type:sA.Plugin,data:{plugin:e.name,payload:t}})}))])||[]},{});ee.addLoadListener(n=>{try{e.push(t(n.contentDocument))}catch(e){console.warn(e)}});let n=()=>{ei(),e.push(t(document))};return"interactive"===document.readyState||"complete"===document.readyState?n():(e.push(sl("DOMContentLoaded",()=>{E({type:sA.DomContentLoaded,data:{}}),"DOMContentLoaded"===N&&n()})),e.push(sl("load",()=>{E({type:sA.Load,data:{}}),"load"===N&&n()},window))),()=>{e.forEach(e=>e()),et.destroy(),w=void 0,b=void 0}}catch(e){console.warn(e)}}function an(e){return e>0x2540be3ff?e:1e3*e}function ar(e){return e>0x2540be3ff?e/1e3:e}function ai(e,t){"sentry.transaction"!==t.category&&(["ui.click","ui.input"].includes(t.category)?e.triggerUserActivity():e.checkAndHandleExpiredSession(),e.addUpdate(()=>(e.throttledAddEvent({type:sA.Custom,timestamp:1e3*(t.timestamp||0),data:{tag:"breadcrumb",payload:eq(t,10,1e3)}}),"console"===t.category)))}function as(e){return e.closest("button,a")||e}function aa(e){let t=ao(e);return t&&t instanceof Element?as(t):t}function ao(e){var t;return"object"==typeof(t=e)&&t&&"target"in t?e.target:e}at.mirror=ae,at.takeFullSnapshot=function(e){if(!w)throw Error("please take full snapshot after start recording");w(e)};let al=new Set([sR.Mutation,sR.StyleSheetRule,sR.StyleDeclaration,sR.AdoptedStyleSheet,sR.CanvasMutation,sR.Selection,sR.MediaInteraction]);class ac{constructor(e,t,n=ai){this._lastMutation=0,this._lastScroll=0,this._clicks=[],this._timeout=t.timeout/1e3,this._threshold=t.threshold/1e3,this._scrollTimeout=t.scrollTimeout/1e3,this._replay=e,this._ignoreSelector=t.ignoreSelector,this._addBreadcrumbEvent=n}addListeners(){var e;let t=(e=()=>{this._lastMutation=ad()},k||(k=[],en(I,"open",function(e){return function(...t){if(k)try{k.forEach(e=>e())}catch(e){}return e.apply(I,t)}})),k.push(e),()=>{let t=k?k.indexOf(e):-1;t>-1&&k.splice(t,1)});this._teardown=()=>{t(),this._clicks=[],this._lastMutation=0,this._lastScroll=0}}removeListeners(){this._teardown&&this._teardown(),this._checkClickTimeout&&clearTimeout(this._checkClickTimeout)}handleClick(e,t){var n,r,i;if(n=t,r=this._ignoreSelector,!au.includes(n.tagName)||"INPUT"===n.tagName&&!["submit","button"].includes(n.getAttribute("type")||"")||"A"===n.tagName&&(n.hasAttribute("download")||n.hasAttribute("target")&&"_self"!==n.getAttribute("target"))||r&&n.matches(r)||!((i=e).data&&"number"==typeof i.data.nodeId&&i.timestamp))return;let s={timestamp:ar(e.timestamp),clickBreadcrumb:e,clickCount:0,node:t};this._clicks.some(e=>e.node===s.node&&1>Math.abs(e.timestamp-s.timestamp))||(this._clicks.push(s),1===this._clicks.length&&this._scheduleCheckClicks())}registerMutation(e=Date.now()){this._lastMutation=ar(e)}registerScroll(e=Date.now()){this._lastScroll=ar(e)}registerClick(e){let t=as(e);this._handleMultiClick(t)}_handleMultiClick(e){this._getClicks(e).forEach(e=>{e.clickCount++})}_getClicks(e){return this._clicks.filter(t=>t.node===e)}_checkClicks(){let e=[],t=ad();for(let n of(this._clicks.forEach(n=>{!n.mutationAfter&&this._lastMutation&&(n.mutationAfter=n.timestamp<=this._lastMutation?this._lastMutation-n.timestamp:void 0),!n.scrollAfter&&this._lastScroll&&(n.scrollAfter=n.timestamp<=this._lastScroll?this._lastScroll-n.timestamp:void 0),n.timestamp+this._timeout<=t&&e.push(n)}),e)){let e=this._clicks.indexOf(n);e>-1&&(this._generateBreadcrumbs(n),this._clicks.splice(e,1))}this._clicks.length&&this._scheduleCheckClicks()}_generateBreadcrumbs(e){let t=this._replay,n=e.scrollAfter&&e.scrollAfter<=this._scrollTimeout,r=e.mutationAfter&&e.mutationAfter<=this._threshold,{clickCount:i,clickBreadcrumb:s}=e;if(!n&&!r){let n=1e3*Math.min(e.mutationAfter||this._timeout,this._timeout),r=n<1e3*this._timeout?"mutation":"timeout",a={type:"default",message:s.message,timestamp:s.timestamp,category:"ui.slowClickDetected",data:{...s.data,url:I.location.href,route:t.getCurrentRoute(),timeAfterClickMs:n,endReason:r,clickCount:i||1}};this._addBreadcrumbEvent(t,a);return}if(i>1){let e={type:"default",message:s.message,timestamp:s.timestamp,category:"ui.multiClick",data:{...s.data,url:I.location.href,route:t.getCurrentRoute(),clickCount:i,metric:!0}};this._addBreadcrumbEvent(t,e)}}_scheduleCheckClicks(){this._checkClickTimeout&&clearTimeout(this._checkClickTimeout),this._checkClickTimeout=nV(()=>this._checkClicks(),1e3)}}let au=["A","BUTTON","INPUT"];function ad(){return Date.now()/1e3}function ah(e){return{timestamp:Date.now()/1e3,type:"default",...e}}!function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"}(n2||(n2={}));let ap=new Set(["id","class","aria-label","role","name","alt","title","data-test-id","data-testid","disabled","aria-disabled","data-sentry-component"]);function af(e,t){let n=at.mirror.getId(e),r=n&&at.mirror.getNode(n),i=r&&at.mirror.getMeta(r),s=i&&i.type===n2.Element?i:null;return{message:t,data:s?{nodeId:n,node:{id:n,tagName:s.tagName,textContent:Array.from(s.childNodes).map(e=>e.type===n2.Text&&e.textContent).filter(Boolean).map(e=>e.trim()).join(""),attributes:function(e){let t={};for(let n in!e["data-sentry-component"]&&e["data-sentry-element"]&&(e["data-sentry-component"]=e["data-sentry-element"]),e)if(ap.has(n)){let r=n;("data-testid"===n||"data-test-id"===n)&&(r="testId"),t[r]=e[n]}return t}(s.attributes)}}:{}}}let am={resource:function(e){let{entryType:t,initiatorType:n,name:r,responseEnd:i,startTime:s,decodedBodySize:a,encodedBodySize:o,responseStatus:l,transferSize:c}=e;return["fetch","xmlhttprequest"].includes(n)?null:{type:`${t}.${n}`,start:a_(s),end:a_(i),name:r,data:{size:c,statusCode:l,decodedBodySize:a,encodedBodySize:o}}},paint:function(e){let{duration:t,entryType:n,name:r,startTime:i}=e,s=a_(i);return{type:n,name:r,start:s,end:s+t,data:void 0}},navigation:function(e){let{entryType:t,name:n,decodedBodySize:r,duration:i,domComplete:s,encodedBodySize:a,domContentLoadedEventStart:o,domContentLoadedEventEnd:l,domInteractive:c,loadEventStart:u,loadEventEnd:d,redirectCount:h,startTime:p,transferSize:f,type:m}=e;return 0===i?null:{type:`${t}.${m}`,start:a_(p),end:a_(s),name:n,data:{size:f,decodedBodySize:r,encodedBodySize:a,duration:i,domInteractive:c,domContentLoadedEventStart:o,domContentLoadedEventEnd:l,loadEventStart:u,loadEventEnd:d,domComplete:s,redirectCount:h}}}};function ag(e,t){return({metric:n})=>void t.replayPerformanceEntries.push(e(n))}function ay(e){let t=am[e.entryType];return t?t(e):null}function a_(e){return((eh||I.performance.timeOrigin)+e)/1e3}function av(e){let t=e.entries[e.entries.length-1];return aw(e,"largest-contentful-paint",t&&t.element?[t.element]:void 0)}function aS(e){let t=[],n=[];for(let r of e.entries)if(void 0!==r.sources){let e=[];for(let t of r.sources)if(t.node){n.push(t.node);let r=at.mirror.getId(t.node);r&&e.push(r)}t.push({value:r.value,nodeIds:e.length?e:void 0})}return aw(e,"cumulative-layout-shift",n,t)}function ab(e){let t=e.entries[e.entries.length-1];return aw(e,"first-input-delay",t&&t.target?[t.target]:void 0)}function aE(e){let t=e.entries[e.entries.length-1];return aw(e,"interaction-to-next-paint",t&&t.target?[t.target]:void 0)}function aw(e,t,n,r){let i=e.value,s=e.rating,a=a_(i);return{type:"web-vital",name:t,start:a,end:a,data:{value:i,size:i,rating:s,nodeIds:n?n.map(e=>at.mirror.getId(e)):void 0,attributions:r}}}let ak=["info","warn","error","log"];(function(){let e={exception:()=>void 0,infoTick:()=>void 0,setConfig:e=>{e.captureExceptions,e.traceInternals}};ak.forEach(t=>{e[t]=()=>void 0})})();class aT extends Error{constructor(){super("Event buffer exceeded maximum size of 20000000.")}}class ax{constructor(){this.events=[],this._totalSize=0,this.hasCheckout=!1,this.waitForCheckout=!1}get hasEvents(){return this.events.length>0}get type(){return"sync"}destroy(){this.events=[]}async addEvent(e){let t=JSON.stringify(e).length;if(this._totalSize+=t,this._totalSize>2e7)throw new aT;this.events.push(e)}finish(){return new Promise(e=>{let t=this.events;this.clear(),e(JSON.stringify(t))})}clear(){this.events=[],this._totalSize=0,this.hasCheckout=!1}getEarliestTimestamp(){let e=this.events.map(e=>e.timestamp).sort()[0];return e?an(e):null}}class aI{constructor(e){this._worker=e,this._id=0}ensureReady(){return this._ensureReadyPromise||(this._ensureReadyPromise=new Promise((e,t)=>{this._worker.addEventListener("message",({data:n})=>{n.success?e():t()},{once:!0}),this._worker.addEventListener("error",e=>{t(e)},{once:!0})})),this._ensureReadyPromise}destroy(){this._worker.terminate()}postMessage(e,t){let n=this._getAndIncrementId();return new Promise((r,i)=>{let s=({data:t})=>{if(t.method===e&&t.id===n){if(this._worker.removeEventListener("message",s),!t.success)return void i(Error("Error in compression worker"));r(t.response)}};this._worker.addEventListener("message",s),this._worker.postMessage({id:n,method:e,arg:t})})}_getAndIncrementId(){return this._id++}}class aC{constructor(e){this._worker=new aI(e),this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1,this.waitForCheckout=!1}get hasEvents(){return!!this._earliestTimestamp}get type(){return"worker"}ensureReady(){return this._worker.ensureReady()}destroy(){this._worker.destroy()}addEvent(e){let t=an(e.timestamp);(!this._earliestTimestamp||t<this._earliestTimestamp)&&(this._earliestTimestamp=t);let n=JSON.stringify(e);return(this._totalSize+=n.length,this._totalSize>2e7)?Promise.reject(new aT):this._sendEventToWorker(n)}finish(){return this._finishRequest()}clear(){this._earliestTimestamp=null,this._totalSize=0,this.hasCheckout=!1,this._worker.postMessage("clear").then(null,e=>{})}getEarliestTimestamp(){return this._earliestTimestamp}_sendEventToWorker(e){return this._worker.postMessage("addEvent",e)}async _finishRequest(){let e=await this._worker.postMessage("finish");return this._earliestTimestamp=null,this._totalSize=0,e}}class aM{constructor(e){this._fallback=new ax,this._compression=new aC(e),this._used=this._fallback,this._ensureWorkerIsLoadedPromise=this._ensureWorkerIsLoaded()}get waitForCheckout(){return this._used.waitForCheckout}get type(){return this._used.type}get hasEvents(){return this._used.hasEvents}get hasCheckout(){return this._used.hasCheckout}set hasCheckout(e){this._used.hasCheckout=e}set waitForCheckout(e){this._used.waitForCheckout=e}destroy(){this._fallback.destroy(),this._compression.destroy()}clear(){return this._used.clear()}getEarliestTimestamp(){return this._used.getEarliestTimestamp()}addEvent(e){return this._used.addEvent(e)}async finish(){return await this.ensureWorkerIsLoaded(),this._used.finish()}ensureWorkerIsLoaded(){return this._ensureWorkerIsLoadedPromise}async _ensureWorkerIsLoaded(){try{await this._compression.ensureReady()}catch(e){return}await this._switchToCompressionWorker()}async _switchToCompressionWorker(){let{events:e,hasCheckout:t,waitForCheckout:n}=this._fallback,r=[];for(let t of e)r.push(this._compression.addEvent(t));this._compression.hasCheckout=t,this._compression.waitForCheckout=n,this._used=this._compression;try{await Promise.all(r),this._fallback.clear()}catch(e){}}}function aA(){try{return"sessionStorage"in I&&!!I.sessionStorage}catch(e){return!1}}function aR(e){return void 0!==e&&Math.random()<e}function aO(e){let t=Date.now(),n=e.id||ep(),r=e.started||t,i=e.lastActivity||t,s=e.segmentId||0;return{id:n,started:r,lastActivity:i,segmentId:s,sampled:e.sampled,previousSessionId:e.previousSessionId}}function aD(e){if(aA())try{I.sessionStorage.setItem(iM,JSON.stringify(e))}catch(e){}}function aN({sessionSampleRate:e,allowBuffering:t,stickySession:n=!1},{previousSessionId:r}={}){let i=aO({sampled:aR(e)?"session":!!t&&"buffer",previousSessionId:r});return n&&aD(i),i}function aL(e,t,n=+new Date){return null===e||void 0===t||t<0||0!==t&&e+t<=n}function aP(e,{maxReplayDuration:t,sessionIdleExpire:n,targetTime:r=Date.now()}){return aL(e.started,t,r)||aL(e.lastActivity,n,r)}function a$(e,{sessionIdleExpire:t,maxReplayDuration:n}){return!!aP(e,{sessionIdleExpire:t,maxReplayDuration:n})&&("buffer"!==e.sampled||0!==e.segmentId)}function aj({sessionIdleExpire:e,maxReplayDuration:t,previousSessionId:n},r){let i=r.stickySession&&function(){if(!aA())return null;try{let e=I.sessionStorage.getItem(iM);if(!e)return null;let t=JSON.parse(e);return aO(t)}catch(e){return null}}();return i?a$(i,{sessionIdleExpire:e,maxReplayDuration:t})?aN(r,{previousSessionId:i.id}):i:aN(r,{previousSessionId:n})}function aF(e,t,n){return!!aB(e,t)&&(aU(e,t,n),!0)}async function aU(e,t,n){let{eventBuffer:r}=e;if(!r||r.waitForCheckout&&!n)return null;let i="buffer"===e.recordingMode;try{n&&i&&r.clear(),n&&(r.hasCheckout=!0,r.waitForCheckout=!1);let s=e.getOptions(),a=function(e,t){try{if("function"==typeof t&&e.type===sA.Custom)return t(e)}catch(e){return null}return e}(t,s.beforeAddRecordingEvent);if(!a)return;return await r.addEvent(a)}catch(s){let t=s&&s instanceof aT;if(t&&i)return r.clear(),r.waitForCheckout=!0,null;e.handleException(s),await e.stop({reason:t?"addEventSizeExceeded":"addEvent"});let n=eN();n&&n.recordDroppedEvent("internal_sdk_error","replay")}}function aB(e,t){if(!e.eventBuffer||e.isPaused()||!e.isEnabled())return!1;let n=an(t.timestamp);return!(n+e.timeouts.sessionIdlePause<Date.now())&&!(n>e.getContext().initialTimestamp+e.getOptions().maxReplayDuration)}function aW(e){return"transaction"===e.type}function az(e){return"feedback"===e.type}function aH(e){return!!e.category}function aq(){let e=eR().getPropagationContext().dsc;e&&delete e.replay_id;let t=td();if(t){let e=tg(t);delete e.replay_id}}function aV(e,t){return t.map(({type:t,start:n,end:r,name:i,data:s})=>{let a=e.throttledAddEvent({type:sA.Custom,timestamp:n,data:{tag:"performanceSpan",payload:{op:t,description:i,startTimestamp:n,endTimestamp:r,data:s}}});return"string"==typeof a?Promise.resolve(null):a})}function aY(e,t){e.isEnabled()&&null!==t&&!function(e,t){var n,r,i,s,a=eN();let o=a&&a.getDsn(),l=a&&a.getOptions().tunnel;return n=t,!!(r=o)&&n.includes(r.host)||(i=t,!!(s=l)&&ix(i)===ix(s))}(0,t.name)&&e.addUpdate(()=>(aV(e,[t]),!0))}function aJ(e){if(!e)return;let t=new TextEncoder;try{if("string"==typeof e)return t.encode(e).length;if(e instanceof URLSearchParams)return t.encode(e.toString()).length;if(e instanceof FormData){let n=a2(e);return t.encode(n).length}if(e instanceof Blob)return e.size;if(e instanceof ArrayBuffer)return e.byteLength}catch(e){}}function aG(e){if(!e)return;let t=parseInt(e,10);return isNaN(t)?void 0:t}function aK(e){try{if("string"==typeof e)return[e];if(e instanceof URLSearchParams)return[e.toString()];if(e instanceof FormData)return[a2(e)];if(!e)return[void 0]}catch(e){return[void 0,"BODY_PARSE_ERROR"]}return[void 0,"UNPARSEABLE_BODY_TYPE"]}function aX(e,t){if(!e)return{headers:{},size:void 0,_meta:{warnings:[t]}};let n={...e._meta},r=n.warnings||[];return n.warnings=[...r,t],e._meta=n,e}function aQ(e,t){if(!t)return null;let{startTimestamp:n,endTimestamp:r,url:i,method:s,statusCode:a,request:o,response:l}=t;return{type:e,start:n/1e3,end:r/1e3,name:i,data:ec({method:s,statusCode:a,request:o,response:l})}}function aZ(e){return{headers:{},size:e,_meta:{warnings:["URL_SKIPPED"]}}}function a0(e,t,n){if(!t&&0===Object.keys(e).length)return;if(!t)return{headers:e};if(!n)return{headers:e,size:t};let r={headers:e,size:t},{body:i,warnings:s}=function(e){if(!e||"string"!=typeof e)return{body:e};let t=e.length>15e4,n=function(e){let t=e[0],n=e[e.length-1];return"["===t&&"]"===n||"{"===t&&"}"===n}(e);if(t){let t=e.slice(0,15e4);return n?{body:t,warnings:["MAYBE_JSON_TRUNCATED"]}:{body:`${t}…`,warnings:["TEXT_TRUNCATED"]}}if(n)try{return{body:JSON.parse(e)}}catch(e){}return{body:e}}(n);return r.body=i,s&&s.length>0&&(r._meta={warnings:s}),r}function a1(e,t){return Object.entries(e).reduce((n,[r,i])=>{let s=r.toLowerCase();return t.includes(s)&&e[r]&&(n[s]=i),n},{})}function a2(e){return new URLSearchParams(e).toString()}function a3(e,t=I.document.baseURI){if(e.startsWith("http://")||e.startsWith("https://")||e.startsWith(I.location.origin))return e;let n=new URL(e,t);if(n.origin!==new URL(t).origin)return e;let r=n.href;return!e.endsWith("/")&&r.endsWith("/")?r.slice(0,-1):r}async function a5(e,t,n){try{let r=await a4(e,t,n),i=aQ("resource.fetch",r);aY(n.replay,i)}catch(e){}}async function a4(e,t,n){var r,i;let s=Date.now(),{startTimestamp:a=s,endTimestamp:o=s}=t,{url:l,method:c,status_code:u=0,request_body_size:d,response_body_size:h}=e.data,p=(r=n.networkDetailAllowUrls,et(a3(l),r)&&(i=n.networkDetailDenyUrls,!et(a3(l),i)));return{startTimestamp:a,endTimestamp:o,url:l,method:c,statusCode:u,request:p?function({networkCaptureBodies:e,networkRequestHeaders:t},n,r){var i,s;let a=n?(i=n,s=t,1===i.length&&"string"!=typeof i[0]?oe(i[0],s):2===i.length?oe(i[1],s):{}):{};if(!e)return a0(a,r,void 0);let[o,l]=aK(a7(n)),c=a0(a,r,o);return l?aX(c,l):c}(n,t.input,d):aZ(d),response:await a8(p,n,t.response,h)}}async function a8(e,{networkCaptureBodies:t,networkResponseHeaders:n},r,i){if(!e&&void 0!==i)return aZ(i);let s=r?a9(r.headers,n):{};if(!r||!t&&void 0!==i)return a0(s,i,void 0);let[a,o]=await a6(r),l=function(e,{networkCaptureBodies:t,responseBodySize:n,captureDetails:r,headers:i}){try{let s=e&&e.length&&void 0===n?aJ(e):n;if(!r)return aZ(s);if(t)return a0(i,s,e);return a0(i,s,void 0)}catch(e){return a0(i,n,void 0)}}(a,{networkCaptureBodies:t,responseBodySize:i,captureDetails:e,headers:s});return o?aX(l,o):l}async function a6(e){let t=function(e){try{return e.clone()}catch(e){}}(e);if(!t)return[void 0,"BODY_PARSE_ERROR"];try{var n;return[await (n=t,new Promise((e,t)=>{let r=nV(()=>t(Error("Timeout while trying to read response body")),500);ot(n).then(t=>e(t),e=>t(e)).finally(()=>clearTimeout(r))}))]}catch(e){if(e instanceof Error&&e.message.indexOf("Timeout")>-1)return[void 0,"BODY_PARSE_TIMEOUT"];return[void 0,"BODY_PARSE_ERROR"]}}function a7(e=[]){if(2===e.length&&"object"==typeof e[1])return e[1].body}function a9(e,t){let n={};return t.forEach(t=>{e.get(t)&&(n[t]=e.get(t))}),n}function oe(e,t){if(!e)return{};let n=e.headers;return n?n instanceof Headers?a9(n,t):Array.isArray(n)?{}:a1(n,t):{}}async function ot(e){return await e.text()}async function on(e,t,n){try{let r=function(e,t,n){var r,i;let s=Date.now(),{startTimestamp:a=s,endTimestamp:o=s,input:l,xhr:c}=t,{url:u,method:d,status_code:h=0,request_body_size:p,response_body_size:f}=e.data;if(!u)return null;if(!c||(r=n.networkDetailAllowUrls,!et(a3(u),r))||(i=n.networkDetailDenyUrls,et(a3(u),i))){let e=aZ(p);return{startTimestamp:a,endTimestamp:o,url:u,method:d,statusCode:h,request:e,response:aZ(f)}}let m=c[no],g=m?a1(m.request_headers,n.networkRequestHeaders):{},y=a1(function(e){let t=e.getAllResponseHeaders();return t?t.split("\r\n").reduce((e,t)=>{let[n,r]=t.split(": ");return r&&(e[n.toLowerCase()]=r),e},{}):{}}(c),n.networkResponseHeaders),[_,v]=n.networkCaptureBodies?aK(l):[void 0],[S,b]=n.networkCaptureBodies?function(e){let t=[];try{return[e.responseText]}catch(e){t.push(e)}try{var n=e.response,r=e.responseType;try{if("string"==typeof n)return[n];if(n instanceof Document)return[n.body.outerHTML];if("json"===r&&n&&"object"==typeof n)return[JSON.stringify(n)];if(!n)return[void 0]}catch(e){return[void 0,"BODY_PARSE_ERROR"]}return[void 0,"UNPARSEABLE_BODY_TYPE"]}catch(e){t.push(e)}return[void 0]}(c):[void 0],E=a0(g,p,_),w=a0(y,f,S);return{startTimestamp:a,endTimestamp:o,url:u,method:d,statusCode:h,request:v?aX(E,v):E,response:b?aX(w,b):w}}(e,t,n),i=aQ("resource.xhr",r);aY(n.replay,i)}catch(e){}}async function or(e){try{return Promise.all(aV(e,[function(e){let{jsHeapSizeLimit:t,totalJSHeapSize:n,usedJSHeapSize:r}=e,i=Date.now()/1e3;return{type:"memory",name:"memory",start:i,end:i,data:{memory:{jsHeapSizeLimit:t,totalJSHeapSize:n,usedJSHeapSize:r}}}}(I.performance.memory)]))}catch(e){return[]}}async function oi({client:e,scope:t,replayId:n,event:r}){let i={event_id:n,integrations:"object"!=typeof e._integrations||null===e._integrations||Array.isArray(e._integrations)?void 0:Object.keys(e._integrations)};e.emit("preprocessEvent",r,i);let s=await tv(e.getOptions(),r,i,t,e,eO());if(!s)return null;s.platform=s.platform||"javascript";let a=e.getSdkMetadata(),{name:o,version:l}=a&&a.sdk||{};return s.sdk={...s.sdk,name:o||"sentry.javascript.unknown",version:l||"0.0.0"},s}async function os({recordingData:e,replayId:t,segmentId:n,eventContext:r,timestamp:i,session:s}){var a;let o,l=function({recordingData:e,headers:t}){let n,r=`${JSON.stringify(t)}
`;if("string"==typeof e)n=`${r}${e}`;else{let t=new TextEncoder().encode(r);(n=new Uint8Array(t.length+e.length)).set(t),n.set(e,t.length)}return n}({recordingData:e,headers:{segment_id:n}}),{urls:c,errorIds:u,traceIds:d,initialTimestamp:h}=r,p=eN(),f=eR(),m=p&&p.getTransport(),g=p&&p.getDsn();if(!p||!m||!g||!s.sampled)return eL({});let y={type:"replay_event",replay_start_timestamp:h/1e3,timestamp:i/1e3,error_ids:u,trace_ids:d,urls:c,replay_id:t,segment_id:n,replay_type:s.sampled},_=await oi({scope:f,client:p,replayId:t,event:y});if(!_)return p.recordDroppedEvent("event_processor","replay",y),eL({});delete _.sdkProcessingMetadata;let v=(a=p.getOptions().tunnel,tW(tY(_,tV(_),a,g),[[{type:"replay_event"},_],[{type:"replay_recording",length:"string"==typeof l?new TextEncoder().encode(l).length:l.length},l]]));try{o=await m.send(v)}catch(t){let e=Error(iA);try{e.cause=t}catch(e){}throw e}if("number"==typeof o.statusCode&&(o.statusCode<200||o.statusCode>=300))throw new oa(o.statusCode);let S=nJ({},o);if(nY(S,"replay"))throw new oo(S);return o}class oa extends Error{constructor(e){super(`Transport returned status code ${e}`)}}class oo extends Error{constructor(e){super("Rate limit hit"),this.rateLimits=e}}async function ol(e,t={count:0,interval:5e3}){let{recordingData:n,onError:r}=e;if(n.length)try{return await os(e),!0}catch(n){if(n instanceof oa||n instanceof oo)throw n;if(tE("Replays",{_retryCount:t.count}),r&&r(n),t.count>=3){let e=Error(`${iA} - max retries exceeded`);try{e.cause=n}catch(e){}throw e}return t.interval*=++t.count,new Promise((n,r)=>{nV(async()=>{try{await ol(e,t),n(!0)}catch(e){r(e)}},t.interval)})}}let oc="__THROTTLED";class ou{constructor({options:e,recordingOptions:t}){ou.prototype.__init.call(this),ou.prototype.__init2.call(this),ou.prototype.__init3.call(this),ou.prototype.__init4.call(this),ou.prototype.__init5.call(this),ou.prototype.__init6.call(this),this.eventBuffer=null,this.performanceEntries=[],this.replayPerformanceEntries=[],this.recordingMode="session",this.timeouts={sessionIdlePause:3e5,sessionIdleExpire:9e5},this._lastActivity=Date.now(),this._isEnabled=!1,this._isPaused=!1,this._requiresManualStart=!1,this._hasInitializedCoreListeners=!1,this._context={errorIds:new Set,traceIds:new Set,urls:[],initialTimestamp:Date.now(),initialUrl:""},this._recordingOptions=t,this._options=e,this._debouncedFlush=function(e,t,n){let r,i,s,a=n&&n.maxWait?Math.max(n.maxWait,t):0;function o(){return l(),r=e()}function l(){void 0!==i&&clearTimeout(i),void 0!==s&&clearTimeout(s),i=s=void 0}function c(){return i&&clearTimeout(i),i=nV(o,t),a&&void 0===s&&(s=nV(o,a)),r}return c.cancel=l,c.flush=function(){return void 0!==i||void 0!==s?o():r},c}(()=>this._flush(),this._options.flushMinDelay,{maxWait:this._options.flushMaxDelay}),this._throttledAddEvent=function(e,t,n){let r=new Map,i=!1;return(...t)=>{let n=Math.floor(Date.now()/1e3),s=n-5;if(r.forEach((e,t)=>{t<s&&r.delete(t)}),[...r.values()].reduce((e,t)=>e+t,0)>=300){let e=i;return i=!0,e?"__SKIPPED":oc}i=!1;let a=r.get(n)||0;return r.set(n,a+1),e(...t)}}((e,t)=>(function(e,t,n){return aB(e,t)?aU(e,t,n):Promise.resolve(null)})(this,e,t),0,0);let{slowClickTimeout:n,slowClickIgnoreSelectors:r}=this.getOptions(),i=n?{threshold:Math.min(3e3,n),timeout:n,scrollTimeout:300,ignoreSelector:r?r.join(","):""}:void 0;i&&(this.clickDetector=new ac(this,i))}getContext(){return this._context}isEnabled(){return this._isEnabled}isPaused(){return this._isPaused}isRecordingCanvas(){return!!this._canvas}getOptions(){return this._options}handleException(e){this._options.onError&&this._options.onError(e)}initializeSampling(e){let{errorSampleRate:t,sessionSampleRate:n}=this._options,r=t<=0&&n<=0;if(this._requiresManualStart=r,!r)this._initializeSessionForSampling(e),this.session&&!1!==this.session.sampled&&(this.recordingMode="buffer"===this.session.sampled&&0===this.session.segmentId?"buffer":"session",this._initializeRecording())}start(){if(this._isEnabled&&"session"===this.recordingMode||this._isEnabled&&"buffer"===this.recordingMode)return;this._updateUserActivity();let e=aj({maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire},{stickySession:this._options.stickySession,sessionSampleRate:1,allowBuffering:!1});this.session=e,this._initializeRecording()}startBuffering(){if(this._isEnabled)return;let e=aj({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration},{stickySession:this._options.stickySession,sessionSampleRate:0,allowBuffering:!0});this.session=e,this.recordingMode="buffer",this._initializeRecording()}startRecording(){try{var e;let t,n=this._canvas;this._stopRecording=at({...this._recordingOptions,..."buffer"===this.recordingMode?{checkoutEveryNms:6e4}:this._options._experiments.continuousCheckout&&{checkoutEveryNms:Math.max(36e4,this._options._experiments.continuousCheckout)},emit:(e=this,t=!1,(n,r)=>{if(!e.checkAndHandleExpiredSession())return;let i=r||!t;t=!0,e.clickDetector&&function(e,t){try{var n;if(n=t,3!==n.type)return;let{source:r}=t.data;if(al.has(r)&&e.registerMutation(t.timestamp),r===sR.Scroll&&e.registerScroll(t.timestamp),t.data.source===sR.MouseInteraction){let{type:n,id:r}=t.data,i=at.mirror.getNode(r);i instanceof HTMLElement&&n===sO.Click&&e.registerClick(i)}}catch(e){}}(e.clickDetector,n),e.addUpdate(()=>{var t;if("buffer"===e.recordingMode&&i&&e.setInitialState(),!aF(e,n,i))return!0;if(!i)return!1;let r=e.session;if(t=e,i&&t.session&&0===t.session.segmentId&&aF(t,function(e){let t=e.getOptions();return{type:sA.Custom,timestamp:Date.now(),data:{tag:"options",payload:{shouldRecordCanvas:e.isRecordingCanvas(),sessionSampleRate:t.sessionSampleRate,errorSampleRate:t.errorSampleRate,useCompressionOption:t.useCompression,blockAllMedia:t.blockAllMedia,maskAllText:t.maskAllText,maskAllInputs:t.maskAllInputs,useCompression:!!e.eventBuffer&&"worker"===e.eventBuffer.type,networkDetailHasUrls:t.networkDetailAllowUrls.length>0,networkCaptureBodies:t.networkCaptureBodies,networkRequestHasHeaders:t.networkRequestHeaders.length>0,networkResponseHasHeaders:t.networkResponseHeaders.length>0}}}}(t),!1),"buffer"===e.recordingMode&&r&&e.eventBuffer){let t=e.eventBuffer.getEarliestTimestamp();t&&(r.started=t,e.getOptions().stickySession&&aD(r))}return!!r&&!!r.previousSessionId||("session"===e.recordingMode&&e.flush(),!0)})}),onMutation:this._onMutationHandler,...n?{recordCanvas:n.recordCanvas,getCanvasManager:n.getCanvasManager,sampling:n.sampling,dataURLOptions:n.dataURLOptions}:{}})}catch(e){this.handleException(e)}}stopRecording(){try{return this._stopRecording&&(this._stopRecording(),this._stopRecording=void 0),!0}catch(e){return this.handleException(e),!1}}async stop({forceFlush:e=!1,reason:t}={}){if(this._isEnabled){this._isEnabled=!1;try{aq(),this._removeListeners(),this.stopRecording(),this._debouncedFlush.cancel(),e&&await this._flush({force:!0}),this.eventBuffer&&this.eventBuffer.destroy(),this.eventBuffer=null,function(){if(aA())try{I.sessionStorage.removeItem(iM)}catch(e){}}(),this.session=void 0}catch(e){this.handleException(e)}}}pause(){this._isPaused||(this._isPaused=!0,this.stopRecording())}resume(){this._isPaused&&this._checkSession()&&(this._isPaused=!1,this.startRecording())}async sendBufferedReplayOrFlush({continueRecording:e=!0}={}){if("session"===this.recordingMode)return this.flushImmediate();let t=Date.now();await this.flushImmediate();let n=this.stopRecording();e&&n&&"session"!==this.recordingMode&&(this.recordingMode="session",this.session&&(this._updateUserActivity(t),this._updateSessionActivity(t),this._maybeSaveSession()),this.startRecording())}addUpdate(e){let t=e();"buffer"!==this.recordingMode&&!0!==t&&this._debouncedFlush()}triggerUserActivity(){if(this._updateUserActivity(),!this._stopRecording){if(!this._checkSession())return;this.resume();return}this.checkAndHandleExpiredSession(),this._updateSessionActivity()}updateUserActivity(){this._updateUserActivity(),this._updateSessionActivity()}conditionalFlush(){return"buffer"===this.recordingMode?Promise.resolve():this.flushImmediate()}flush(){return this._debouncedFlush()}flushImmediate(){return this._debouncedFlush(),this._debouncedFlush.flush()}cancelFlush(){this._debouncedFlush.cancel()}getSessionId(){return this.session&&this.session.id}checkAndHandleExpiredSession(){return this._lastActivity&&aL(this._lastActivity,this.timeouts.sessionIdlePause)&&this.session&&"session"===this.session.sampled?void this.pause():!!this._checkSession()}setInitialState(){let e=`${I.location.pathname}${I.location.hash}${I.location.search}`,t=`${I.location.origin}${e}`;this.performanceEntries=[],this.replayPerformanceEntries=[],this._clearContext(),this._context.initialUrl=t,this._context.initialTimestamp=Date.now(),this._context.urls.push(t)}throttledAddEvent(e,t){let n=this._throttledAddEvent(e,t);if(n===oc){let e=ah({category:"replay.throttled"});this.addUpdate(()=>!aF(this,{type:5,timestamp:e.timestamp||0,data:{tag:"breadcrumb",payload:e,metric:!0}}))}return n}getCurrentRoute(){let e=this.lastActiveSpan||td(),t=e&&tu(e),n=(t&&tr(t).data||{})[eV];if(t&&n&&["route","custom"].includes(n))return tr(t).description}_initializeRecording(){this.setInitialState(),this._updateSessionActivity(),this.eventBuffer=function({useCompression:e,workerUrl:t}){if(e&&window.Worker){let e=function(e){try{let t=e||function(){if("undefined"==typeof __SENTRY_EXCLUDE_REPLAY_WORKER__||!__SENTRY_EXCLUDE_REPLAY_WORKER__){let e=new Blob(['var t=Uint8Array,n=Uint16Array,r=Int32Array,e=new t([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),i=new t([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),a=new t([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),s=function(t,e){for(var i=new n(31),a=0;a<31;++a)i[a]=e+=1<<t[a-1];var s=new r(i[30]);for(a=1;a<30;++a)for(var o=i[a];o<i[a+1];++o)s[o]=o-i[a]<<5|a;return{b:i,r:s}},o=s(e,2),f=o.b,h=o.r;f[28]=258,h[258]=28;for(var l=s(i,0).r,u=new n(32768),c=0;c<32768;++c){var v=(43690&c)>>1|(21845&c)<<1;v=(61680&(v=(52428&v)>>2|(13107&v)<<2))>>4|(3855&v)<<4,u[c]=((65280&v)>>8|(255&v)<<8)>>1}var d=function(t,r,e){for(var i=t.length,a=0,s=new n(r);a<i;++a)t[a]&&++s[t[a]-1];var o,f=new n(r);for(a=1;a<r;++a)f[a]=f[a-1]+s[a-1]<<1;if(e){o=new n(1<<r);var h=15-r;for(a=0;a<i;++a)if(t[a])for(var l=a<<4|t[a],c=r-t[a],v=f[t[a]-1]++<<c,d=v|(1<<c)-1;v<=d;++v)o[u[v]>>h]=l}else for(o=new n(i),a=0;a<i;++a)t[a]&&(o[a]=u[f[t[a]-1]++]>>15-t[a]);return o},g=new t(288);for(c=0;c<144;++c)g[c]=8;for(c=144;c<256;++c)g[c]=9;for(c=256;c<280;++c)g[c]=7;for(c=280;c<288;++c)g[c]=8;var w=new t(32);for(c=0;c<32;++c)w[c]=5;var p=d(g,9,0),y=d(w,5,0),m=function(t){return(t+7)/8|0},b=function(n,r,e){return(null==e||e>n.length)&&(e=n.length),new t(n.subarray(r,e))},M=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],E=function(t,n,r){var e=new Error(n||M[t]);if(e.code=t,Error.captureStackTrace&&Error.captureStackTrace(e,E),!r)throw e;return e},z=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8},_=function(t,n,r){r<<=7&n;var e=n/8|0;t[e]|=r,t[e+1]|=r>>8,t[e+2]|=r>>16},x=function(r,e){for(var i=[],a=0;a<r.length;++a)r[a]&&i.push({s:a,f:r[a]});var s=i.length,o=i.slice();if(!s)return{t:F,l:0};if(1==s){var f=new t(i[0].s+1);return f[i[0].s]=1,{t:f,l:1}}i.sort((function(t,n){return t.f-n.f})),i.push({s:-1,f:25001});var h=i[0],l=i[1],u=0,c=1,v=2;for(i[0]={s:-1,f:h.f+l.f,l:h,r:l};c!=s-1;)h=i[i[u].f<i[v].f?u++:v++],l=i[u!=c&&i[u].f<i[v].f?u++:v++],i[c++]={s:-1,f:h.f+l.f,l:h,r:l};var d=o[0].s;for(a=1;a<s;++a)o[a].s>d&&(d=o[a].s);var g=new n(d+1),w=A(i[c-1],g,0);if(w>e){a=0;var p=0,y=w-e,m=1<<y;for(o.sort((function(t,n){return g[n.s]-g[t.s]||t.f-n.f}));a<s;++a){var b=o[a].s;if(!(g[b]>e))break;p+=m-(1<<w-g[b]),g[b]=e}for(p>>=y;p>0;){var M=o[a].s;g[M]<e?p-=1<<e-g[M]++-1:++a}for(;a>=0&&p;--a){var E=o[a].s;g[E]==e&&(--g[E],++p)}w=e}return{t:new t(g),l:w}},A=function(t,n,r){return-1==t.s?Math.max(A(t.l,n,r+1),A(t.r,n,r+1)):n[t.s]=r},D=function(t){for(var r=t.length;r&&!t[--r];);for(var e=new n(++r),i=0,a=t[0],s=1,o=function(t){e[i++]=t},f=1;f<=r;++f)if(t[f]==a&&f!=r)++s;else{if(!a&&s>2){for(;s>138;s-=138)o(32754);s>2&&(o(s>10?s-11<<5|28690:s-3<<5|12305),s=0)}else if(s>3){for(o(a),--s;s>6;s-=6)o(8304);s>2&&(o(s-3<<5|8208),s=0)}for(;s--;)o(a);s=1,a=t[f]}return{c:e.subarray(0,i),n:r}},T=function(t,n){for(var r=0,e=0;e<n.length;++e)r+=t[e]*n[e];return r},k=function(t,n,r){var e=r.length,i=m(n+2);t[i]=255&e,t[i+1]=e>>8,t[i+2]=255^t[i],t[i+3]=255^t[i+1];for(var a=0;a<e;++a)t[i+a+4]=r[a];return 8*(i+4+e)},U=function(t,r,s,o,f,h,l,u,c,v,m){z(r,m++,s),++f[256];for(var b=x(f,15),M=b.t,E=b.l,A=x(h,15),U=A.t,C=A.l,F=D(M),I=F.c,S=F.n,L=D(U),O=L.c,j=L.n,q=new n(19),B=0;B<I.length;++B)++q[31&I[B]];for(B=0;B<O.length;++B)++q[31&O[B]];for(var G=x(q,7),H=G.t,J=G.l,K=19;K>4&&!H[a[K-1]];--K);var N,P,Q,R,V=v+5<<3,W=T(f,g)+T(h,w)+l,X=T(f,M)+T(h,U)+l+14+3*K+T(q,H)+2*q[16]+3*q[17]+7*q[18];if(c>=0&&V<=W&&V<=X)return k(r,m,t.subarray(c,c+v));if(z(r,m,1+(X<W)),m+=2,X<W){N=d(M,E,0),P=M,Q=d(U,C,0),R=U;var Y=d(H,J,0);z(r,m,S-257),z(r,m+5,j-1),z(r,m+10,K-4),m+=14;for(B=0;B<K;++B)z(r,m+3*B,H[a[B]]);m+=3*K;for(var Z=[I,O],$=0;$<2;++$){var tt=Z[$];for(B=0;B<tt.length;++B){var nt=31&tt[B];z(r,m,Y[nt]),m+=H[nt],nt>15&&(z(r,m,tt[B]>>5&127),m+=tt[B]>>12)}}}else N=p,P=g,Q=y,R=w;for(B=0;B<u;++B){var rt=o[B];if(rt>255){_(r,m,N[(nt=rt>>18&31)+257]),m+=P[nt+257],nt>7&&(z(r,m,rt>>23&31),m+=e[nt]);var et=31&rt;_(r,m,Q[et]),m+=R[et],et>3&&(_(r,m,rt>>5&8191),m+=i[et])}else _(r,m,N[rt]),m+=P[rt]}return _(r,m,N[256]),m+P[256]},C=new r([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),F=new t(0),I=function(){for(var t=new Int32Array(256),n=0;n<256;++n){for(var r=n,e=9;--e;)r=(1&r&&-306674912)^r>>>1;t[n]=r}return t}(),S=function(){var t=-1;return{p:function(n){for(var r=t,e=0;e<n.length;++e)r=I[255&r^n[e]]^r>>>8;t=r},d:function(){return~t}}},L=function(){var t=1,n=0;return{p:function(r){for(var e=t,i=n,a=0|r.length,s=0;s!=a;){for(var o=Math.min(s+2655,a);s<o;++s)i+=e+=r[s];e=(65535&e)+15*(e>>16),i=(65535&i)+15*(i>>16)}t=e,n=i},d:function(){return(255&(t%=65521))<<24|(65280&t)<<8|(255&(n%=65521))<<8|n>>8}}},O=function(a,s,o,f,u){if(!u&&(u={l:1},s.dictionary)){var c=s.dictionary.subarray(-32768),v=new t(c.length+a.length);v.set(c),v.set(a,c.length),a=v,u.w=c.length}return function(a,s,o,f,u,c){var v=c.z||a.length,d=new t(f+v+5*(1+Math.ceil(v/7e3))+u),g=d.subarray(f,d.length-u),w=c.l,p=7&(c.r||0);if(s){p&&(g[0]=c.r>>3);for(var y=C[s-1],M=y>>13,E=8191&y,z=(1<<o)-1,_=c.p||new n(32768),x=c.h||new n(z+1),A=Math.ceil(o/3),D=2*A,T=function(t){return(a[t]^a[t+1]<<A^a[t+2]<<D)&z},F=new r(25e3),I=new n(288),S=new n(32),L=0,O=0,j=c.i||0,q=0,B=c.w||0,G=0;j+2<v;++j){var H=T(j),J=32767&j,K=x[H];if(_[J]=K,x[H]=J,B<=j){var N=v-j;if((L>7e3||q>24576)&&(N>423||!w)){p=U(a,g,0,F,I,S,O,q,G,j-G,p),q=L=O=0,G=j;for(var P=0;P<286;++P)I[P]=0;for(P=0;P<30;++P)S[P]=0}var Q=2,R=0,V=E,W=J-K&32767;if(N>2&&H==T(j-W))for(var X=Math.min(M,N)-1,Y=Math.min(32767,j),Z=Math.min(258,N);W<=Y&&--V&&J!=K;){if(a[j+Q]==a[j+Q-W]){for(var $=0;$<Z&&a[j+$]==a[j+$-W];++$);if($>Q){if(Q=$,R=W,$>X)break;var tt=Math.min(W,$-2),nt=0;for(P=0;P<tt;++P){var rt=j-W+P&32767,et=rt-_[rt]&32767;et>nt&&(nt=et,K=rt)}}}W+=(J=K)-(K=_[J])&32767}if(R){F[q++]=268435456|h[Q]<<18|l[R];var it=31&h[Q],at=31&l[R];O+=e[it]+i[at],++I[257+it],++S[at],B=j+Q,++L}else F[q++]=a[j],++I[a[j]]}}for(j=Math.max(j,B);j<v;++j)F[q++]=a[j],++I[a[j]];p=U(a,g,w,F,I,S,O,q,G,j-G,p),w||(c.r=7&p|g[p/8|0]<<3,p-=7,c.h=x,c.p=_,c.i=j,c.w=B)}else{for(j=c.w||0;j<v+w;j+=65535){var st=j+65535;st>=v&&(g[p/8|0]=w,st=v),p=k(g,p+1,a.subarray(j,st))}c.i=v}return b(d,0,f+m(p)+u)}(a,null==s.level?6:s.level,null==s.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(a.length)))):12+s.mem,o,f,u)},j=function(t,n,r){for(;r;++n)t[n]=r,r>>>=8},q=function(t,n){var r=n.filename;if(t[0]=31,t[1]=139,t[2]=8,t[8]=n.level<2?4:9==n.level?2:0,t[9]=3,0!=n.mtime&&j(t,4,Math.floor(new Date(n.mtime||Date.now())/1e3)),r){t[3]=8;for(var e=0;e<=r.length;++e)t[e+10]=r.charCodeAt(e)}},B=function(t){return 10+(t.filename?t.filename.length+1:0)},G=function(){function n(n,r){if("function"==typeof n&&(r=n,n={}),this.ondata=r,this.o=n||{},this.s={l:0,i:32768,w:32768,z:32768},this.b=new t(98304),this.o.dictionary){var e=this.o.dictionary.subarray(-32768);this.b.set(e,32768-e.length),this.s.i=32768-e.length}}return n.prototype.p=function(t,n){this.ondata(O(t,this.o,0,0,this.s),n)},n.prototype.push=function(n,r){this.ondata||E(5),this.s.l&&E(4);var e=n.length+this.s.z;if(e>this.b.length){if(e>2*this.b.length-32768){var i=new t(-32768&e);i.set(this.b.subarray(0,this.s.z)),this.b=i}var a=this.b.length-this.s.z;a&&(this.b.set(n.subarray(0,a),this.s.z),this.s.z=this.b.length,this.p(this.b,!1)),this.b.set(this.b.subarray(-32768)),this.b.set(n.subarray(a),32768),this.s.z=n.length-a+32768,this.s.i=32766,this.s.w=32768}else this.b.set(n,this.s.z),this.s.z+=n.length;this.s.l=1&r,(this.s.z>this.s.w+8191||r)&&(this.p(this.b,r||!1),this.s.w=this.s.i,this.s.i-=2)},n}();var H=function(){function t(t,n){this.c=L(),this.v=1,G.call(this,t,n)}return t.prototype.push=function(t,n){this.c.p(t),G.prototype.push.call(this,t,n)},t.prototype.p=function(t,n){var r=O(t,this.o,this.v&&(this.o.dictionary?6:2),n&&4,this.s);this.v&&(function(t,n){var r=n.level,e=0==r?0:r<6?1:9==r?3:2;if(t[0]=120,t[1]=e<<6|(n.dictionary&&32),t[1]|=31-(t[0]<<8|t[1])%31,n.dictionary){var i=L();i.p(n.dictionary),j(t,2,i.d())}}(r,this.o),this.v=0),n&&j(r,r.length-4,this.c.d()),this.ondata(r,n)},t}(),J="undefined"!=typeof TextEncoder&&new TextEncoder,K="undefined"!=typeof TextDecoder&&new TextDecoder;try{K.decode(F,{stream:!0})}catch(t){}var N=function(){function t(t){this.ondata=t}return t.prototype.push=function(t,n){this.ondata||E(5),this.d&&E(4),this.ondata(P(t),this.d=n||!1)},t}();function P(n,r){if(J)return J.encode(n);for(var e=n.length,i=new t(n.length+(n.length>>1)),a=0,s=function(t){i[a++]=t},o=0;o<e;++o){if(a+5>i.length){var f=new t(a+8+(e-o<<1));f.set(i),i=f}var h=n.charCodeAt(o);h<128||r?s(h):h<2048?(s(192|h>>6),s(128|63&h)):h>55295&&h<57344?(s(240|(h=65536+(1047552&h)|1023&n.charCodeAt(++o))>>18),s(128|h>>12&63),s(128|h>>6&63),s(128|63&h)):(s(224|h>>12),s(128|h>>6&63),s(128|63&h))}return b(i,0,a)}function Q(t){return function(t,n){n||(n={});var r=S(),e=t.length;r.p(t);var i=O(t,n,B(n),8),a=i.length;return q(i,n),j(i,a-8,r.d()),j(i,a-4,e),i}(P(t))}const R=new class{constructor(){this._init()}clear(){this._init()}addEvent(t){if(!t)throw new Error("Adding invalid event");const n=this._hasEvents?",":"";this.stream.push(n+t),this._hasEvents=!0}finish(){this.stream.push("]",!0);const t=function(t){let n=0;for(const r of t)n+=r.length;const r=new Uint8Array(n);for(let n=0,e=0,i=t.length;n<i;n++){const i=t[n];r.set(i,e),e+=i.length}return r}(this._deflatedData);return this._init(),t}_init(){this._hasEvents=!1,this._deflatedData=[],this.deflate=new H,this.deflate.ondata=(t,n)=>{this._deflatedData.push(t)},this.stream=new N(((t,n)=>{this.deflate.push(t,n)})),this.stream.push("[")}},V={clear:()=>{R.clear()},addEvent:t=>R.addEvent(t),finish:()=>R.finish(),compress:t=>Q(t)};addEventListener("message",(function(t){const n=t.data.method,r=t.data.id,e=t.data.arg;if(n in V&&"function"==typeof V[n])try{const t=V[n](e);postMessage({id:r,method:n,success:!0,response:t})}catch(t){postMessage({id:r,method:n,success:!1,response:t.message}),console.error(t)}})),postMessage({id:void 0,method:"init",success:!0,response:void 0});']);return URL.createObjectURL(e)}return""}();if(!t)return;let n=new Worker(t);return new aM(n)}catch(e){}}(t);if(e)return e}return new ax}({useCompression:this._options.useCompression,workerUrl:this._options.workerUrl}),this._removeListeners(),this._addListeners(),this._isEnabled=!0,this._isPaused=!1,this.startRecording()}_initializeSessionForSampling(e){let t=this._options.errorSampleRate>0,n=aj({sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration,previousSessionId:e},{stickySession:this._options.stickySession,sessionSampleRate:this._options.sessionSampleRate,allowBuffering:t});this.session=n}_checkSession(){if(!this.session)return!1;let e=this.session;return!a$(e,{sessionIdleExpire:this.timeouts.sessionIdleExpire,maxReplayDuration:this._options.maxReplayDuration})||(this._refreshSession(e),!1)}async _refreshSession(e){this._isEnabled&&(await this.stop({reason:"refresh session"}),this.initializeSampling(e.id))}_addListeners(){try{I.document.addEventListener("visibilitychange",this._handleVisibilityChange),I.addEventListener("blur",this._handleWindowBlur),I.addEventListener("focus",this._handleWindowFocus),I.addEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.addListeners(),this._hasInitializedCoreListeners||(!function(e){let t=eN();ni(t=>{var n,r;if(!e.isEnabled())return;let i=function(e){let{target:t,message:n}=function(e){let t,n="click"===e.name,r=null;try{r=n?aa(e.event):ao(e.event),t=V(r,{maxStringLength:200})||"<unknown>"}catch(e){t="<unknown>"}return{target:r,message:t}}(e);return ah({category:`ui.${e.name}`,...af(t,n)})}(t);if(!i)return;let s="click"===t.name,a=s?t.event:void 0;s&&e.clickDetector&&a&&a.target&&!a.altKey&&!a.metaKey&&!a.ctrlKey&&!a.shiftKey&&(n=e.clickDetector,r=aa(t.event),n.handleClick(i,r)),ai(e,i)}),nu(t=>{if(!e.isEnabled())return;let n=function(e){let{from:t,to:n}=e,r=Date.now()/1e3;return{type:"navigation.push",start:r,end:r,name:n,data:{previous:t}}}(t);null!==n&&(e.getContext().urls.push(n.name),e.triggerUserActivity(),e.addUpdate(()=>(aV(e,[n]),!1)))});let n=eN();n&&n.on("beforeAddBreadcrumb",t=>(function(e,t){var n;if(!e.isEnabled()||!aH(t))return;let r=(n=t,!aH(n)||["fetch","xhr","sentry.event","sentry.transaction"].includes(n.category)||n.category.startsWith("ui.")?null:"console"===n.category?function(e){let t=e.data&&e.data.arguments;if(!Array.isArray(t)||0===t.length)return ah(e);let n=!1,r=t.map(e=>{if(!e)return e;if("string"==typeof e)return e.length>5e3?(n=!0,`${e.slice(0,5e3)}…`):e;if("object"==typeof e)try{let t=eq(e,7);if(JSON.stringify(t).length>5e3)return n=!0,`${JSON.stringify(t,null,2).slice(0,5e3)}…`;return t}catch(e){}return e});return ah({...e,data:{...e.data,arguments:r,...n?{_meta:{warnings:["CONSOLE_ARG_TRUNCATED"]}}:{}}})}(n):ah(n));r&&ai(e,r)})(e,t));let r=eN();try{let{networkDetailAllowUrls:t,networkDetailDenyUrls:n,networkCaptureBodies:i,networkRequestHeaders:s,networkResponseHeaders:a}=e.getOptions(),o={replay:e,networkDetailAllowUrls:t,networkDetailDenyUrls:n,networkCaptureBodies:i,networkRequestHeaders:s,networkResponseHeaders:a};r&&r.on("beforeAddBreadcrumb",(e,t)=>(function(e,t,n){if(t.data)try{var r,i,s,a;if(r=t,"xhr"===r.category&&(i=n)&&i.xhr&&(!function(e,t){let{xhr:n,input:r}=t;if(!n)return;let i=aJ(r),s=n.getResponseHeader("content-length")?aG(n.getResponseHeader("content-length")):function(e,t){try{let n="json"===t&&e&&"object"==typeof e?JSON.stringify(e):e;return aJ(n)}catch(e){return}}(n.response,n.responseType);void 0!==i&&(e.data.request_body_size=i),void 0!==s&&(e.data.response_body_size=s)}(t,n),on(t,n,e)),s=t,"fetch"===s.category&&(a=n)&&a.response){let{input:r,response:i}=n,s=aJ(r?a7(r):void 0),a=i?aG(i.headers.get("content-length")):void 0;void 0!==s&&(t.data.request_body_size=s),void 0!==a&&(t.data.response_body_size=a),a5(t,n,e)}}catch(e){}})(o,e,t))}catch(e){}tw(Object.assign((t,n)=>!e.isEnabled()||e.isPaused()?t:"replay_event"===t.type?(delete t.breadcrumbs,t):!t.type||aW(t)||az(t)?e.checkAndHandleExpiredSession()?az(t)?(e.flush(),t.contexts.feedback.replay_id=e.getSessionId(),e.triggerUserActivity(),e.addUpdate(()=>!t.timestamp||(e.throttledAddEvent({type:sA.Custom,timestamp:1e3*t.timestamp,data:{tag:"breadcrumb",payload:{timestamp:t.timestamp,type:"default",category:"sentry.feedback",data:{feedbackId:t.event_id}}}}),!1)),t):!t.type&&t.exception&&t.exception.values&&t.exception.values.length&&n.originalException&&n.originalException.__rrweb__&&!e.getOptions()._experiments.captureExceptions?null:(("buffer"===e.recordingMode&&t.message!==iA&&t.exception&&!t.type&&aR(e.getOptions().errorSampleRate)||"session"===e.recordingMode)&&(t.tags={...t.tags,replayId:e.getSessionId()}),t):(aq(),t):t,{id:"Replay"})),t&&(t.on("beforeSendEvent",t=>{e.isEnabled()&&!t.type&&function(e,t){let n=t.exception&&t.exception.values&&t.exception.values[0]&&t.exception.values[0].value;"string"==typeof n&&(n.match(/(reactjs\.org\/docs\/error-decoder\.html\?invariant=|react\.dev\/errors\/)(418|419|422|423|425)/)||n.match(/(does not match server-rendered HTML|Hydration failed because)/i))&&ai(e,ah({category:"replay.hydrate-error",data:{url:Y()}}))}(e,t)}),t.on("afterSendEvent",(t,n)=>{if(!e.isEnabled()||t.type&&!aW(t))return;let r=n&&n.statusCode;if(r&&!(r<200)&&!(r>=300)){if(aW(t))return void function(e,t){let n=e.getContext();t.contexts&&t.contexts.trace&&t.contexts.trace.trace_id&&n.traceIds.size<100&&n.traceIds.add(t.contexts.trace.trace_id)}(e,t);!function(e,t){let n=e.getContext();if(t.event_id&&n.errorIds.size<100&&n.errorIds.add(t.event_id),"buffer"!==e.recordingMode||!t.tags||!t.tags.replayId)return;let{beforeErrorSampling:r}=e.getOptions();("function"!=typeof r||r(t))&&nV(async()=>{try{await e.sendBufferedReplayOrFlush()}catch(t){e.handleException(t)}})}(e,t)}}),t.on("createDsc",t=>{let n=e.getSessionId();n&&e.isEnabled()&&"session"===e.recordingMode&&e.checkAndHandleExpiredSession()&&(t.replay_id=n)}),t.on("spanStart",t=>{e.lastActiveSpan=t}),t.on("spanEnd",t=>{e.lastActiveSpan=t}),t.on("beforeSendFeedback",(t,n)=>{let r=e.getSessionId();n&&n.includeReplay&&e.isEnabled()&&r&&t.contexts&&t.contexts.feedback&&(t.contexts.feedback.replay_id=r)}))}(this),this._hasInitializedCoreListeners=!0)}catch(e){this.handleException(e)}this._performanceCleanupCallback=function(e){function t(t){e.performanceEntries.includes(t)||e.performanceEntries.push(t)}function n({entries:e}){e.forEach(t)}let r=[];return["navigation","paint","resource"].forEach(e=>{r.push(rR(e,n))}),r.push(rC(ag(av,e)),rI(ag(aS,e)),rM(ag(ab,e)),rA(ag(aE,e))),()=>{r.forEach(e=>e())}}(this)}_removeListeners(){try{I.document.removeEventListener("visibilitychange",this._handleVisibilityChange),I.removeEventListener("blur",this._handleWindowBlur),I.removeEventListener("focus",this._handleWindowFocus),I.removeEventListener("keydown",this._handleKeyboardEvent),this.clickDetector&&this.clickDetector.removeListeners(),this._performanceCleanupCallback&&this._performanceCleanupCallback()}catch(e){this.handleException(e)}}__init(){this._handleVisibilityChange=()=>{"visible"===I.document.visibilityState?this._doChangeToForegroundTasks():this._doChangeToBackgroundTasks()}}__init2(){this._handleWindowBlur=()=>{let e=ah({category:"ui.blur"});this._doChangeToBackgroundTasks(e)}}__init3(){this._handleWindowFocus=()=>{let e=ah({category:"ui.focus"});this._doChangeToForegroundTasks(e)}}__init4(){this._handleKeyboardEvent=e=>{!function(e,t){if(!e.isEnabled())return;e.updateUserActivity();let n=function(e){var t;let{metaKey:n,shiftKey:r,ctrlKey:i,altKey:s,key:a,target:o}=e;if(!o||"INPUT"===(t=o).tagName||"TEXTAREA"===t.tagName||t.isContentEditable||!a)return null;let l=n||i||s,c=1===a.length;if(!l&&c)return null;let u=V(o,{maxStringLength:200})||"<unknown>",d=af(o,u);return ah({category:"ui.keyDown",message:u,data:{...d.data,metaKey:n,shiftKey:r,ctrlKey:i,altKey:s,key:a}})}(t);n&&ai(e,n)}(this,e)}}_doChangeToBackgroundTasks(e){this.session&&(aP(this.session,{maxReplayDuration:this._options.maxReplayDuration,sessionIdleExpire:this.timeouts.sessionIdleExpire})||(e&&this._createCustomBreadcrumb(e),this.conditionalFlush()))}_doChangeToForegroundTasks(e){if(this.session)this.checkAndHandleExpiredSession()&&e&&this._createCustomBreadcrumb(e)}_updateUserActivity(e=Date.now()){this._lastActivity=e}_updateSessionActivity(e=Date.now()){this.session&&(this.session.lastActivity=e,this._maybeSaveSession())}_createCustomBreadcrumb(e){this.addUpdate(()=>{this.throttledAddEvent({type:sA.Custom,timestamp:e.timestamp||0,data:{tag:"breadcrumb",payload:e}})})}_addPerformanceEntries(){let e=this.performanceEntries.map(ay).filter(Boolean).concat(this.replayPerformanceEntries);if(this.performanceEntries=[],this.replayPerformanceEntries=[],this._requiresManualStart){let t=this._context.initialTimestamp/1e3;e=e.filter(e=>e.start>=t)}return Promise.all(aV(this,e))}_clearContext(){this._context.errorIds.clear(),this._context.traceIds.clear(),this._context.urls=[]}_updateInitialTimestampFromEventBuffer(){let{session:e,eventBuffer:t}=this;if(!e||!t||this._requiresManualStart||e.segmentId)return;let n=t.getEarliestTimestamp();n&&n<this._context.initialTimestamp&&(this._context.initialTimestamp=n)}_popEventContext(){let e={initialTimestamp:this._context.initialTimestamp,initialUrl:this._context.initialUrl,errorIds:Array.from(this._context.errorIds),traceIds:Array.from(this._context.traceIds),urls:this._context.urls};return this._clearContext(),e}async _runFlush(){let e=this.getSessionId();if(this.session&&this.eventBuffer&&e&&(await this._addPerformanceEntries(),this.eventBuffer&&this.eventBuffer.hasEvents)){if((await or(this),this.eventBuffer)&&e===this.getSessionId())try{this._updateInitialTimestampFromEventBuffer();let t=Date.now();if(t-this._context.initialTimestamp>this._options.maxReplayDuration+3e4)throw Error("Session is too long, not sending replay");let n=this._popEventContext(),r=this.session.segmentId++;this._maybeSaveSession();let i=await this.eventBuffer.finish();await ol({replayId:e,recordingData:i,segmentId:r,eventContext:n,session:this.session,timestamp:t,onError:e=>this.handleException(e)})}catch(t){this.handleException(t),this.stop({reason:"sendReplay"});let e=eN();e&&e.recordDroppedEvent(t instanceof oo?"ratelimit_backoff":"send_error","replay")}}}__init5(){this._flush=async({force:e=!1}={})=>{if(!this._isEnabled&&!e||!this.checkAndHandleExpiredSession()||!this.session)return;let t=this.session.started,n=Date.now()-t;this._debouncedFlush.cancel();let r=n<this._options.minReplayDuration,i=n>this._options.maxReplayDuration+5e3;if(r||i){r&&this._debouncedFlush();return}let s=this.eventBuffer;s&&0===this.session.segmentId&&s.hasCheckout;let a=!!this._flushLock;this._flushLock||(this._flushLock=this._runFlush());try{await this._flushLock}catch(e){this.handleException(e)}finally{this._flushLock=void 0,a&&this._debouncedFlush()}}}_maybeSaveSession(){this.session&&this._options.stickySession&&aD(this.session)}__init6(){this._onMutationHandler=e=>{let t=e.length,n=this._options.mutationLimit,r=this._options.mutationBreadcrumbLimit,i=n&&t>n;if(t>r||i){let e=ah({category:"replay.mutations",data:{count:t,limit:i}});this._createCustomBreadcrumb(e)}return!i||(this.stop({reason:"mutationLimit",forceFlush:"session"===this.recordingMode}),!1)}}}function od(e,t){return[...e,...t].join(",")}let oh='img,image,svg,video,object,picture,embed,map,audio,link[rel="icon"],link[rel="apple-touch-icon"]',op=["content-length","content-type","accept"],of=!1;class om{static __initStatic(){this.id="Replay"}constructor({flushMinDelay:e=5e3,flushMaxDelay:t=5500,minReplayDuration:n=4999,maxReplayDuration:r=36e5,stickySession:i=!0,useCompression:s=!0,workerUrl:a,_experiments:o={},maskAllText:l=!0,maskAllInputs:c=!0,blockAllMedia:u=!0,mutationBreadcrumbLimit:d=750,mutationLimit:h=1e4,slowClickTimeout:p=7e3,slowClickIgnoreSelectors:f=[],networkDetailAllowUrls:m=[],networkDetailDenyUrls:g=[],networkCaptureBodies:y=!0,networkRequestHeaders:_=[],networkResponseHeaders:v=[],mask:S=[],maskAttributes:b=["title","placeholder"],unmask:E=[],block:w=[],unblock:k=[],ignore:T=[],maskFn:x,beforeAddRecordingEvent:I,beforeErrorSampling:C,onError:M}={}){this.name=om.id;let A=function({mask:e,unmask:t,block:n,unblock:r,ignore:i}){return{maskTextSelector:od(e,[".sentry-mask","[data-sentry-mask]"]),unmaskTextSelector:od(t,[]),blockSelector:od(n,[".sentry-block","[data-sentry-block]",'base[href="/"]']),unblockSelector:od(r,[]),ignoreSelector:od(i,[".sentry-ignore","[data-sentry-ignore]",'input[type="file"]'])}}({mask:S,unmask:E,block:w,unblock:k,ignore:T});if(this._recordingOptions={maskAllInputs:c,maskAllText:l,maskInputOptions:{password:!0},maskTextFn:x,maskInputFn:x,maskAttributeFn:(e,t,n)=>(function({el:e,key:t,maskAttributes:n,maskAllText:r,privacyOptions:i,value:s}){return!r||i.unmaskTextSelector&&e.matches(i.unmaskTextSelector)?s:n.includes(t)||"value"===t&&"INPUT"===e.tagName&&["submit","button"].includes(e.getAttribute("type")||"")?s.replace(/[\S]/g,"*"):s})({maskAttributes:b,maskAllText:l,privacyOptions:A,key:e,value:t,el:n}),...A,slimDOMOptions:"all",inlineStylesheet:!0,inlineImages:!1,collectFonts:!0,errorHandler:e=>{try{e.__rrweb__=!0}catch(e){}}},this._initialOptions={flushMinDelay:e,flushMaxDelay:t,minReplayDuration:Math.min(n,15e3),maxReplayDuration:Math.min(r,36e5),stickySession:i,useCompression:s,workerUrl:a,blockAllMedia:u,maskAllInputs:c,maskAllText:l,mutationBreadcrumbLimit:d,mutationLimit:h,slowClickTimeout:p,slowClickIgnoreSelectors:f,networkDetailAllowUrls:m,networkDetailDenyUrls:g,networkCaptureBodies:y,networkRequestHeaders:og(_),networkResponseHeaders:og(v),beforeAddRecordingEvent:I,beforeErrorSampling:C,onError:M,_experiments:o},this._initialOptions.blockAllMedia&&(this._recordingOptions.blockSelector=this._recordingOptions.blockSelector?`${this._recordingOptions.blockSelector},${oh}`:oh),this._isInitialized&&iC())throw Error("Multiple Sentry Session Replay instances are not supported");this._isInitialized=!0}get _isInitialized(){return of}set _isInitialized(e){of=e}afterAllSetup(e){iC()&&!this._replay&&(this._setup(e),this._initialize(e))}start(){this._replay&&this._replay.start()}startBuffering(){this._replay&&this._replay.startBuffering()}stop(){return this._replay?this._replay.stop({forceFlush:"session"===this._replay.recordingMode}):Promise.resolve()}flush(e){return this._replay?this._replay.isEnabled()?this._replay.sendBufferedReplayOrFlush(e):(this._replay.start(),Promise.resolve()):Promise.resolve()}getReplayId(){if(this._replay&&this._replay.isEnabled())return this._replay.getSessionId()}getRecordingMode(){if(this._replay&&this._replay.isEnabled())return this._replay.recordingMode}_initialize(e){this._replay&&(this._maybeLoadFromReplayCanvasIntegration(e),this._replay.initializeSampling())}_setup(e){let t=function(e,t){let n=t.getOptions(),r={sessionSampleRate:0,errorSampleRate:0,...ec(e)},i=tG(n.replaysSessionSampleRate),s=tG(n.replaysOnErrorSampleRate);return null==i&&null==s&&X(()=>{console.warn("Replay is disabled because neither `replaysSessionSampleRate` nor `replaysOnErrorSampleRate` are set.")}),null!=i&&(r.sessionSampleRate=i),null!=s&&(r.errorSampleRate=s),r}(this._initialOptions,e);this._replay=new ou({options:t,recordingOptions:this._recordingOptions})}_maybeLoadFromReplayCanvasIntegration(e){try{let t=e.getIntegrationByName("ReplayCanvas");if(!t)return;this._replay._canvas=t.getOptions()}catch(e){}}}function og(e){return[...op,...e.map(e=>e.toLowerCase())]}om.__initStatic();var oy=n(6953);globalThis._sentryRewritesTunnelPath=void 0,globalThis.SENTRY_RELEASE={id:"5vSlIgqeRTAp1o6V6s2SH"},globalThis._sentryBasePath=void 0,globalThis._sentryRewriteFramesAssetPrefixPath="",!function(e){let t={environment:function(e){let t=e?n5.env.NEXT_PUBLIC_VERCEL_ENV:n5.env.VERCEL_ENV;return t?`vercel-${t}`:void 0}(!0)||"production",defaultIntegrations:function(e){let t=nX(e);("undefined"==typeof __SENTRY_TRACING__||__SENTRY_TRACING__)&&t.push(function(e={}){let t=((e={})=>{ir||(ir=!0,nC(ii),nR(ii));let{enableInp:t,enableLongTask:n,enableLongAnimationFrame:r,_experiments:{enableInteractions:i,enableStandaloneClsSpans:s},beforeStartSpan:a,idleTimeout:o,finalTimeout:l,childSpanTimeout:c,markBackgroundSpan:u,traceFetch:d,traceXHR:h,trackFetchStreamPerformance:p,shouldCreateSpanForRequest:f,enableHTTPTimings:g,instrumentPageLoad:v,instrumentNavigation:S}={...ip,...e},b=function({recordClsStandaloneSpans:e}){let t=r1();if(t&&eh){t.mark&&I.performance.mark("sentry-tracing-init");let n=rM(({metric:e})=>{let t=e.entries[e.entries.length-1];if(!t)return;let n=r2(eh),r=r2(t.startTime);r5.fid={value:e.value,unit:"millisecond"},r5["mark.fid"]={value:n+r,unit:"second"}}),r=rC(({metric:e})=>{let t=e.entries[e.entries.length-1];t&&(r5.lcp={value:e.value,unit:"millisecond"},y=t)},!0),i=rj("ttfb",({metric:e})=>{e.entries[e.entries.length-1]&&(r5.ttfb={value:e.value,unit:"millisecond"})},rP,m),s=e?function(){let e,t,n=0;if(!function(){try{return PerformanceObserver.supportedEntryTypes.includes("layout-shift")}catch(e){return!1}}())return;let r=!1;function i(){r||(r=!0,t&&function(e,t,n){let r=r2((eh||0)+(t&&t.startTime||0)),i=eR().getScopeData().transactionName,s=r0({name:t?V(t.sources[0]&&t.sources[0].node):"Layout shift",transaction:i,attributes:ec({[eG]:"auto.http.browser.cls",[eJ]:"ui.webvital.cls",[eZ]:t&&t.duration||0,"sentry.pageload.span_id":n}),startTime:r});s&&(s.addEvent("cls",{[eX]:"",[eQ]:e}),s.end(r))}(n,e,t),s())}let s=rI(({metric:t})=>{let r=t.entries[t.entries.length-1];r&&(n=t.value,e=r)},!0);rt(()=>{i()}),setTimeout(()=>{let e=eN();if(!e)return;let n=e.on("startNavigationSpan",()=>{i(),n&&n()}),r=td(),s=r&&tu(r),a=s&&tr(s);a&&"pageload"===a.op&&(t=s.spanContext().spanId)},0)}():rI(({metric:e})=>{let t=e.entries[e.entries.length-1];t&&(r5.cls={value:e.value,unit:""},_=t)},!0);return()=>{n(),r(),i(),s&&s()}}return()=>void 0}({recordClsStandaloneSpans:s||!1});t&&function(){if(r1()&&eh){let e=rA(({metric:e})=>{if(void 0==e.value)return;let t=e.entries.find(t=>t.duration===e.value&&r9[t.name]);if(!t)return;let{interactionId:n}=t,r=r9[t.name],i=r2(eh+t.startTime),s=r2(e.value),a=td(),o=a?tu(a):void 0,l=(null!=n?r7.get(n):void 0)||o,c=l?tr(l).description:eR().getScopeData().transactionName,u=r0({name:V(t.target),transaction:c,attributes:ec({[eG]:"auto.http.browser.inp",[eJ]:`ui.interaction.${r}`,[eZ]:t.duration}),startTime:i});u&&(u.addEvent("inp",{[eX]:"millisecond",[eQ]:e.value}),u.end(i+s))});()=>{e()}}}(),r&&I.PerformanceObserver&&PerformanceObserver.supportedEntryTypes&&PerformanceObserver.supportedEntryTypes.includes("long-animation-frame")?new PerformanceObserver(e=>{let t=td();if(t)for(let n of e.getEntries()){if(!n.scripts[0])continue;let e=r2(eh+n.startTime),{start_timestamp:r,op:i}=tr(t);if("navigation"===i&&r&&e<r)continue;let s=r2(n.duration),a={[eG]:"auto.ui.browser.metrics"},{invoker:o,invokerType:l,sourceURL:c,sourceFunctionName:u,sourceCharPosition:d}=n.scripts[0];a["browser.script.invoker"]=o,a["browser.script.invoker_type"]=l,c&&(a["code.filepath"]=c),u&&(a["code.function"]=u),-1!==d&&(a["browser.script.source_char_position"]=d),rZ(t,e,e+s,{name:"Main UI thread blocked",op:"ui.long-animation-frame",attributes:a})}}).observe({type:"long-animation-frame",buffered:!0}):n&&rR("longtask",({entries:e})=>{let t=td();if(!t)return;let{op:n,start_timestamp:r}=tr(t);for(let i of e){let e=r2(eh+i.startTime),s=r2(i.duration);"navigation"===n&&r&&e<r||rZ(t,e,e+s,{name:"Main UI thread blocked",op:"ui.long-task",attributes:{[eG]:"auto.ui.browser.metrics"}})}}),i&&rR("event",({entries:e})=>{let t=td();if(t){for(let n of e)if("click"===n.name){let e=r2(eh+n.startTime),r=r2(n.duration),i={name:V(n.target),op:`ui.interaction.${n.name}`,startTime:e,attributes:{[eG]:"auto.ui.browser.metrics"}},s=J(n.target);s&&(i.attributes["ui.component_name"]=s),rZ(t,e,e+r,i)}}});let E={name:void 0,source:void 0};function w(e,t){let n="pageload"===t.op,r=a?a(t):t,i=r.attributes||{};t.name!==r.name&&(i[eV]="custom",r.attributes=i),E.name=r.name,E.source=i[eV];let u=it(r,{idleTimeout:o,finalTimeout:l,childSpanTimeout:c,disableAutoFinish:n,beforeSpanEnd:e=>{b(),function(e,t){let n=r1();if(!n||!n.getEntries||!eh)return;let r=r2(eh),i=n.getEntries(),{op:s,start_timestamp:a}=tr(e);if(i.slice(r3).forEach(t=>{let n=r2(t.startTime),i=r2(Math.max(0,t.duration));if("navigation"!==s||!a||!(r+n<a))switch(t.entryType){case"navigation":var o,l,c;o=e,l=t,c=r,["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach(e=>{r4(o,l,e,c)}),r4(o,l,"secureConnection",c,"TLS/SSL"),r4(o,l,"fetch",c,"cache"),r4(o,l,"domainLookup",c,"DNS"),function(e,t,n){let r=n+r2(t.requestStart),i=n+r2(t.responseEnd),s=n+r2(t.responseStart);t.responseEnd&&(rZ(e,r,i,{op:"browser.request",name:t.name,attributes:{[eG]:"auto.ui.browser.metrics"}}),rZ(e,s,i,{op:"browser.response",name:t.name,attributes:{[eG]:"auto.ui.browser.metrics"}}))}(o,l,c);break;case"mark":case"paint":case"measure":{var u=e,d=t,h=n,p=i,f=r;let s=n6(!1),a=f+Math.max(h,r2(s?s.requestStart:0)),o=f+h,l={[eG]:"auto.resource.browser.metrics"};a!==o&&(l["sentry.browser.measure_happened_before_request"]=!0,l["sentry.browser.measure_start_time"]=a),rZ(u,a,o+p,{name:d.name,op:d.entryType,attributes:l});let c=ra(),m=t.startTime<c.firstHiddenTime;"first-paint"===t.name&&m&&(r5.fp={value:t.startTime,unit:"millisecond"}),"first-contentful-paint"===t.name&&m&&(r5.fcp={value:t.startTime,unit:"millisecond"});break}case"resource":!function(e,t,n,r,i,s){if("xmlhttprequest"===t.initiatorType||"fetch"===t.initiatorType)return;let a=nb(n),o={[eG]:"auto.resource.browser.metrics"};r8(o,t,"transferSize","http.response_transfer_size"),r8(o,t,"encodedBodySize","http.response_content_length"),r8(o,t,"decodedBodySize","http.decoded_response_content_length");let l=t.deliveryType;null!=l&&(o["http.response_delivery_type"]=l);let c=t.renderBlockingStatus;c&&(o["resource.render_blocking_status"]=c),a.protocol&&(o["url.scheme"]=a.protocol.split(":").pop()),a.host&&(o["server.address"]=a.host),o["url.same_origin"]=n.includes(I.location.origin);let u=s+r;rZ(e,u,u+i,{name:n.replace(I.location.origin,""),op:t.initiatorType?`resource.${t.initiatorType}`:"resource.other",attributes:o})}(e,t,t.name,n,i,r)}}),r3=Math.max(i.length-1,0),function(e){let t=I.navigator;if(!t)return;let n=t.connection;n&&(n.effectiveType&&e.setAttribute("effectiveConnectionType",n.effectiveType),n.type&&e.setAttribute("connectionType",n.type),rQ(n.rtt)&&(r5["connection.rtt"]={value:n.rtt,unit:"millisecond"})),rQ(t.deviceMemory)&&e.setAttribute("deviceMemory",`${t.deviceMemory} GB`),rQ(t.hardwareConcurrency)&&e.setAttribute("hardwareConcurrency",String(t.hardwareConcurrency))}(e),"pageload"===s){var o;!function(e){let t=n6(!1);if(!t)return;let{responseStart:n,requestStart:r}=t;r<=n&&(e["ttfb.requestTime"]={value:n-r,unit:"millisecond"})}(r5);let n=r5["mark.fid"];n&&r5.fid&&(rZ(e,n.value,n.value+r2(r5.fid.value),{name:"first input delay",op:"ui.action",attributes:{[eG]:"auto.ui.browser.metrics"}}),delete r5["mark.fid"]),"fcp"in r5&&t.recordClsOnPageloadSpan||delete r5.cls,Object.entries(r5).forEach(([e,t])=>{!function(e,t,n,r=td()){let i=r&&tu(r);i&&i.addEvent(e,{[eQ]:t,[eX]:n})}(e,t.value,t.unit)}),e.setAttribute("performance.timeOrigin",r),e.setAttribute("performance.activationStart",n7()),o=e,y&&(y.element&&o.setAttribute("lcp.element",V(y.element)),y.id&&o.setAttribute("lcp.id",y.id),y.url&&o.setAttribute("lcp.url",y.url.trim().slice(0,200)),null!=y.loadTime&&o.setAttribute("lcp.loadTime",y.loadTime),null!=y.renderTime&&o.setAttribute("lcp.renderTime",y.renderTime),o.setAttribute("lcp.size",y.size)),_&&_.sources&&_.sources.forEach((e,t)=>o.setAttribute(`cls.source.${t+1}`,V(e.node)))}y=void 0,_=void 0,r5={}}(e,{recordClsOnPageloadSpan:!s})}});function d(){["interactive","complete"].includes(I.document.readyState)&&e.emit("idleSpanEnableAutoFinish",u)}return n&&I.document&&(I.document.addEventListener("readystatechange",()=>{d()}),d()),u}return{name:"BrowserTracing",afterAllSetup(e){var n,r,s,a;let m,y,_=I.location&&I.location.href;function b(){y&&!tr(y).timestamp&&y.end()}e.on("startNavigationSpan",t=>{eN()===e&&(b(),y=w(e,{op:"navigation",...t}))}),e.on("startPageLoadSpan",(t,n={})=>{if(eN()!==e)return;b();let r=function(e,t){let n=function(e){let t;if(!e)return;let n=e.match(e7);if(n)return"1"===n[3]?t=!0:"0"===n[3]&&(t=!1),{traceId:n[1],parentSampled:t,parentSpanId:n[2]}}(e),r=e2(t);if(!n||!n.traceId)return{traceId:ep(),spanId:ev()};let{traceId:i,parentSpanId:s,parentSampled:a}=n;return{traceId:i,parentSpanId:s,spanId:ev(),sampled:a,dsc:r||{}}}(n.sentryTrace||iy("sentry-trace"),n.baggage||iy("baggage"));eR().setPropagationContext(r),y=w(e,{op:"pageload",...t})}),e.on("spanEnd",e=>{let t=tr(e).op;if(e!==tu(e)||"navigation"!==t&&"pageload"!==t)return;let n=eR(),r=n.getPropagationContext();n.setPropagationContext({...r,sampled:void 0!==r.sampled?r.sampled:ti(e),dsc:r.dsc||tg(e)})}),I.location&&(v&&im(e,{name:I.location.pathname,startTime:eh?eh/1e3:void 0,attributes:{[eV]:"url",[eG]:"auto.pageload.browser"}}),S&&nu(({to:t,from:n})=>{if(void 0===n&&_&&-1!==_.indexOf(t)){_=void 0;return}n!==t&&(_=void 0,ig(e,{name:I.location.pathname,attributes:{[eV]:"url",[eG]:"auto.navigation.browser"}}))})),u&&I&&I.document&&I.document.addEventListener("visibilitychange",()=>{let e=td();if(!e)return;let t=tu(e);if(I.document.hidden&&t){let{op:e,status:n}=tr(t);n||t.setStatus({code:2,message:"cancelled"}),t.setAttribute("sentry.cancellation_reason","document.hidden"),t.end()}}),i&&(n=o,r=l,s=c,a=E,I.document&&addEventListener("click",()=>{let e=td(),t=e&&tu(e);if(!(t&&["navigation","pageload"].includes(tr(t).op)))m&&(m.setAttribute(eK,"interactionInterrupted"),m.end(),m=void 0),a.name&&(m=it({name:a.name,op:"ui.action.click",attributes:{[eV]:a.source||"url"}},{idleTimeout:n,finalTimeout:r,childSpanTimeout:s}))},{once:!1,capture:!0})),t&&function(){let e=({entries:e})=>{let t=td(),n=t&&tu(t);e.forEach(e=>{if(!("duration"in e)||!n)return;let t=e.interactionId;if(null!=t&&!r7.has(t)){if(r6.length>10){let e=r6.shift();r7.delete(e)}r6.push(t),r7.set(t,n)}})};rR("event",e),rR("first-input",e)}(),function(e,t){let{traceFetch:n,traceXHR:r,trackFetchStreamPerformance:i,shouldCreateSpanForRequest:s,enableHTTPTimings:a,tracePropagationTargets:o}={traceFetch:ic.traceFetch,traceXHR:ic.traceXHR,trackFetchStreamPerformance:ic.trackFetchStreamPerformance,...t},l="function"==typeof s?s:e=>!0,c=e=>(function(e,t){let n=I.location&&I.location.href;if(n){let r,i;try{r=new URL(e,n),i=new URL(n).origin}catch(e){return!1}let s=r.origin===i;return t?et(r.toString(),t)||s&&et(r.pathname,t):s}{let n=!!e.match(/^\/(?!\/)/);return t?et(e,t):n}})(e,o),u={};n&&(e.addEventProcessor(e=>("transaction"===e.type&&e.spans&&e.spans.forEach(e=>{if("http.client"===e.op){let t=il.get(e.span_id);t&&(e.timestamp=t/1e3,il.delete(e.span_id))}}),e)),i&&function(e){let t="fetch-body-resolved";nt(t,e),nn(t,()=>nf(ng))}(e=>{if(e.response){let t=io.get(e.response);t&&e.endTimestamp&&il.set(t,e.endTimestamp)}}),np(e=>{let t=function(e,t,n,r,i="auto.http.browser"){if(!e.fetchData)return;let s=e4()&&t(e.fetchData.url);if(e.endTimestamp&&s){let t=e.fetchData.__span;if(!t)return;let n=r[t];n&&(function(e,t){if(t.response){e6(e,t.response.status);let n=t.response&&t.response.headers&&t.response.headers.get("content-length");if(n){let t=parseInt(n);t>0&&e.setAttribute("http.response_content_length",t)}}else t.error&&e.setStatus({code:2,message:"internal_error"});e.end()}(n,e),delete r[t]);return}let{method:a,url:o}=e.fetchData,l=function(e){try{return new URL(e).href}catch(e){return}}(o),c=l?nb(l).host:void 0,u=!!td(),d=s&&u?rG({name:`${a} ${o}`,attributes:{url:o,type:"fetch","http.method":a,"http.url":l,"server.address":c,[eG]:i,[eJ]:"http.client"}}):new rB;if(e.fetchData.__span=d.spanContext().spanId,r[d.spanContext().spanId]=d,n(e.fetchData.url)){let t=e.args[0],n=e.args[1]||{},r=function(e,t,n){var r,i;let s=is({span:n}),a=s["sentry-trace"],o=s.baggage;if(!a)return;let l=t.headers||((r=e,"undefined"!=typeof Request&&H(r,Request))?e.headers:void 0);if(!l)return{...s};if(i=l,"undefined"!=typeof Headers&&H(i,Headers)){let e=new Headers(l);if(e.set("sentry-trace",a),o){let t=e.get("baggage");if(t){let n=ia(t);e.set("baggage",n?`${n},${o}`:o)}else e.set("baggage",o)}return e}if(Array.isArray(l)){let e=[...l.filter(e=>!(Array.isArray(e)&&"sentry-trace"===e[0])).map(e=>{if(!Array.isArray(e)||"baggage"!==e[0]||"string"!=typeof e[1])return e;{let[t,n,...r]=e;return[t,ia(n),...r]}}),["sentry-trace",a]];return o&&e.push(["baggage",o]),e}{let e="baggage"in l?l.baggage:void 0,t=[];return Array.isArray(e)?t=e.map(e=>"string"==typeof e?ia(e):e).filter(e=>""===e):e&&t.push(ia(e)),o&&t.push(o),{...l,"sentry-trace":a,baggage:t.length>0?t.join(","):void 0}}}(t,n,e4()&&u?d:void 0);r&&(e.args[1]=n,n.headers=r)}return d}(e,l,c,u);if(e.response&&e.fetchData.__span&&io.set(e.response,e.fetchData.__span),t){let n=ih(e.fetchData.url),r=n?nb(n).host:void 0;t.setAttributes({"http.url":n,"server.address":r})}a&&t&&iu(t)})),r&&nl(e=>{let t=function(e,t,n,r){let i=e.xhr,s=i&&i[no];if(!i||i.__sentry_own_request__||!s)return;let a=e4()&&t(s.url);if(e.endTimestamp&&a){let e=i.__sentry_xhr_span_id__;if(!e)return;let t=r[e];t&&void 0!==s.status_code&&(e6(t,s.status_code),t.end(),delete r[e]);return}let o=ih(s.url),l=o?nb(o).host:void 0,c=!!td(),u=a&&c?rG({name:`${s.method} ${s.url}`,attributes:{type:"xhr","http.method":s.method,"http.url":o,url:s.url,"server.address":l,[eG]:"auto.http.browser",[eJ]:"http.client"}}):new rB;return i.__sentry_xhr_span_id__=u.spanContext().spanId,r[i.__sentry_xhr_span_id__]=u,n(s.url)&&function(e,t){let{"sentry-trace":n,baggage:r}=is({span:t});n&&function(e,t,n){try{e.setRequestHeader("sentry-trace",t),n&&e.setRequestHeader("baggage",n)}catch(e){}}(e,n,r)}(i,e4()&&c?u:void 0),u}(e,l,c,u);a&&t&&iu(t)})}(e,{traceFetch:d,traceXHR:h,trackFetchStreamPerformance:p,tracePropagationTargets:e.getOptions().tracePropagationTargets,shouldCreateSpanForRequest:f,enableHTTPTimings:g})}}})({...e,instrumentNavigation:!1,instrumentPageLoad:!1}),{instrumentPageLoad:n=!0,instrumentNavigation:r=!0}=e;return{...t,afterAllSetup(e){r&&function(e){if(I.document.getElementById("__NEXT_DATA__"))ib.events.on("routeChangeStart",t=>{let n,r,i=t.split(/[?#]/,1)[0],s=function(e){let t=(I.__BUILD_MANIFEST||{}).sortedPages;if(t)return t.find(t=>{let n=function(e){let t=e.split("/"),n="";T([t,"access",e=>e[t.length-1],"optionalAccess",e=>e.match,"call",e=>e(/^\[\[\.\.\..+\]\]$/)])&&(t.pop(),n="(?:/(.+?))?");let r=t.map(e=>e.replace(/^\[\.\.\..+\]$/,"(.+?)").replace(/^\[.*\]$/,"([^/]+?)")).join("/");return RegExp(`^${r}${n}(?:/)?$`)}(t);return e.match(n)})}(i);s?(n=s,r="route"):(n=i,r="url"),ig(e,{name:n,attributes:{[eJ]:"navigation",[eG]:"auto.navigation.nextjs.pages_router_instrumentation",[eV]:r}})});else{let t;I.addEventListener("popstate",()=>{t&&t.isRecording()?(t.updateName(I.location.pathname),t.setAttribute(eV,"url")):t=ig(e,{name:I.location.pathname,attributes:{[eJ]:"navigation",[eG]:"auto.navigation.nextjs.app_router_instrumentation",[eV]:"url","navigation.type":"browser.popstate"}})});let n=!1,r=0,i=setInterval(()=>{var s,a;r++;let o=(s=T([I,"optionalAccess",e=>e.next,"optionalAccess",e=>e.router]),a=()=>T([I,"optionalAccess",e=>e.nd,"optionalAccess",e=>e.router]),null!=s?s:a());n||r>500?clearInterval(i):o&&(clearInterval(i),n=!0,["back","forward","push","replace"].forEach(n=>{T([o,"optionalAccess",e=>e[n]])&&(o[n]=new Proxy(o[n],{apply(r,i,s){let a=ig(e,{name:i_,attributes:{[eJ]:"navigation",[eG]:"auto.navigation.nextjs.app_router_instrumentation",[eV]:"url"}});return t=a,"push"===n?(T([a,"optionalAccess",e=>e.updateName,"call",e=>e(iv(s[0]))]),T([a,"optionalAccess",e=>e.setAttribute,"call",e=>e(eV,"url")]),T([a,"optionalAccess",e=>e.setAttribute,"call",e=>e("navigation.type","router.push")])):"replace"===n?(T([a,"optionalAccess",e=>e.updateName,"call",e=>e(iv(s[0]))]),T([a,"optionalAccess",e=>e.setAttribute,"call",e=>e(eV,"url")]),T([a,"optionalAccess",e=>e.setAttribute,"call",e=>e("navigation.type","router.replace")])):"back"===n?T([a,"optionalAccess",e=>e.setAttribute,"call",e=>e("navigation.type","router.back")]):"forward"===n&&T([a,"optionalAccess",e=>e.setAttribute,"call",e=>e("navigation.type","router.forward")]),r.apply(i,s)}}))}))},20)}}(e),t.afterAllSetup(e),n&&function(e){if(I.document.getElementById("__NEXT_DATA__")){let{route:t,params:n,sentryTrace:r,baggage:i}=function(){let e,t=I.document.getElementById("__NEXT_DATA__");if(t&&t.innerHTML)try{e=JSON.parse(t.innerHTML)}catch(e){}if(!e)return{};let n={},{page:r,query:i,props:s}=e;return n.route=r,n.params=i,s&&s.pageProps&&(n.sentryTrace=s.pageProps._sentryTraceData,n.baggage=s.pageProps._sentryBaggage),n}(),s=e3(i),a=t||I.location.pathname;s&&s["sentry-transaction"]&&"/_error"===a&&(a=(a=s["sentry-transaction"]).replace(/^(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS|TRACE|CONNECT)\s+/i,"")),im(e,{name:a,startTime:eh?eh/1e3:void 0,attributes:{[eJ]:"pageload",[eG]:"auto.pageload.nextjs.pages_router_instrumentation",[eV]:t?"route":"url",...n&&e.getOptions().sendDefaultPii&&{...n}}},{sentryTrace:r,baggage:i})}else im(e,{name:I.location.pathname,startTime:eh?eh/1e3:void 0,attributes:{[eJ]:"pageload",[eG]:"auto.pageload.nextjs.app_router_instrumentation",[eV]:"url"}})}(e)}}}());let n=I._sentryRewriteFramesAssetPrefixPath||"";return t.push((({assetPrefixPath:e})=>({...((e={})=>{let t=e.root,n=e.prefix||"app:///",r="window"in I&&void 0!==I.window,i=e.iteratee||function({isBrowser:e,root:t,prefix:n}){return r=>{if(!r.filename)return r;let i=/^[a-zA-Z]:\\/.test(r.filename)||r.filename.includes("\\")&&!r.filename.includes("/"),s=/^\//.test(r.filename);if(e){if(t){let e=r.filename;0===e.indexOf(t)&&(r.filename=e.replace(t,n))}}else if(i||s){let e=i?r.filename.replace(/^[a-zA-Z]:/,"").replace(/\\/g,"/"):r.filename,s=t?function(e,t){e=iw(e).slice(1),t=iw(t).slice(1);let n=ik(e.split("/")),r=ik(t.split("/")),i=Math.min(n.length,r.length),s=i;for(let e=0;e<i;e++)if(n[e]!==r[e]){s=e;break}let a=[];for(let e=s;e<n.length;e++)a.push("..");return(a=a.concat(r.slice(s))).join("/")}(t,e):function(e){let t=e.length>1024?`<truncated>${e.slice(-1024)}`:e,n=iE.exec(t);return n?n.slice(1):[]}(e)[2]||"";r.filename=`${n}${s}`}return r}}({isBrowser:r,root:t,prefix:n});return{name:"RewriteFrames",processEvent(e){let t=e;return e.exception&&Array.isArray(e.exception.values)&&(t=function(e){try{return{...e,exception:{...e.exception,values:e.exception.values.map(e=>{var t;return{...e,...e.stacktrace&&{stacktrace:{...t=e.stacktrace,frames:t&&t.frames&&t.frames.map(e=>i(e))}}}})}}}catch(t){return e}}(t)),t}}})({iteratee:t=>{try{let{origin:n}=new URL(t.filename);t.filename=T([t,"access",e=>e.filename,"optionalAccess",e=>e.replace,"call",e=>e(n,"app://"),"access",e=>e.replace,"call",t=>t(e,"")])}catch(e){}return t.filename&&t.filename.startsWith("app:///_next")&&(t.filename=decodeURI(t.filename)),t.filename&&t.filename.match(/^app:\/\/\/_next\/static\/chunks\/(main-|main-app-|polyfills-|webpack-|framework-|framework\.)[0-9a-f]+\.js$/)&&(t.in_app=!1),t}}),name:"NextjsClientStackFrameNormalization"}))({assetPrefixPath:n})),t}(e),...e};!function(e){let t=iT.env._sentryRewritesTunnelPath||I._sentryRewritesTunnelPath;if(t&&e.dsn){let n=tU(e.dsn);if(!n)return;let r=n.host.match(/^o(\d+)\.ingest(?:\.([a-z]{2}))?\.sentry\.io$/);if(r){let i=r[1],s=r[2],a=`${t}?o=${i}&p=${n.projectId}`;s&&(a+=`&r=${s}`),e.tunnel=a}}}(t),M(t,"nextjs",["nextjs","react"]),function(e){let t={...e};M(t,"react"),tE("react",{version:n3.version}),function(e={}){var t,n;let r=function(e={}){let t={defaultIntegrations:nX(e),release:"string"==typeof __SENTRY_RELEASE__?__SENTRY_RELEASE__:I.SENTRY_RELEASE&&I.SENTRY_RELEASE.id?I.SENTRY_RELEASE.id:void 0,autoSessionTracking:!0,sendClientReports:!0};return null==e.defaultIntegrations&&delete e.defaultIntegrations,{...t,...e}}(e);if(!r.skipBrowserExtensionCheck&&function(){let e=void 0!==I.window&&I;if(!e)return!1;let t=e.chrome?"chrome":"browser",n=e[t],r=n&&n.runtime&&n.runtime.id,i=I.location&&I.location.href||"",s=!!r&&I===I.top&&["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"].some(e=>i.startsWith(`${e}//`)),a=void 0!==e.nw;return!!r&&!s&&!a}())return X(()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")});let i={...r,stackParser:Array.isArray(t=r.stackParser||nW)?eU(...t):t,integrations:function(e){let t,n=e.defaultIntegrations||[],r=e.integrations;if(n.forEach(e=>{e.isDefaultInstance=!0}),Array.isArray(r))t=[...n,...r];else if("function"==typeof r){let e=r(n);t=Array.isArray(e)?e:[e]}else t=n;let i=function(e){let t={};return e.forEach(e=>{let{name:n}=e,r=t[n];r&&!r.isDefaultInstance&&e.isDefaultInstance||(t[n]=e)}),Object.values(t)}(t),s=i.findIndex(e=>"Debug"===e.name);if(s>-1){let[e]=i.splice(s,1);i.push(e)}return i}(r),transport:r.transport||nK};!0===i.debug&&X(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")}),eR().update(i.initialScope);let s=new t7(i);n=s,eR().setClient(n),s.init()}(t)}(t);let n=e=>"transaction"===e.type&&"/404"===e.transaction?null:e;n.id="NextClient404Filter",tw(n);let r=e=>"transaction"===e.type&&e.transaction===i_?null:e;r.id="IncompleteTransactionFilter",tw(r);let i=(e,t)=>{var n;return N(n=T([t,"optionalAccess",e=>e.originalException]))&&"string"==typeof n.digest&&n.digest.startsWith("NEXT_REDIRECT;")?null:e};i.id="NextRedirectErrorFilter",tw(i)}({dsn:oy.env.SENTRY_DSN,tracesSampleRate:1,debug:!1,replaysOnErrorSampleRate:1,replaysSessionSampleRate:.1,integrations:[new om({maskAllText:!0,blockAllMedia:!0})],beforeSend:(e,t)=>e,initialScope:{tags:{component:"client"}},environment:"production",release:oy.env.VERCEL_GIT_COMMIT_SHA||"development",beforeSendTransaction:e=>(e.tags={...e.tags,section:"client"},e)})},4089:(e,t,n)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/_app",function(){return n(6746)}])},4998:(e,t,n)=>{e.exports=n(6820)}},e=>{var t=t=>e(e.s=t);e.O(0,[593,792],()=>(t(1824),t(4089),t(6820))),_N_E=e.O()}]);
//# sourceMappingURL=_app-6fe42823cf50b1ba.js.map