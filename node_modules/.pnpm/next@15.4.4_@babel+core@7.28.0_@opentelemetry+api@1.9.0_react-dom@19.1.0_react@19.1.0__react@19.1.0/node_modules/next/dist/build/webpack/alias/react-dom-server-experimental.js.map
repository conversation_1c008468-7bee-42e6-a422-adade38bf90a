{"version": 3, "sources": ["../../../../src/build/webpack/alias/react-dom-server-experimental.js"], "sourcesContent": ["const ERROR_MESSAGE =\n  'Internal Error: do not use legacy react-dom/server APIs. If you encountered this error, please open an issue on the Next.js repo.'\n\nfunction error() {\n  throw new Error(ERROR_MESSAGE)\n}\n\nvar b\nif (process.env.NEXT_RUNTIME === 'nodejs') {\n  if (process.env.NODE_ENV === 'production') {\n    b = require('next/dist/compiled/react-dom-experimental/cjs/react-dom-server.node.production.js')\n  } else {\n    b = require('next/dist/compiled/react-dom-experimental/cjs/react-dom-server.node.development.js')\n  }\n} else {\n  if (process.env.NODE_ENV === 'production') {\n    b = require('next/dist/compiled/react-dom-experimental/cjs/react-dom-server.edge.production.js')\n  } else {\n    b = require('next/dist/compiled/react-dom-experimental/cjs/react-dom-server.edge.development.js')\n  }\n}\n\nexports.version = b.version\nexports.renderToReadableStream = b.renderToReadableStream\nexports.renderToString = error\nexports.renderToStaticMarkup = error\nif (b.resume) {\n  exports.resume = b.resume\n}\n"], "names": ["ERROR_MESSAGE", "error", "Error", "b", "process", "env", "NEXT_RUNTIME", "NODE_ENV", "require", "exports", "version", "renderToReadableStream", "renderToString", "renderToStaticMarkup", "resume"], "mappings": ";AAAA,MAAMA,gBACJ;AAEF,SAASC;IACP,MAAM,qBAAwB,CAAxB,IAAIC,MAAMF,gBAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAuB;AAC/B;AAEA,IAAIG;AACJ,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,UAAU;IACzC,IAAIF,QAAQC,GAAG,CAACE,QAAQ,KAAK,cAAc;QACzCJ,IAAIK,QAAQ;IACd,OAAO;QACLL,IAAIK,QAAQ;IACd;AACF,OAAO;IACL,IAAIJ,QAAQC,GAAG,CAACE,QAAQ,KAAK,cAAc;QACzCJ,IAAIK,QAAQ;IACd,OAAO;QACLL,IAAIK,QAAQ;IACd;AACF;AAEAC,QAAQC,OAAO,GAAGP,EAAEO,OAAO;AAC3BD,QAAQE,sBAAsB,GAAGR,EAAEQ,sBAAsB;AACzDF,QAAQG,cAAc,GAAGX;AACzBQ,QAAQI,oBAAoB,GAAGZ;AAC/B,IAAIE,EAAEW,MAAM,EAAE;IACZL,QAAQK,MAAM,GAAGX,EAAEW,MAAM;AAC3B", "ignoreList": [0]}