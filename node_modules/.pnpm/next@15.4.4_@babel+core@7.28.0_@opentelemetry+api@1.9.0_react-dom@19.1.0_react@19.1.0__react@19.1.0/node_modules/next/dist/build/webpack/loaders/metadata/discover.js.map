{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/metadata/discover.ts"], "sourcesContent": ["import type {\n  CollectingMetadata,\n  PossibleStaticMetadataFileNameConvention,\n} from './types'\nimport path from 'path'\nimport { stringify } from 'querystring'\nimport { STATIC_METADATA_IMAGES } from '../../../../lib/metadata/is-metadata-route'\nimport { WEBPACK_RESOURCE_QUERIES } from '../../../../lib/constants'\nimport type { MetadataResolver } from '../next-app-loader'\nimport type { PageExtensions } from '../../../page-extensions-type'\n\nconst METADATA_TYPE = 'metadata'\nconst NUMERIC_SUFFIX_ARRAY = Array(10).fill(0)\n\n// Produce all compositions with filename (icon, apple-icon, etc.) with extensions (png, jpg, etc.)\nasync function enumMetadataFiles(\n  dir: string,\n  filename: string,\n  extensions: readonly string[],\n  {\n    metadataResolver,\n    // When set to true, possible filename without extension could: icon, icon0, ..., icon9\n    numericSuffix,\n  }: {\n    metadataResolver: MetadataResolver\n    numericSuffix: boolean\n  }\n): Promise<string[]> {\n  const collectedFiles: string[] = []\n\n  // Collect <filename>.<ext>, <filename>[].<ext>\n  const possibleFileNames = [filename].concat(\n    numericSuffix\n      ? NUMERIC_SUFFIX_ARRAY.map((_, index) => filename + index)\n      : []\n  )\n  for (const name of possibleFileNames) {\n    const resolved = await metadataResolver(dir, name, extensions)\n    if (resolved) {\n      collectedFiles.push(resolved)\n    }\n  }\n\n  return collectedFiles\n}\n\nexport async function createStaticMetadataFromRoute(\n  resolvedDir: string,\n  {\n    segment,\n    metadataResolver,\n    isRootLayoutOrRootPage,\n    pageExtensions,\n    basePath,\n  }: {\n    segment: string\n    metadataResolver: MetadataResolver\n    isRootLayoutOrRootPage: boolean\n    pageExtensions: PageExtensions\n    basePath: string\n  }\n) {\n  let hasStaticMetadataFiles = false\n  const staticImagesMetadata: CollectingMetadata = {\n    icon: [],\n    apple: [],\n    twitter: [],\n    openGraph: [],\n    manifest: undefined,\n  }\n\n  async function collectIconModuleIfExists(\n    type: PossibleStaticMetadataFileNameConvention\n  ) {\n    if (type === 'manifest') {\n      const staticManifestExtension = ['webmanifest', 'json']\n      const manifestFile = await enumMetadataFiles(\n        resolvedDir,\n        'manifest',\n        staticManifestExtension.concat(pageExtensions),\n        { metadataResolver, numericSuffix: false }\n      )\n      if (manifestFile.length > 0) {\n        hasStaticMetadataFiles = true\n        const { name, ext } = path.parse(manifestFile[0])\n        const extension = staticManifestExtension.includes(ext.slice(1))\n          ? ext.slice(1)\n          : 'webmanifest'\n        staticImagesMetadata.manifest = JSON.stringify(\n          `${basePath}/${name}.${extension}`\n        )\n      }\n      return\n    }\n\n    const isFavicon = type === 'favicon'\n    const resolvedMetadataFiles = await enumMetadataFiles(\n      resolvedDir,\n      STATIC_METADATA_IMAGES[type].filename,\n      [\n        ...STATIC_METADATA_IMAGES[type].extensions,\n        ...(isFavicon ? [] : pageExtensions),\n      ],\n      { metadataResolver, numericSuffix: !isFavicon }\n    )\n    resolvedMetadataFiles\n      .sort((a, b) => a.localeCompare(b))\n      .forEach((filepath) => {\n        const imageModuleImportSource = `next-metadata-image-loader?${stringify(\n          {\n            type,\n            segment,\n            basePath,\n            pageExtensions,\n          }\n          // WEBPACK_RESOURCE_QUERIES.metadata query here only for filtering out applying to image loader\n        )}!${filepath}?${WEBPACK_RESOURCE_QUERIES.metadata}`\n\n        const imageModule = `(async (props) => (await import(/* webpackMode: \"eager\" */ ${JSON.stringify(\n          imageModuleImportSource\n        )})).default(props))`\n        hasStaticMetadataFiles = true\n        if (type === 'favicon') {\n          staticImagesMetadata.icon.unshift(imageModule)\n        } else {\n          staticImagesMetadata[type].push(imageModule)\n        }\n      })\n  }\n\n  // Intentionally make these serial to reuse directory access cache.\n  await collectIconModuleIfExists('icon')\n  await collectIconModuleIfExists('apple')\n  await collectIconModuleIfExists('openGraph')\n  await collectIconModuleIfExists('twitter')\n  if (isRootLayoutOrRootPage) {\n    await collectIconModuleIfExists('favicon')\n    await collectIconModuleIfExists('manifest')\n  }\n\n  return hasStaticMetadataFiles ? staticImagesMetadata : null\n}\n\nexport function createMetadataExportsCode(\n  metadata: Awaited<ReturnType<typeof createStaticMetadataFromRoute>>\n) {\n  return metadata\n    ? `${METADATA_TYPE}: {\n    icon: [${metadata.icon.join(',')}],\n    apple: [${metadata.apple.join(',')}],\n    openGraph: [${metadata.openGraph.join(',')}],\n    twitter: [${metadata.twitter.join(',')}],\n    manifest: ${metadata.manifest ? metadata.manifest : 'undefined'}\n  }`\n    : ''\n}\n"], "names": ["createMetadataExportsCode", "createStaticMetadataFromRoute", "METADATA_TYPE", "NUMERIC_SUFFIX_ARRAY", "Array", "fill", "enumMetadataFiles", "dir", "filename", "extensions", "metadataResolver", "numericSuffix", "collectedFiles", "possibleFileNames", "concat", "map", "_", "index", "name", "resolved", "push", "resolvedDir", "segment", "isRootLayoutOrRootPage", "pageExtensions", "basePath", "hasStaticMetadataFiles", "staticImagesMetadata", "icon", "apple", "twitter", "openGraph", "manifest", "undefined", "collectIconModuleIfExists", "type", "staticManifestExtension", "manifestFile", "length", "ext", "path", "parse", "extension", "includes", "slice", "JSON", "stringify", "isFavicon", "resolvedMetadataFiles", "STATIC_METADATA_IMAGES", "sort", "a", "b", "localeCompare", "for<PERSON>ach", "filepath", "imageModuleImportSource", "WEBPACK_RESOURCE_QUERIES", "metadata", "imageModule", "unshift", "join"], "mappings": ";;;;;;;;;;;;;;;IA+Ig<PERSON>,yBAAyB;eAAzBA;;IAjGMC,6BAA6B;eAA7BA;;;6DA1CL;6BACS;iCACa;2BACE;;;;;;AAIzC,MAAMC,gBAAgB;AACtB,MAAMC,uBAAuBC,MAAM,IAAIC,IAAI,CAAC;AAE5C,mGAAmG;AACnG,eAAeC,kBACbC,GAAW,EACXC,QAAgB,EAChBC,UAA6B,EAC7B,EACEC,gBAAgB,EAChB,uFAAuF;AACvFC,aAAa,EAId;IAED,MAAMC,iBAA2B,EAAE;IAEnC,+CAA+C;IAC/C,MAAMC,oBAAoB;QAACL;KAAS,CAACM,MAAM,CACzCH,gBACIR,qBAAqBY,GAAG,CAAC,CAACC,GAAGC,QAAUT,WAAWS,SAClD,EAAE;IAER,KAAK,MAAMC,QAAQL,kBAAmB;QACpC,MAAMM,WAAW,MAAMT,iBAAiBH,KAAKW,MAAMT;QACnD,IAAIU,UAAU;YACZP,eAAeQ,IAAI,CAACD;QACtB;IACF;IAEA,OAAOP;AACT;AAEO,eAAeX,8BACpBoB,WAAmB,EACnB,EACEC,OAAO,EACPZ,gBAAgB,EAChBa,sBAAsB,EACtBC,cAAc,EACdC,QAAQ,EAOT;IAED,IAAIC,yBAAyB;IAC7B,MAAMC,uBAA2C;QAC/CC,MAAM,EAAE;QACRC,OAAO,EAAE;QACTC,SAAS,EAAE;QACXC,WAAW,EAAE;QACbC,UAAUC;IACZ;IAEA,eAAeC,0BACbC,IAA8C;QAE9C,IAAIA,SAAS,YAAY;YACvB,MAAMC,0BAA0B;gBAAC;gBAAe;aAAO;YACvD,MAAMC,eAAe,MAAM/B,kBACzBe,aACA,YACAe,wBAAwBtB,MAAM,CAACU,iBAC/B;gBAAEd;gBAAkBC,eAAe;YAAM;YAE3C,IAAI0B,aAAaC,MAAM,GAAG,GAAG;gBAC3BZ,yBAAyB;gBACzB,MAAM,EAAER,IAAI,EAAEqB,GAAG,EAAE,GAAGC,aAAI,CAACC,KAAK,CAACJ,YAAY,CAAC,EAAE;gBAChD,MAAMK,YAAYN,wBAAwBO,QAAQ,CAACJ,IAAIK,KAAK,CAAC,MACzDL,IAAIK,KAAK,CAAC,KACV;gBACJjB,qBAAqBK,QAAQ,GAAGa,KAAKC,SAAS,CAC5C,GAAGrB,SAAS,CAAC,EAAEP,KAAK,CAAC,EAAEwB,WAAW;YAEtC;YACA;QACF;QAEA,MAAMK,YAAYZ,SAAS;QAC3B,MAAMa,wBAAwB,MAAM1C,kBAClCe,aACA4B,uCAAsB,CAACd,KAAK,CAAC3B,QAAQ,EACrC;eACKyC,uCAAsB,CAACd,KAAK,CAAC1B,UAAU;eACtCsC,YAAY,EAAE,GAAGvB;SACtB,EACD;YAAEd;YAAkBC,eAAe,CAACoC;QAAU;QAEhDC,sBACGE,IAAI,CAAC,CAACC,GAAGC,IAAMD,EAAEE,aAAa,CAACD,IAC/BE,OAAO,CAAC,CAACC;YACR,MAAMC,0BAA0B,CAAC,2BAA2B,EAAEV,IAAAA,sBAAS,EACrE;gBACEX;gBACAb;gBACAG;gBACAD;YACF,GAEA,CAAC,EAAE+B,SAAS,CAAC,EAAEE,mCAAwB,CAACC,QAAQ,EAAE;YAEpD,MAAMC,cAAc,CAAC,2DAA2D,EAAEd,KAAKC,SAAS,CAC9FU,yBACA,kBAAkB,CAAC;YACrB9B,yBAAyB;YACzB,IAAIS,SAAS,WAAW;gBACtBR,qBAAqBC,IAAI,CAACgC,OAAO,CAACD;YACpC,OAAO;gBACLhC,oBAAoB,CAACQ,KAAK,CAACf,IAAI,CAACuC;YAClC;QACF;IACJ;IAEA,mEAAmE;IACnE,MAAMzB,0BAA0B;IAChC,MAAMA,0BAA0B;IAChC,MAAMA,0BAA0B;IAChC,MAAMA,0BAA0B;IAChC,IAAIX,wBAAwB;QAC1B,MAAMW,0BAA0B;QAChC,MAAMA,0BAA0B;IAClC;IAEA,OAAOR,yBAAyBC,uBAAuB;AACzD;AAEO,SAAS3B,0BACd0D,QAAmE;IAEnE,OAAOA,WACH,GAAGxD,cAAc;WACZ,EAAEwD,SAAS9B,IAAI,CAACiC,IAAI,CAAC,KAAK;YACzB,EAAEH,SAAS7B,KAAK,CAACgC,IAAI,CAAC,KAAK;gBACvB,EAAEH,SAAS3B,SAAS,CAAC8B,IAAI,CAAC,KAAK;cACjC,EAAEH,SAAS5B,OAAO,CAAC+B,IAAI,CAAC,KAAK;cAC7B,EAAEH,SAAS1B,QAAQ,GAAG0B,SAAS1B,QAAQ,GAAG,YAAY;GACjE,CAAC,GACE;AACN", "ignoreList": [0]}