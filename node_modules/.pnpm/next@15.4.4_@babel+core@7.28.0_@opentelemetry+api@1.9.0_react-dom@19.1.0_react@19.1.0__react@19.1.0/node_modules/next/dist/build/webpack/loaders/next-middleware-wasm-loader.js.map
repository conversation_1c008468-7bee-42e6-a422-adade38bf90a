{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-middleware-wasm-loader.ts"], "sourcesContent": ["import { getModuleBuildInfo } from './get-module-build-info'\nimport crypto from 'crypto'\n\nfunction sha1(source: string | Buffer) {\n  return crypto.createHash('sha1').update(source).digest('hex')\n}\n\nexport default function MiddlewareWasmLoader(this: any, source: Buffer) {\n  const name = `wasm_${sha1(source)}`\n  const filePath = `edge-chunks/${name}.wasm`\n  const buildInfo = getModuleBuildInfo(this._module)\n  buildInfo.nextWasmMiddlewareBinding = { filePath: `server/${filePath}`, name }\n  this.emitFile(`/${filePath}`, source, null)\n  return `module.exports = ${name};`\n}\n\nexport const raw = true\n"], "names": ["MiddlewareWasmLoader", "raw", "sha1", "source", "crypto", "createHash", "update", "digest", "name", "filePath", "buildInfo", "getModuleBuildInfo", "_module", "nextWasmMiddlewareBinding", "emitFile"], "mappings": ";;;;;;;;;;;;;;;IAOA,OAOC;eAPuBA;;IASXC,GAAG;eAAHA;;;oCAhBsB;+DAChB;;;;;;AAEnB,SAASC,KAAKC,MAAuB;IACnC,OAAOC,eAAM,CAACC,UAAU,CAAC,QAAQC,MAAM,CAACH,QAAQI,MAAM,CAAC;AACzD;AAEe,SAASP,qBAAgCG,MAAc;IACpE,MAAMK,OAAO,CAAC,KAAK,EAAEN,KAAKC,SAAS;IACnC,MAAMM,WAAW,CAAC,YAAY,EAAED,KAAK,KAAK,CAAC;IAC3C,MAAME,YAAYC,IAAAA,sCAAkB,EAAC,IAAI,CAACC,OAAO;IACjDF,UAAUG,yBAAyB,GAAG;QAAEJ,UAAU,CAAC,OAAO,EAAEA,UAAU;QAAED;IAAK;IAC7E,IAAI,CAACM,QAAQ,CAAC,CAAC,CAAC,EAAEL,UAAU,EAAEN,QAAQ;IACtC,OAAO,CAAC,iBAAiB,EAAEK,KAAK,CAAC,CAAC;AACpC;AAEO,MAAMP,MAAM", "ignoreList": [0]}