{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/resolve-url-loader/lib/join-function.ts"], "sourcesContent": ["// TODO: Remove use of `any` type. Fix no-use-before-define violations.\n/* eslint-disable @typescript-eslint/no-use-before-define */\n/*\nThe MIT License (MIT)\n\nCopyright (c) 2016 Ben Holloway\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\nimport path from 'path'\nimport fs from 'fs'\n\nconst compose =\n  (f: any, g: any) =>\n  (...args: any[]) =>\n    f(g(...args))\n\nconst simpleJoin = compose(path.normalize, path.join)\n\n/**\n * The default join function iterates over possible base paths until a suitable join is found.\n *\n * The first base path is used as fallback for the case where none of the base paths can locate the actual file.\n *\n * @type {function}\n */\nexport const defaultJoin = createJoinForPredicate(function predicate(\n  _: any,\n  uri: any,\n  base: any,\n  i: any,\n  next: any\n) {\n  const absolute = simpleJoin(base, uri)\n  return fs.existsSync(absolute) ? absolute : next(i === 0 ? absolute : null)\n}, 'defaultJoin')\n\nfunction* createIterator(arr: any) {\n  for (const i of arr) {\n    yield i\n  }\n}\n\n/**\n * Define a join function by a predicate that tests possible base paths from an iterator.\n *\n * The `predicate` is of the form:\n *\n * ```\n * function(filename, uri, base, i, next):string|null\n * ```\n *\n * Given the uri and base it should either return:\n * - an absolute path success\n * - a call to `next(null)` as failure\n * - a call to `next(absolute)` where absolute is placeholder and the iterator continues\n *\n * The value given to `next(...)` is only used if success does not eventually occur.\n *\n * The `file` value is typically unused but useful if you would like to differentiate behaviour.\n *\n * You can write a much simpler function than this if you have specific requirements.\n *\n */\nfunction createJoinForPredicate(\n  /** predicate A function that tests values */\n  predicate: any,\n  /** Optional name for the resulting join function */\n  name: string\n) {\n  /**\n   * A factory for a join function with logging.\n   */\n  function join(\n    /** The current file being processed */\n    filename: string,\n    /** An options hash */\n    options: { debug?: any | boolean; root: string }\n  ) {\n    const log = createDebugLogger(options.debug)\n\n    /**\n     * Join function proper.\n     *\n     * For absolute uri only `uri` will be provided. In this case we substitute any `root` given in options.\n     *\n     * Returns Just the uri where base is empty or the uri appended to the base\n     */\n    return function joinProper(\n      /** A uri path, relative or absolute */\n      uri: string,\n      /** Optional absolute base path or iterator thereof */\n      baseOrIteratorOrAbsent: any\n    ) {\n      const iterator =\n        (typeof baseOrIteratorOrAbsent === 'undefined' &&\n          createIterator([options.root])) ||\n        (typeof baseOrIteratorOrAbsent === 'string' &&\n          createIterator([baseOrIteratorOrAbsent])) ||\n        baseOrIteratorOrAbsent\n\n      const result = runIterator([])\n      log(createJoinMsg, [filename, uri, result, result.isFound])\n\n      return typeof result.absolute === 'string' ? result.absolute : uri\n\n      function runIterator(accumulator: any) {\n        const nextItem = iterator.next()\n        var base = !nextItem.done && nextItem.value\n        if (typeof base === 'string') {\n          const element = predicate(\n            filename,\n            uri,\n            base,\n            accumulator.length,\n            next\n          )\n\n          if (typeof element === 'string' && path.isAbsolute(element)) {\n            return Object.assign(accumulator.concat(base), {\n              isFound: true,\n              absolute: element,\n            })\n          } else if (Array.isArray(element)) {\n            return element\n          } else {\n            throw new Error(\n              'predicate must return an absolute path or the result of calling next()'\n            )\n          }\n        } else {\n          return accumulator\n        }\n\n        function next(fallback: any) {\n          return runIterator(\n            Object.assign(\n              accumulator.concat(base),\n              typeof fallback === 'string' && { absolute: fallback }\n            )\n          )\n        }\n      }\n    }\n  }\n\n  function toString() {\n    return '[Function: ' + name + ']'\n  }\n\n  return Object.assign(\n    join,\n    name && {\n      valueOf: toString,\n      toString: toString,\n    }\n  )\n}\n\n/**\n * Format a debug message.\n * Return Formatted message\n */\nfunction createJoinMsg(\n  /** The file being processed by webpack */\n  file: string,\n  /**  A uri path, relative or absolute */\n  uri: string,\n  /** Absolute base paths up to and including the found one */\n  bases: string[],\n  /** Indicates the last base was correct */\n  isFound: boolean\n): string {\n  return [\n    'resolve-url-loader: ' + pathToString(file) + ': ' + uri,\n    //\n    ...bases.map(pathToString).filter(Boolean),\n    ...(isFound ? ['FOUND'] : ['NOT FOUND']),\n  ].join('\\n  ')\n\n  /**\n   * If given path is within `process.cwd()` then show relative posix path, otherwise show absolute posix path.\n   *\n   * Returns A relative or absolute path\n   */\n  function pathToString(\n    /** An absolute path */\n    absolute: string\n  ): string | null {\n    if (!absolute) {\n      return null\n    } else {\n      const relative = path.relative(process.cwd(), absolute).split(path.sep)\n\n      return (\n        relative[0] === '..'\n          ? absolute.split(path.sep)\n          : ['.'].concat(relative).filter(Boolean)\n      ).join('/')\n    }\n  }\n}\n\nexports.createJoinMsg = createJoinMsg\n\n/**\n * A factory for a log function predicated on the given debug parameter.\n *\n * The logging function created accepts a function that formats a message and parameters that the function utilises.\n * Presuming the message function may be expensive we only call it if logging is enabled.\n *\n * The log messages are de-duplicated based on the parameters, so it is assumed they are simple types that stringify\n * well.\n *\n * Returns A logging function possibly degenerate\n */\nfunction createDebugLogger(\n  /** A boolean or debug function */\n  debug: any | boolean\n): any {\n  const log = !!debug && (typeof debug === 'function' ? debug : console.log)\n  const cache: any = {}\n  return log ? actuallyLog : noop\n\n  function noop() {}\n\n  function actuallyLog(msgFn: any, params: any) {\n    const key = JSON.stringify(params)\n    if (!cache[key]) {\n      cache[key] = true\n      log(msgFn.apply(null, params))\n    }\n  }\n}\n\nexports.createDebugLogger = createDebugLogger\n"], "names": ["defaultJoin", "compose", "f", "g", "args", "simpleJoin", "path", "normalize", "join", "createJoinForPredicate", "predicate", "_", "uri", "base", "i", "next", "absolute", "fs", "existsSync", "createIterator", "arr", "name", "filename", "options", "log", "createDebugLogger", "debug", "joinProper", "baseOrIteratorOrAbsent", "iterator", "root", "result", "runIterator", "createJoin<PERSON>g", "isFound", "accumulator", "nextItem", "done", "value", "element", "length", "isAbsolute", "Object", "assign", "concat", "Array", "isArray", "Error", "fallback", "toString", "valueOf", "file", "bases", "pathToString", "map", "filter", "Boolean", "relative", "process", "cwd", "split", "sep", "exports", "console", "cache", "<PERSON><PERSON><PERSON>", "noop", "msgFn", "params", "key", "JSON", "stringify", "apply"], "mappings": "AAAA,uEAAuE;AACvE,0DAA0D,GAC1D;;;;;;;;;;;;;;;;;;;;;;AAsBA;;;;+BAkBaA;;;eAAAA;;;6DAjBI;2DACF;;;;;;AAEf,MAAMC,UACJ,CAACC,GAAQC,IACT,CAAC,GAAGC,OACFF,EAAEC,KAAKC;AAEX,MAAMC,aAAaJ,QAAQK,aAAI,CAACC,SAAS,EAAED,aAAI,CAACE,IAAI;AAS7C,MAAMR,cAAcS,uBAAuB,SAASC,UACzDC,CAAM,EACNC,GAAQ,EACRC,IAAS,EACTC,CAAM,EACNC,IAAS;IAET,MAAMC,WAAWX,WAAWQ,MAAMD;IAClC,OAAOK,WAAE,CAACC,UAAU,CAACF,YAAYA,WAAWD,KAAKD,MAAM,IAAIE,WAAW;AACxE,GAAG;AAEH,UAAUG,eAAeC,GAAQ;IAC/B,KAAK,MAAMN,KAAKM,IAAK;QACnB,MAAMN;IACR;AACF;AAEA;;;;;;;;;;;;;;;;;;;;CAoBC,GACD,SAASL,uBACP,2CAA2C,GAC3CC,SAAc,EACd,kDAAkD,GAClDW,IAAY;IAEZ;;GAEC,GACD,SAASb,KACP,qCAAqC,GACrCc,QAAgB,EAChB,oBAAoB,GACpBC,OAAgD;QAEhD,MAAMC,MAAMC,kBAAkBF,QAAQG,KAAK;QAE3C;;;;;;KAMC,GACD,OAAO,SAASC,WACd,qCAAqC,GACrCf,GAAW,EACX,oDAAoD,GACpDgB,sBAA2B;YAE3B,MAAMC,WACJ,AAAC,OAAOD,2BAA2B,eACjCT,eAAe;gBAACI,QAAQO,IAAI;aAAC,KAC9B,OAAOF,2BAA2B,YACjCT,eAAe;gBAACS;aAAuB,KACzCA;YAEF,MAAMG,SAASC,YAAY,EAAE;YAC7BR,IAAIS,eAAe;gBAACX;gBAAUV;gBAAKmB;gBAAQA,OAAOG,OAAO;aAAC;YAE1D,OAAO,OAAOH,OAAOf,QAAQ,KAAK,WAAWe,OAAOf,QAAQ,GAAGJ;YAE/D,SAASoB,YAAYG,WAAgB;gBACnC,MAAMC,WAAWP,SAASd,IAAI;gBAC9B,IAAIF,OAAO,CAACuB,SAASC,IAAI,IAAID,SAASE,KAAK;gBAC3C,IAAI,OAAOzB,SAAS,UAAU;oBAC5B,MAAM0B,UAAU7B,UACdY,UACAV,KACAC,MACAsB,YAAYK,MAAM,EAClBzB;oBAGF,IAAI,OAAOwB,YAAY,YAAYjC,aAAI,CAACmC,UAAU,CAACF,UAAU;wBAC3D,OAAOG,OAAOC,MAAM,CAACR,YAAYS,MAAM,CAAC/B,OAAO;4BAC7CqB,SAAS;4BACTlB,UAAUuB;wBACZ;oBACF,OAAO,IAAIM,MAAMC,OAAO,CAACP,UAAU;wBACjC,OAAOA;oBACT,OAAO;wBACL,MAAM,qBAEL,CAFK,IAAIQ,MACR,2EADI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF,OAAO;oBACL,OAAOZ;gBACT;gBAEA,SAASpB,KAAKiC,QAAa;oBACzB,OAAOhB,YACLU,OAAOC,MAAM,CACXR,YAAYS,MAAM,CAAC/B,OACnB,OAAOmC,aAAa,YAAY;wBAAEhC,UAAUgC;oBAAS;gBAG3D;YACF;QACF;IACF;IAEA,SAASC;QACP,OAAO,gBAAgB5B,OAAO;IAChC;IAEA,OAAOqB,OAAOC,MAAM,CAClBnC,MACAa,QAAQ;QACN6B,SAASD;QACTA,UAAUA;IACZ;AAEJ;AAEA;;;CAGC,GACD,SAAShB,cACP,wCAAwC,GACxCkB,IAAY,EACZ,sCAAsC,GACtCvC,GAAW,EACX,0DAA0D,GAC1DwC,KAAe,EACf,wCAAwC,GACxClB,OAAgB;IAEhB,OAAO;QACL,yBAAyBmB,aAAaF,QAAQ,OAAOvC;QACrD,EAAE;WACCwC,MAAME,GAAG,CAACD,cAAcE,MAAM,CAACC;WAC9BtB,UAAU;YAAC;SAAQ,GAAG;YAAC;SAAY;KACxC,CAAC1B,IAAI,CAAC;IAEP;;;;GAIC,GACD,SAAS6C,aACP,qBAAqB,GACrBrC,QAAgB;QAEhB,IAAI,CAACA,UAAU;YACb,OAAO;QACT,OAAO;YACL,MAAMyC,WAAWnD,aAAI,CAACmD,QAAQ,CAACC,QAAQC,GAAG,IAAI3C,UAAU4C,KAAK,CAACtD,aAAI,CAACuD,GAAG;YAEtE,OAAO,AACLJ,CAAAA,QAAQ,CAAC,EAAE,KAAK,OACZzC,SAAS4C,KAAK,CAACtD,aAAI,CAACuD,GAAG,IACvB;gBAAC;aAAI,CAACjB,MAAM,CAACa,UAAUF,MAAM,CAACC,QAAO,EACzChD,IAAI,CAAC;QACT;IACF;AACF;AAEAsD,QAAQ7B,aAAa,GAAGA;AAExB;;;;;;;;;;CAUC,GACD,SAASR,kBACP,gCAAgC,GAChCC,KAAoB;IAEpB,MAAMF,MAAM,CAAC,CAACE,SAAU,CAAA,OAAOA,UAAU,aAAaA,QAAQqC,QAAQvC,GAAG,AAAD;IACxE,MAAMwC,QAAa,CAAC;IACpB,OAAOxC,MAAMyC,cAAcC;IAE3B,SAASA,QAAQ;IAEjB,SAASD,YAAYE,KAAU,EAAEC,MAAW;QAC1C,MAAMC,MAAMC,KAAKC,SAAS,CAACH;QAC3B,IAAI,CAACJ,KAAK,CAACK,IAAI,EAAE;YACfL,KAAK,CAACK,IAAI,GAAG;YACb7C,IAAI2C,MAAMK,KAAK,CAAC,MAAMJ;QACxB;IACF;AACF;AAEAN,QAAQrC,iBAAiB,GAAGA", "ignoreList": [0]}