{"version": 3, "sources": ["../../../../src/build/babel/plugins/next-font-unsupported.ts"], "sourcesContent": ["import type { NodePath, types } from 'next/dist/compiled/babel/core'\nimport type { PluginObj } from 'next/dist/compiled/babel/core'\n\nexport default function NextPageDisallowReExportAllExports(): PluginObj<any> {\n  return {\n    visitor: {\n      ImportDeclaration(path: NodePath<types.ImportDeclaration>) {\n        if (\n          [\n            '@next/font/local',\n            '@next/font/google',\n            'next/font/local',\n            'next/font/google',\n          ].includes(path.node.source.value)\n        ) {\n          const err = new SyntaxError(\n            `\"next/font\" requires SWC although Babel is being used due to a custom babel config being present.\\nRead more: https://nextjs.org/docs/messages/babel-font-loader-conflict`\n          )\n          ;(err as any).code = 'BABEL_PARSE_ERROR'\n          ;(err as any).loc =\n            path.node.loc?.start ?? path.node.loc?.end ?? path.node.loc\n          throw err\n        }\n      },\n    },\n  }\n}\n"], "names": ["NextPageDisallowReExportAllExports", "visitor", "ImportDeclaration", "path", "includes", "node", "source", "value", "err", "SyntaxError", "code", "loc", "start", "end"], "mappings": ";;;;+BAGA;;;eAAwBA;;;AAAT,SAASA;IACtB,OAAO;QACLC,SAAS;YACPC,mBAAkBC,IAAuC;gBACvD,IACE;oBACE;oBACA;oBACA;oBACA;iBACD,CAACC,QAAQ,CAACD,KAAKE,IAAI,CAACC,MAAM,CAACC,KAAK,GACjC;wBAMEJ,gBAAwBA;oBAL1B,MAAMK,MAAM,qBAEX,CAFW,IAAIC,YACd,CAAC,yKAAyK,CAAC,GADjK,qBAAA;+BAAA;oCAAA;sCAAA;oBAEZ;oBACED,IAAYE,IAAI,GAAG;oBACnBF,IAAYG,GAAG,GACfR,EAAAA,iBAAAA,KAAKE,IAAI,CAACM,GAAG,qBAAbR,eAAeS,KAAK,OAAIT,kBAAAA,KAAKE,IAAI,CAACM,GAAG,qBAAbR,gBAAeU,GAAG,KAAIV,KAAKE,IAAI,CAACM,GAAG;oBAC7D,MAAMH;gBACR;YACF;QACF;IACF;AACF", "ignoreList": [0]}