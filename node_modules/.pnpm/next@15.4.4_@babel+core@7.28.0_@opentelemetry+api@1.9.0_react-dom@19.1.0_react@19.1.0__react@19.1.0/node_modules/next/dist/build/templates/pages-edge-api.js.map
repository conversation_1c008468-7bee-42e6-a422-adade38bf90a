{"version": 3, "sources": ["../../../src/build/templates/pages-edge-api.ts"], "sourcesContent": ["import type { AdapterOptions } from '../../server/web/adapter'\n\nimport '../../server/web/globals'\n\nimport { adapter } from '../../server/web/adapter'\nimport { IncrementalCache } from '../../server/lib/incremental-cache'\nimport { wrapApi<PERSON>andler } from '../../server/api-utils'\n\n// Import the userland code.\nimport handler from 'VAR_USERLAND'\n\nconst page = 'VAR_DEFINITION_PAGE'\n\nif (typeof handler !== 'function') {\n  throw new Error(\n    `The Edge Function \"pages${page}\" must export a \\`default\\` function`\n  )\n}\n\nexport default function (\n  opts: Omit<AdapterOptions, 'IncrementalCache' | 'page' | 'handler'>\n) {\n  return adapter({\n    ...opts,\n    IncrementalCache,\n    page: 'VAR_DEFINITION_PATHNAME',\n    handler: wrapApi<PERSON>and<PERSON>(page, handler),\n  })\n}\n"], "names": ["page", "handler", "Error", "opts", "adapter", "IncrementalCache", "wrapApiHandler"], "mappings": ";;;;+BAmBA;;;eAAA;;;QAjBO;yBAEiB;kCACS;0BACF;qEAGX;;;;;;AAEpB,MAAMA,OAAO;AAEb,IAAI,OAAOC,qBAAO,KAAK,YAAY;IACjC,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,wBAAwB,EAAEF,KAAK,oCAAoC,CAAC,GADjE,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEe,SAAf,SACEG,IAAmE;IAEnE,OAAOC,IAAAA,gBAAO,EAAC;QACb,GAAGD,IAAI;QACPE,kBAAAA,kCAAgB;QAChBL,MAAM;QACNC,SAASK,IAAAA,wBAAc,EAACN,MAAMC,qBAAO;IACvC;AACF", "ignoreList": [0]}